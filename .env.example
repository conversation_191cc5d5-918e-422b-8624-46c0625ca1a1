# PostgreSQL
POSTGRES_DB=banqroll
POSTGRES_USER=superuser
POSTGRES_PASSWORD=masterKey
DATABASE_URL=**********************************************/banqroll?schema=public

# Redis
REDIS_URL=redis://redis:6379
REDIS_PASSWORD=

# Elasticsearch
ELASTICSEARCH_URL=http://elasticsearch:9200

JWT_SECRET=zEQ6Og0jAheJfeqrFU5zqrFJnro1NAMlq5NSA8nc=
JWT_REFRESH_SECRET=zEQ6Og0jHRieJfeqrFU5zqrFJnIWKNAMlq5NSA8nc=

# Email Configuration
SMTP_HOST=smtp.mandrillapp.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=banqroll
SMTP_PASS=md-0yhMaGUkROMq060LiBfNTw
SMTP_FROM=<EMAIL>

# Frontend URL for password reset links
FRONTEND_URL=http://localhost:3000

# Storage Configuration
# Options: cloudinary, aws, local
STORAGE_PROVIDER=local

# Cloudinary Configuration (when STORAGE_PROVIDER=cloudinary)
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret

# AWS S3 Configuration (when STORAGE_PROVIDER=aws)
AWS_REGION=us-east-1
AWS_S3_BUCKET_NAME=your_bucket_name
AWS_ACCESS_KEY_ID=your_access_key_id
AWS_SECRET_ACCESS_KEY=your_secret_access_key

# Local Storage Configuration (when STORAGE_PROVIDER=local)
LOCAL_STORAGE_PATH=./storage
LOCAL_STORAGE_BASE_URL=http://localhost:3000/files
LOCAL_STORAGE_SECRET=your_local_storage_secret

# Prembly KYC Provider Configuration
PREMBLY_API_KEY=your_prembly_api_key
PREMBLY_APP_ID=your_prembly_app_id
PREMBLY_BASE_URL=https://api.prembly.com/identitypass/verification
PREMBLY_TIMEOUT=30000
PREMBLY_RETRY_ATTEMPTS=2
PREMBLY_RETRY_DELAY=1000
PREMBLY_ENABLE_LOGGING=true
PREMBLY_WEBHOOK_SECRET=your_prembly_webhook_secret

# Zeeh KYC Provider Configuration
ZEEH_API_KEY=your_zeeh_api_key
ZEEH_SECRET_KEY=your_zeeh_secret_key
ZEEH_BASE_URL=https://api.usezeeh.com/v1
ZEEH_TIMEOUT=30000
ZEEH_RETRY_ATTEMPTS=2
ZEEH_RETRY_DELAY=1000
ZEEH_ENABLE_LOGGING=true
ZEEH_WEBHOOK_SECRET=your_zeeh_webhook_secret
