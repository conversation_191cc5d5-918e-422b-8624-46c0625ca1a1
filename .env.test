# Test Environment Configuration
# PostgreSQL Test Database
POSTGRES_DB=banqroll_test
POSTGRES_USER=test_user
POSTGRES_PASSWORD=test_password
DATABASE_URL=postgresql://test_user:test_password@localhost:5432/banqroll_test?schema=public

# Redis Test
REDIS_URL=redis://localhost:6379/1
REDIS_PASSWORD=

# Elasticsearch Test
ELASTICSEARCH_URL=http://localhost:9200

# JWT Secrets for Testing
JWT_SECRET=test-jwt-secret-key-for-testing-only
JWT_REFRESH_SECRET=test-jwt-refresh-secret-key-for-testing-only

# Email Configuration for Testing (Mock)
SMTP_HOST=localhost
SMTP_PORT=1025
SMTP_SECURE=false
SMTP_USER=test
SMTP_PASS=test
SMTP_FROM=<EMAIL>

# Frontend URL for testing
FRONTEND_URL=http://localhost:3000

# Test-specific settings
NODE_ENV=test
