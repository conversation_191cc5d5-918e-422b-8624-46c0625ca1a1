# Comprehensive Audit Logging System

## Overview

The audit logging system provides comprehensive tracking of all user actions and data modifications in the application. It captures detailed information about who performed what action, when, and on which resources, while respecting the application's role-based permission system.

## Features

### 🔍 **Comprehensive Action Tracking**
- **CRUD Operations**: Create, Read, Update, Delete actions on all entities
- **Authentication Events**: Login, logout, password changes
- **Permission Management**: Role assignments, permission grants/revokes
- **Sharing Operations**: Account/wallet sharing and unsharing
- **Resource Access**: Tracking access to sensitive data

### 🔐 **Permission-Based Access Control**
- **Owner Role**: Can view all audit logs for their personal accounts AND all shared accounts they own
- **Manager Role**: Can view audit logs based on their assigned permissions
- **Viewer Role + view_audit permission**: Can view audit logs for accounts/wallets they have access to
- **Shared Users**: Can only view audit logs for accounts/wallets explicitly shared with them

### 📊 **Rich Audit Data**
- **Before/After Values**: Captures state changes for updates
- **Context Information**: IP address, user agent, session ID
- **Resource Linking**: Links to accounts/wallets for permission filtering
- **Metadata**: Additional context and custom information
- **Sensitive Data Protection**: Automatic redaction of passwords and tokens

## Architecture

### Core Components

#### 1. **AuditLog Entity** (`src/core/entities/audit-log.entity.ts`)
```typescript
class AuditLog {
  id: string;
  userId: string;
  action: AuditAction;
  entity: AuditEntity;
  entityId: string;
  accountId?: string;
  walletId?: string;
  beforeValues?: Record<string, any>;
  afterValues?: Record<string, any>;
  metadata?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
  createdAt: Date;
}
```

#### 2. **AuditService** (`src/core/services/audit.service.ts`)
Central service for creating audit logs with methods for:
- `logCreate()` - Log creation actions
- `logUpdate()` - Log update actions with before/after values
- `logDelete()` - Log deletion actions
- `logAuth()` - Log authentication events
- `logSharing()` - Log sharing operations
- `logPermissionChange()` - Log permission changes

#### 3. **AuditRepository** (`src/infrastructure/database/prisma/repositories/audit-log/`)
Repository with advanced querying capabilities:
- Permission-based filtering
- Pagination and sorting
- Statistics and analytics
- Data retention management

#### 4. **Access Control** (`src/core/use-cases/audit/check-audit-access.use-case.ts`)
Enforces permission-based access to audit logs:
- Checks user's audit viewing permissions
- Determines accessible accounts and wallets
- Filters audit logs based on user access

## Database Schema

### Enhanced AuditLog Model
```prisma
model AuditLog {
  id              String      @id @default(uuid())
  userId          String      @map("user_id")
  action          AuditAction
  entity          String
  entityId        String      @map("entity_id")
  accountId       String?     @map("account_id")
  walletId        String?     @map("wallet_id")
  beforeValues    Json?       @map("before_values")
  afterValues     Json?       @map("after_values")
  metadata        Json?
  ipAddress       String?     @map("ip_address")
  userAgent       String?     @map("user_agent")
  sessionId       String?     @map("session_id")
  createdAt       DateTime    @default(now()) @map("created_at")

  user    User     @relation(fields: [userId], references: [id])
  account Account? @relation(fields: [accountId], references: [id])
  wallet  Wallet?  @relation(fields: [walletId], references: [id])

  @@index([userId])
  @@index([accountId])
  @@index([walletId])
  @@index([entity, entityId])
  @@index([action])
  @@index([createdAt])
  @@map("audit_logs")
}

enum AuditAction {
  CREATE
  READ
  UPDATE
  DELETE
  SHARE
  UNSHARE
  LOGIN
  LOGOUT
  PASSWORD_CHANGE
  PERMISSION_GRANT
  PERMISSION_REVOKE
  ROLE_ASSIGN
  ROLE_UNASSIGN
}
```

## API Endpoints

### 🔍 **Get Audit Logs**
```
GET /audit
```
Retrieve audit logs with filtering and pagination.

**Query Parameters:**
- `accountId` - Filter by account ID
- `walletId` - Filter by wallet ID
- `entity` - Filter by entity type
- `action` - Filter by action type
- `entityId` - Filter by specific entity ID
- `targetUserId` - Filter by user who performed the action
- `startDate` - Start date for filtering
- `endDate` - End date for filtering
- `page` - Page number (default: 1)
- `limit` - Items per page (default: 50, max: 100)
- `sortBy` - Sort field (createdAt, action, entity)
- `sortOrder` - Sort order (asc, desc)

### 🏢 **Get Account Audit Logs**
```
GET /audit/accounts/:accountId
```
Retrieve audit logs for a specific account.

### 💰 **Get Wallet Audit Logs**
```
GET /audit/wallets/:walletId
```
Retrieve audit logs for a specific wallet.

### 📊 **Get Entity Audit Logs**
```
GET /audit/entities/:entity/:entityId
```
Retrieve audit logs for a specific entity.

### 👤 **Get User Audit Logs**
```
GET /audit/users/:targetUserId
```
Retrieve audit logs for actions performed by a specific user.

### 📈 **Get Audit Statistics**
```
GET /audit/statistics
```
Retrieve audit log statistics and analytics.

## Usage Examples

### 1. **Manual Audit Logging in Use Cases**

```typescript
// In CreateAccountUseCase
async execute(input: CreateAccountInput): Promise<Account> {
  const account = Account.create({
    userId: input.userId,
    name: input.name,
  });

  const createdAccount = await this.accountRepository.create(account);

  // Log the account creation
  await this.auditService.logCreate(
    input.userId,
    AuditEntity.ACCOUNT,
    createdAccount.id,
    { name: createdAccount.name, userId: createdAccount.userId },
    {
      accountId: createdAccount.id,
      context: input.auditContext,
      metadata: { description: 'Account created' },
    },
  );

  return createdAccount;
}
```

### 2. **Automatic Audit Logging with Decorators**

```typescript
@Controller('accounts')
export class AccountsController {
  
  @Post()
  @AuditCreate(AuditEntity.ACCOUNT, 'id', { 
    captureResponse: true,
    description: 'Account created via API'
  })
  async createAccount(@Body() dto: CreateAccountDto) {
    return this.accountService.create(dto);
  }

  @Put(':id')
  @AuditUpdate(AuditEntity.ACCOUNT, 'id', {
    captureRequest: true,
    captureResponse: true
  })
  async updateAccount(@Param('id') id: string, @Body() dto: UpdateAccountDto) {
    return this.accountService.update(id, dto);
  }
}
```

### 3. **Querying Audit Logs with Permissions**

```typescript
// Get audit logs for accounts the user has access to
const auditLogs = await this.getAuditLogsUseCase.execute({
  userId: currentUser.id,
  filters: {
    entity: AuditEntity.ACCOUNT,
    action: AuditAction.UPDATE,
    startDate: new Date('2024-01-01'),
    endDate: new Date('2024-12-31'),
  },
  pagination: {
    page: 1,
    limit: 50,
    sortBy: 'createdAt',
    sortOrder: 'desc',
  },
});
```

## Permission Matrix

| Role | Personal Accounts | Shared Accounts (Owner) | Shared Accounts (Manager) | Shared Accounts (Viewer) |
|------|------------------|-------------------------|---------------------------|--------------------------|
| **Owner** | ✅ All logs | ✅ All logs | ❌ No access | ❌ No access |
| **Manager** | ✅ All logs | ❌ No access | ✅ Based on permissions | ❌ No access |
| **Viewer + view_audit** | ✅ All logs | ❌ No access | ❌ No access | ✅ Read-only logs |

## Security Features

### 🔒 **Data Protection**
- Automatic redaction of sensitive fields (passwords, tokens, etc.)
- Configurable sensitive field lists
- Safe serialization of complex objects

### 🛡️ **Access Control**
- Permission-based filtering at the repository level
- Resource-level access validation
- Role hierarchy enforcement

### 📝 **Audit Trail Integrity**
- Immutable audit logs (no updates or deletes)
- Comprehensive indexing for performance
- Data retention policies

## Configuration

### Environment Variables
```env
# Audit log retention (days)
AUDIT_LOG_RETENTION_DAYS=2555  # 7 years for compliance

# Enable/disable audit logging
AUDIT_LOGGING_ENABLED=true

# Audit log level (ALL, SENSITIVE_ONLY, NONE)
AUDIT_LOG_LEVEL=ALL
```

### Default Permissions
The system automatically includes `view_audit_logs` permission in:
- **Owner role**: Full access
- **Manager role**: Based on resource permissions
- **Viewer role**: Conditional access based on shared resources

## Performance Considerations

### 📊 **Database Optimization**
- Comprehensive indexing strategy
- Partitioning by date for large datasets
- Efficient query patterns for permission filtering

### 🚀 **Async Processing**
- Non-blocking audit log creation
- Background processing for statistics
- Batch operations for bulk actions

### 💾 **Storage Management**
- Configurable data retention policies
- Archive old audit logs to cold storage
- Compression for historical data

## Compliance & Legal

### 📋 **Regulatory Compliance**
- **GDPR**: User data access and deletion tracking
- **SOX**: Financial data modification audit trails
- **HIPAA**: Healthcare data access logging (if applicable)
- **PCI DSS**: Payment data handling audit trails

### 🗃️ **Data Retention**
- Default 7-year retention for compliance
- Configurable retention policies per entity type
- Secure deletion after retention period

## Monitoring & Alerting

### 🚨 **Security Alerts**
- Failed access attempts to audit logs
- Unusual audit log access patterns
- Bulk data access events
- Permission escalation attempts

### 📈 **Analytics**
- User activity patterns
- Resource access frequency
- Permission usage statistics
- Compliance reporting

## Migration Guide

### Database Migration
```bash
# Run the enhanced audit logging migration
npx prisma migrate deploy
```

### Code Integration
1. Update use cases to include audit logging
2. Add audit decorators to controllers
3. Configure permission-based access
4. Set up monitoring and alerting

## Best Practices

### 🎯 **Implementation Guidelines**
1. **Always log sensitive operations** (CREATE, UPDATE, DELETE)
2. **Include context information** (IP, user agent, session)
3. **Use appropriate audit levels** (don't over-audit read operations)
4. **Sanitize sensitive data** before logging
5. **Implement proper error handling** for audit failures

### 🔧 **Performance Tips**
1. **Use async audit logging** to avoid blocking operations
2. **Batch audit logs** for bulk operations
3. **Index frequently queried fields**
4. **Archive old logs** regularly
5. **Monitor audit log growth** and performance impact

## Troubleshooting

### Common Issues
1. **Permission denied errors**: Check user's `view_audit_logs` permission
2. **Missing audit logs**: Verify audit service is properly injected
3. **Performance issues**: Check database indexes and query patterns
4. **Storage growth**: Implement data retention policies

### Debug Mode
Enable detailed audit logging for troubleshooting:
```typescript
// In development environment
AUDIT_LOG_LEVEL=DEBUG
AUDIT_INCLUDE_STACK_TRACES=true
```

## Future Enhancements

### 🚀 **Planned Features**
- Real-time audit log streaming
- Advanced analytics dashboard
- Machine learning for anomaly detection
- Integration with external SIEM systems
- Blockchain-based audit trail verification
