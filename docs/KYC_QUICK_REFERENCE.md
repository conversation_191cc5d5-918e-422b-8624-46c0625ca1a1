# KYC System Quick Reference

## 🚀 Quick Start

### 1. Environment Setup
```bash
# Copy environment template
cp .env.example .env

# Add your API keys
ZEEH_API_KEY=pk_your_api_key
ZEEH_SECRET_KEY=pv_your_secret_key
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret

# Setup database
npm run migrate
```

### 2. Test Configuration
```bash
# Test Zeeh API
node test-zeeh-builtin.js

# Test BVN verification
node test-bvn-simple.js
```

### 3. Start Application
```bash
npm run start:dev
```

## 📋 Supported Documents by Country

| Country | Code | BVN | NIN | National ID | Drivers License | Passport | Voters Card | CAC | TIN |
|---------|------|-----|-----|-------------|-----------------|----------|-------------|-----|-----|
| Nigeria | 234  | ✅  | ✅  | ❌          | ✅              | ✅       | ✅          | ✅  | ✅  |
| Ghana   | 233  | ❌  | ❌  | ✅          | ✅              | ✅       | ✅          | ❌  | ✅  |
| Kenya   | 254  | ❌  | ❌  | ✅          | ✅              | ✅       | ❌          | ❌  | ✅  |

## 🔧 Provider Configuration

### Zeeh (Primary for Nigeria)
```bash
ZEEH_API_KEY=pk_your_api_key
ZEEH_SECRET_KEY=pv_your_secret_key
ZEEH_BASE_URL=https://api.usezeeh.com/v1
```

### Prembly (Fallback)
```bash
PREMBLY_API_KEY=your_api_key
PREMBLY_SECRET_KEY=your_secret_key
PREMBLY_BASE_URL=https://api.prembly.com/v1
```

## 🛣️ API Endpoints

### BVN Verification
```bash
POST /api/kyc/verify/bvn
{
  "bvn": "12345678901",
  "firstName": "John",
  "lastName": "Doe",
  "dateOfBirth": "1990-01-01",
  "phoneNumber": "08123456789"
}
```

### NIN Verification
```bash
POST /api/kyc/verify/nin
{
  "nin": "12345678901",
  "firstName": "Jane",
  "lastName": "Smith",
  "dateOfBirth": "1985-05-15"
}
```

### CAC Verification
```bash
POST /api/kyc/verify/cac
{
  "rcNumber": "RC123456",
  "companyName": "Test Company Ltd"
}
```

### TIN Verification
```bash
POST /api/kyc/verify/tin
{
  "tin": "12345678-0001"
}
```

## 🔄 Adding New Provider Checklist

- [ ] Create provider class implementing `KycProviderPort`
- [ ] Add to `KYC_PROVIDERS` constant
- [ ] Update `KycProviderFactory`
- [ ] Add environment variables
- [ ] Update country configuration
- [ ] Create tests
- [ ] Update documentation

## 🌍 Adding New Country Checklist

- [ ] Add country code to provider `supportedCountries`
- [ ] Add country endpoints in `getCountryEndpoints()`
- [ ] Add supported documents in `getCountrySupportedDocuments()`
- [ ] Add payload builder method
- [ ] Add data extraction method
- [ ] Create country configuration
- [ ] Add validation rules
- [ ] Create tests

## 🛣️ Adding New Route Checklist

- [ ] Create use case class
- [ ] Create DTO with validation
- [ ] Add controller method
- [ ] Update module providers/exports
- [ ] Add API documentation
- [ ] Create tests
- [ ] Update documentation

## 🐛 Common Issues & Solutions

### 401 Authentication Error
```bash
# Check API keys
echo $ZEEH_API_KEY
echo $ZEEH_SECRET_KEY

# Test connectivity
node test-zeeh-builtin.js
```

### 400 Bad Request
- Check document number format
- Verify required fields
- Validate country code

### Provider Failover Not Working
- Check country configuration
- Verify fallback providers
- Check provider health

### Database Issues
```bash
# Reset database
npm run prisma:reset

# Check connection
npx prisma db pull
```

## 📊 Monitoring Commands

### Check Provider Health
```bash
# Test all providers
node -e "
const providers = ['zeeh', 'prembly'];
providers.forEach(async (p) => {
  const provider = factory.create(p);
  const health = await provider.checkHealth();
  console.log(\`\${p}: \${health.isHealthy ? '✅' : '❌'}\`);
});
"
```

### View Verification Logs
```bash
# Enable debug logging
export LOG_LEVEL=debug
export ZEEH_ENABLE_LOGGING=true

# Check logs
tail -f logs/application.log | grep KYC
```

## 🧪 Test Data

### Valid Test BVNs (Zeeh)
- `22222222222` - Returns sample user data
- `11111111111` - May fail (test various scenarios)

### Valid Test NINs (Zeeh)
- `12345678901` - Returns sample user data

### Test Company Data (CAC)
- RC Number: `RC123456`
- Company Name: `Test Company Ltd`

## 📈 Performance Optimization

### Response Time Targets
- BVN: < 3 seconds
- NIN: < 5 seconds
- CAC: < 10 seconds
- TIN: < 5 seconds

### Caching Strategy
```typescript
// Cache successful verifications for 24 hours
const cacheKey = `kyc:${userId}:${documentType}:${documentNumber}`;
await redis.setex(cacheKey, 86400, JSON.stringify(result));
```

### Rate Limiting
```typescript
// Implement rate limiting per user
@Throttle(10, 60) // 10 requests per minute
@Post('verify/bvn')
async verifyBvn() { ... }
```

## 🔒 Security Best Practices

### Data Protection
- Never log sensitive document numbers
- Encrypt stored verification data
- Use secure headers for API requests
- Implement request signing for webhooks

### Access Control
- Require authentication for all endpoints
- Implement role-based permissions
- Log all verification attempts
- Monitor for suspicious patterns

### Compliance
- Store audit logs for 7 years
- Implement data retention policies
- Provide user data export/deletion
- Follow GDPR/local privacy laws

## 📚 Additional Resources

- [Full Documentation](./KYC_SYSTEM_DOCUMENTATION.md)
- [Zeeh API Docs](https://zeehdocs.zeeh.africa/)
- [Prembly API Docs](https://docs.prembly.com/)
- [Cloudinary Docs](https://cloudinary.com/documentation)

## 🆘 Support

### Internal Support
- Check logs: `logs/application.log`
- Run diagnostics: `npm run test:kyc`
- Review metrics: `npm run metrics:kyc`

### External Support
- Zeeh: <EMAIL>
- Prembly: <EMAIL>
- Cloudinary: <EMAIL>

---

**Last Updated:** $(date)
**Version:** 1.0.0
