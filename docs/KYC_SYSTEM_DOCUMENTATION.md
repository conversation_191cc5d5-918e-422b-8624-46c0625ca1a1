# KYC System Documentation

## Table of Contents
1. [System Overview](#system-overview)
2. [Architecture](#architecture)
3. [Configuration Setup](#configuration-setup)
4. [Adding New Providers](#adding-new-providers)
5. [Adding New Countries](#adding-new-countries)
6. [Adding New KYC Routes](#adding-new-kyc-routes)
7. [BVN Route Example](#bvn-route-example)
8. [Testing](#testing)
9. [Troubleshooting](#troubleshooting)

## System Overview

The KYC (Know Your Customer) system is a flexible, multi-provider, multi-country verification platform that supports various document types including BVN, NIN, CAC, TIN, Drivers License, International Passport, and more.

### Key Features
- **Multi-Provider Support**: Zeeh, Prembly, and extensible for more
- **Multi-Country Support**: Nigeria, Ghana, Kenya, and extensible
- **Provider Failover**: Automatic fallback to secondary providers
- **Document Storage**: Cloudinary, AWS S3, or Local storage
- **Type Safety**: Full TypeScript support
- **Audit Logging**: Complete verification tracking

### Supported Document Types
```typescript
enum DocumentType {
  BVN = 'BVN',                    // Nigeria only
  NIN = 'NIN',                    // Nigeria only
  NATIONAL_ID = 'NATIONAL_ID',    // Kenya, Ghana
  DRIVERS_LICENSE = 'DRIVERS_LICENSE',
  INTERNATIONAL_PASSPORT = 'INTERNATIONAL_PASSPORT',
  VOTERS_CARD = 'VOTERS_CARD',
  CAC = 'CAC',                    // Nigeria only
  TIN = 'TIN',
  FACIAL_IMAGE = 'FACIAL_IMAGE',
  SELFIE = 'SELFIE',
}
```

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Controllers   │    │   Use Cases     │    │   Entities      │
│                 │    │                 │    │                 │
│ • BVN Route     │───▶│ • VerifyBvnUC   │───▶│ • KycVerification│
│ • NIN Route     │    │ • VerifyNinUC   │    │ • User          │
│ • CAC Route     │    │ • VerifyCacUC   │    │ • DocumentType  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Provider Manager│    │ Country Config  │    │  Repositories   │
│                 │    │                 │    │                 │
│ • Failover      │    │ • Validation    │    │ • Verification  │
│ • Load Balance  │    │ • Provider Map  │    │ • User          │
│ • Retry Logic   │    │ • Document Rules│    │ • Audit Log     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Providers     │    │ Storage Service │    │   Database      │
│                 │    │                 │    │                 │
│ • Zeeh          │    │ • Cloudinary    │    │ • PostgreSQL    │
│ • Prembly       │    │ • AWS S3        │    │ • Prisma ORM    │
│ • Custom        │    │ • Local Storage │    │ • Redis Cache   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Configuration Setup

### 1. Environment Variables

Create or update your `.env` file:

```bash
# Database
DATABASE_URL="postgresql://user:password@localhost:5432/banqroll"

# Redis
REDIS_URL="redis://localhost:6379"

# Storage Provider (cloudinary, aws, local)
STORAGE_PROVIDER=cloudinary

# Cloudinary Configuration
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret

# AWS S3 Configuration (if using AWS)
AWS_REGION=us-east-1
AWS_S3_BUCKET_NAME=your_bucket_name
AWS_ACCESS_KEY_ID=your_access_key_id
AWS_SECRET_ACCESS_KEY=your_secret_access_key

# Zeeh KYC Provider
ZEEH_API_KEY=pk_your_api_key
ZEEH_SECRET_KEY=pv_your_secret_key
ZEEH_BASE_URL=https://api.usezeeh.com/v1
ZEEH_TIMEOUT=30000
ZEEH_RETRY_ATTEMPTS=2
ZEEH_ENABLE_LOGGING=true

# Prembly KYC Provider
PREMBLY_API_KEY=your_prembly_api_key
PREMBLY_SECRET_KEY=your_prembly_secret_key
PREMBLY_BASE_URL=https://api.prembly.com/v1
PREMBLY_TIMEOUT=30000
PREMBLY_RETRY_ATTEMPTS=2
PREMBLY_ENABLE_LOGGING=true

# Email Configuration
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USER=<EMAIL>
MAIL_PASS=your_app_password
MAIL_FROM=<EMAIL>

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key
JWT_EXPIRES_IN=7d

# Frontend URL
FRONTEND_URL=http://localhost:3000
```

### 2. Database Setup

Run database migrations:

```bash
# Reset and migrate database
npm run prisma:reset

# Or just migrate
npm run migrate

# Generate Prisma client
npx prisma generate
```

### 3. Install Dependencies

```bash
# Install required packages
npm install

# Install KYC-specific dependencies
npm install @aws-sdk/client-s3 @aws-sdk/s3-request-presigner cloudinary axios
```

## Adding New Providers

### Step 1: Create Provider Interface Implementation

Create a new provider file: `src/infrastructure/kyc/providers/new-provider-kyc.provider.ts`

```typescript
import { Injectable, Logger } from '@nestjs/common';
import { AxiosInstance } from 'axios';
import {
  KycProviderPort,
  VerificationRequest,
  VerificationResult,
  BvnVerificationRequest,
  ProviderHealthStatus,
} from '../../../core/ports/kyc/kyc-provider.port';
import { DocumentType } from '../../../core/entities/kyc-verification.entity';
import { KYC_PROVIDERS } from '../kyc-provider-factory';

@Injectable()
export class NewProviderKycProvider implements KycProviderPort {
  readonly providerId = 'new-provider'; // Add to KYC_PROVIDERS constant
  readonly supportedCountries = ['234']; // Nigeria
  readonly supportedDocuments = [
    DocumentType.BVN,
    DocumentType.NIN,
    // Add supported document types
  ];

  private readonly logger = new Logger(NewProviderKycProvider.name);
  private readonly httpClient: AxiosInstance;

  constructor(private readonly config: ProviderConfiguration) {
    this.httpClient = axios.create({
      baseURL: config.credentials.baseUrl,
      timeout: config.timeout || 30000,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.credentials.apiKey}`,
        'X-API-Key': config.credentials.secretKey,
      },
    });
  }

  async verify(request: VerificationRequest, countryCode: string = '234'): Promise<VerificationResult> {
    // Implement verification logic
    switch (request.documentType) {
      case DocumentType.BVN:
        return this.verifyBvn(request as BvnVerificationRequest);
      default:
        throw new Error(`Document type ${request.documentType} not supported`);
    }
  }

  async verifyBvn(request: BvnVerificationRequest): Promise<VerificationResult> {
    try {
      const payload = {
        bvn: request.documentNumber,
        first_name: request.firstName,
        last_name: request.lastName,
        // Map to provider's expected format
      };

      const response = await this.httpClient.post('/verify/bvn', payload);
      return this.parseResponse(response.data, request.documentType);
    } catch (error) {
      return this.handleError(error, request.documentType);
    }
  }

  async checkHealth(): Promise<ProviderHealthStatus> {
    try {
      const startTime = Date.now();
      const response = await this.httpClient.get('/health');
      const responseTime = Date.now() - startTime;

      return {
        isHealthy: response.status === 200,
        responseTime,
        lastChecked: new Date(),
      };
    } catch (error) {
      return {
        isHealthy: false,
        lastChecked: new Date(),
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  private parseResponse(response: any, documentType: DocumentType): VerificationResult {
    // Implement response parsing logic
    return {
      success: response.success,
      verified: response.verified,
      confidence: response.confidence || 0.9,
      providerReference: response.reference,
      providerResponse: response,
      extractedData: this.extractData(response.data, documentType),
    };
  }

  private extractData(data: any, documentType: DocumentType): Record<string, any> | undefined {
    // Extract and normalize data based on document type
    const extracted: Record<string, any> = {};
    
    if (data.first_name) extracted.firstName = data.first_name;
    if (data.last_name) extracted.lastName = data.last_name;
    // Add more field mappings
    
    return Object.keys(extracted).length > 0 ? extracted : undefined;
  }

  private handleError(error: any, documentType: DocumentType): VerificationResult {
    const errorMessage = error.response?.data?.message || error.message || 'Unknown error';
    
    return {
      success: false,
      verified: false,
      confidence: 0,
      providerReference: '',
      providerResponse: error.response?.data,
      failureReason: errorMessage,
    };
  }
}
```

### Step 2: Update Provider Factory

Add your provider to `src/infrastructure/kyc/kyc-provider-factory.ts`:

```typescript
// Add to KYC_PROVIDERS constant
export const KYC_PROVIDERS = {
  PREMBLY: 'prembly',
  ZEEH: 'zeeh',
  NEW_PROVIDER: 'new-provider', // Add this
} as const;

// Import your provider
import { NewProviderKycProvider } from './providers/new-provider-kyc.provider';

// Add configuration
private getProviderConfigurations(): Record<string, ProviderConfiguration> {
  return {
    // ... existing providers
    [KYC_PROVIDERS.NEW_PROVIDER]: {
      credentials: {
        apiKey: process.env.NEW_PROVIDER_API_KEY || '',
        secretKey: process.env.NEW_PROVIDER_SECRET_KEY || '',
        baseUrl: process.env.NEW_PROVIDER_BASE_URL || '',
      },
      timeout: parseInt(process.env.NEW_PROVIDER_TIMEOUT || '30000'),
      retryAttempts: parseInt(process.env.NEW_PROVIDER_RETRY_ATTEMPTS || '2'),
      enableLogging: process.env.NEW_PROVIDER_ENABLE_LOGGING === 'true',
    },
  };
}

// Add to factory method
create(providerId: string): KycProviderPort {
  const configuration = this.configurations[providerId];
  
  switch (providerId) {
    // ... existing cases
    case KYC_PROVIDERS.NEW_PROVIDER:
      return new NewProviderKycProvider(configuration);
    default:
      throw new Error(`Unknown KYC provider: ${providerId}`);
  }
}
```

### Step 3: Update Country Configuration

Add your provider to country configs in `src/infrastructure/kyc/country-kyc-config.service.ts`:

```typescript
[DocumentType.BVN]: {
  primary: KYC_PROVIDERS.NEW_PROVIDER, // Set as primary
  fallback: [KYC_PROVIDERS.ZEEH, KYC_PROVIDERS.PREMBLY], // Add as fallback
  config: {
    endpoint: '/bvn',
    requiresImage: false,
    timeout: 30000,
    retryAttempts: 2
  }
},
```

### Step 4: Add Environment Variables

Add to your `.env` file:

```bash
# New Provider Configuration
NEW_PROVIDER_API_KEY=your_api_key
NEW_PROVIDER_SECRET_KEY=your_secret_key
NEW_PROVIDER_BASE_URL=https://api.newprovider.com/v1
NEW_PROVIDER_TIMEOUT=30000
NEW_PROVIDER_RETRY_ATTEMPTS=2
NEW_PROVIDER_ENABLE_LOGGING=true
```

### Step 5: Create Tests

Create `src/infrastructure/kyc/providers/__tests__/new-provider-kyc.provider.test.ts`:

```typescript
import { NewProviderKycProvider } from '../new-provider-kyc.provider';
import { DocumentType } from '../../../../core/entities/kyc-verification.entity';
import { BvnVerificationRequest } from '../../../../core/ports/kyc/kyc-provider.port';

describe('NewProviderKycProvider', () => {
  let provider: NewProviderKycProvider;

  beforeEach(() => {
    const mockConfig = {
      credentials: {
        apiKey: 'test-api-key',
        secretKey: 'test-secret-key',
        baseUrl: 'https://api.newprovider.com/v1',
      },
      timeout: 30000,
      retryAttempts: 2,
      enableLogging: false,
    };

    provider = new NewProviderKycProvider(mockConfig);
  });

  it('should verify BVN successfully', async () => {
    // Mock axios response
    const mockResponse = {
      data: {
        success: true,
        verified: true,
        data: {
          bvn: '***********',
          first_name: 'John',
          last_name: 'Doe',
        },
      },
    };

    // Mock the HTTP client
    jest.spyOn(provider['httpClient'], 'post').mockResolvedValue(mockResponse);

    const request: BvnVerificationRequest = {
      documentType: DocumentType.BVN,
      documentNumber: '***********',
      firstName: 'John',
      lastName: 'Doe',
    };

    const result = await provider.verifyBvn(request);

    expect(result.success).toBe(true);
    expect(result.verified).toBe(true);
    expect(result.extractedData?.firstName).toBe('John');
  });
});
```

## Adding New Countries

### Step 1: Update Provider Support

Add country support to existing providers in `src/infrastructure/kyc/providers/zeeh-kyc.provider.ts`:

```typescript
export class ZeehKycProvider implements KycProviderPort {
  readonly supportedCountries = ['234', '233', '254', '256']; // Add Uganda (256)

  private getCountrySupportedDocuments(countryCode: string): DocumentType[] {
    switch (countryCode) {
      // ... existing countries
      case '256': // Uganda
        return [
          DocumentType.NATIONAL_ID,
          DocumentType.DRIVERS_LICENSE,
          DocumentType.INTERNATIONAL_PASSPORT,
          DocumentType.TIN,
          DocumentType.FACIAL_IMAGE,
          DocumentType.SELFIE,
        ];
      default:
        return [];
    }
  }

  private getCountryEndpoints(countryCode: string): Record<DocumentType, string> {
    switch (countryCode) {
      // ... existing countries
      case '256': // Uganda
        return {
          [DocumentType.NATIONAL_ID]: '/uganda_kyc/national_id',
          [DocumentType.DRIVERS_LICENSE]: '/uganda_kyc/drivers_license',
          [DocumentType.INTERNATIONAL_PASSPORT]: '/uganda_kyc/passport',
          [DocumentType.TIN]: '/uganda_kyc/tin',
          [DocumentType.FACIAL_IMAGE]: '/uganda_kyc/facial_verification',
          [DocumentType.SELFIE]: '/uganda_kyc/selfie_verification',
        } as Record<DocumentType, string>;
      default:
        throw new Error(`Country ${countryCode} not supported by Zeeh`);
    }
  }

  private buildUgandaPayload(request: VerificationRequest): Record<string, any> {
    switch (request.documentType) {
      case DocumentType.NATIONAL_ID:
        const nationalIdReq = request as NationalIdVerificationRequest;
        return {
          national_id: nationalIdReq.documentNumber,
          first_name: nationalIdReq.firstName,
          last_name: nationalIdReq.lastName,
          date_of_birth: nationalIdReq.dateOfBirth,
        };
      // Add other document types
      default:
        throw new Error(`Document type ${request.documentType} not supported for Uganda`);
    }
  }

  private extractUgandaData(data: any, documentType: DocumentType, extracted: Record<string, any>): Record<string, any> | undefined {
    switch (documentType) {
      case DocumentType.NATIONAL_ID:
        if (data.national_id) extracted.nationalId = data.national_id;
        if (data.place_of_birth) extracted.placeOfBirth = data.place_of_birth;
        break;
      // Add other document types
    }
    return Object.keys(extracted).length > 0 ? extracted : undefined;
  }
}
```

### Step 2: Add Country Configuration

Add Uganda config to `src/infrastructure/kyc/country-kyc-config.service.ts`:

```typescript
private initializeConfigs(): void {
  // ... existing countries

  // Uganda Configuration
  const ugandaConfig: CountryKycConfig = {
    countryCode: '256',
    countryName: 'Uganda',
    supportedDocuments: [
      DocumentType.NATIONAL_ID,
      DocumentType.DRIVERS_LICENSE,
      DocumentType.INTERNATIONAL_PASSPORT,
      DocumentType.TIN,
      DocumentType.FACIAL_IMAGE,
      DocumentType.SELFIE,
    ],
    providers: {
      [DocumentType.NATIONAL_ID]: {
        primary: KYC_PROVIDERS.ZEEH,
        fallback: [KYC_PROVIDERS.PREMBLY],
        config: {
          endpoint: '/national-id',
          requiresImage: true,
          timeout: 30000,
          retryAttempts: 2,
        },
      },
      [DocumentType.TIN]: {
        primary: KYC_PROVIDERS.ZEEH,
        fallback: [],
        config: {
          endpoint: '/tin',
          requiresImage: false,
          timeout: 30000,
          retryAttempts: 2,
        },
      },
    },
    validationRules: {
      [DocumentType.NATIONAL_ID]: {
        format: /^[A-Z]{2}[0-9]{14}$/,
        required: true,
        requiresImage: true,
        description: 'Uganda National ID (2 letters followed by 14 digits)',
      },
      [DocumentType.TIN]: {
        format: /^[0-9]{10}$/,
        required: true,
        requiresImage: false,
        description: 'Uganda TIN (10 digits)',
      },
    },
    kycRequirements: {
      minimumIdentityDocuments: 1,
      minimumAddressDocuments: 0,
      minimumBusinessDocuments: 0,
      facialVerificationRequired: true,
      documentExpiryMonths: 24,
    },
  };

  this.configs.set('256', ugandaConfig);
}
```

### Step 3: Create Country-Specific Use Cases (Optional)

Create `src/core/use-cases/kyc/verify-uganda-national-id.use-case.ts`:

```typescript
import { Injectable, Inject, Logger } from '@nestjs/common';
import { KycVerification, DocumentType, VerificationType } from '../../entities/kyc-verification.entity';
import {
  KYC_VERIFICATION_REPOSITORY,
  KycVerificationRepositoryPort
} from '../../ports/kyc/kyc-verification-repository.port';
import { NationalIdVerificationRequest } from '../../ports/kyc/kyc-provider.port';
import { KycProviderManager } from '../../../infrastructure/kyc/kyc-provider-manager';

export interface VerifyUgandaNationalIdInput {
  userId: string;
  nationalId: string;
  firstName?: string;
  lastName?: string;
  dateOfBirth?: string;
  countryCode?: string;
}

@Injectable()
export class VerifyUgandaNationalIdUseCase {
  private readonly logger = new Logger(VerifyUgandaNationalIdUseCase.name);

  constructor(
    @Inject(KYC_VERIFICATION_REPOSITORY)
    private readonly verificationRepository: KycVerificationRepositoryPort,
    private readonly providerManager: KycProviderManager,
  ) {}

  async execute(input: VerifyUgandaNationalIdInput) {
    const countryCode = input.countryCode || '256'; // Uganda

    const verificationRequest: NationalIdVerificationRequest = {
      documentType: DocumentType.NATIONAL_ID,
      documentNumber: input.nationalId,
      firstName: input.firstName,
      lastName: input.lastName,
      dateOfBirth: input.dateOfBirth,
    };

    const { result, log } = await this.providerManager.verifyDocument(
      countryCode,
      DocumentType.NATIONAL_ID,
      verificationRequest
    );

    // Create and save verification entity
    const verification = KycVerification.create({
      userId: input.userId,
      verificationType: VerificationType.IDENTITY,
      documentType: DocumentType.NATIONAL_ID,
      countryCode,
      providerId: log.attempts[log.attempts.length - 1]?.providerId || 'unknown',
      providerReference: result.providerReference,
      documentNumber: input.nationalId,
      verificationData: result.extractedData,
    });

    // Update status based on result
    let finalVerification: KycVerification;
    if (result.success && result.verified) {
      finalVerification = verification.markAsVerified(result.providerResponse, result.extractedData);
    } else {
      finalVerification = verification.markAsFailed(
        result.failureReason || 'Verification failed',
        result.providerResponse
      );
    }

    const savedVerification = await this.verificationRepository.save(finalVerification);

    return {
      verification: savedVerification,
      isVerified: savedVerification.isVerified,
      extractedData: savedVerification.verificationData,
      providerUsed: savedVerification.providerId,
    };
  }
}
```

## Adding New KYC Routes

### Step 1: Create Use Case

First, create the use case (if not already exists). For example, `src/core/use-cases/kyc/verify-drivers-license.use-case.ts`:

```typescript
import { Injectable, Inject, Logger } from '@nestjs/common';
import { KycVerification, DocumentType, VerificationType } from '../../entities/kyc-verification.entity';
import {
  KYC_VERIFICATION_REPOSITORY,
  KycVerificationRepositoryPort
} from '../../ports/kyc/kyc-verification-repository.port';
import { DriversLicenseVerificationRequest } from '../../ports/kyc/kyc-provider.port';
import { KycProviderManager } from '../../../infrastructure/kyc/kyc-provider-manager';
import { CountryKycConfigService } from '../../../infrastructure/kyc/country-kyc-config.service';

export interface VerifyDriversLicenseInput {
  userId: string;
  licenseNumber: string;
  firstName?: string;
  lastName?: string;
  dateOfBirth?: string;
  documentImageUrl?: string;
  countryCode?: string;
}

export interface VerifyDriversLicenseOutput {
  verification: KycVerification;
  isVerified: boolean;
  extractedData?: Record<string, any>;
  providerUsed: string;
  responseTime: number;
}

@Injectable()
export class VerifyDriversLicenseUseCase {
  private readonly logger = new Logger(VerifyDriversLicenseUseCase.name);

  constructor(
    @Inject(KYC_VERIFICATION_REPOSITORY)
    private readonly verificationRepository: KycVerificationRepositoryPort,
    private readonly providerManager: KycProviderManager,
    private readonly countryConfigService: CountryKycConfigService,
  ) {}

  async execute(input: VerifyDriversLicenseInput): Promise<VerifyDriversLicenseOutput> {
    const startTime = Date.now();
    const countryCode = input.countryCode || '234'; // Default to Nigeria

    this.logger.log(`Starting Drivers License verification for user ${input.userId}`, {
      userId: input.userId,
      countryCode,
      licenseNumber: input.licenseNumber.substring(0, 4) + '****',
    });

    try {
      // Validate input
      this.validateInput(input, countryCode);

      // Check for existing verification
      const existingVerification = await this.verificationRepository.getUserLatestVerification(
        input.userId,
        DocumentType.DRIVERS_LICENSE
      );

      if (existingVerification?.isVerified &&
          !existingVerification.isExpired &&
          existingVerification.documentNumber === input.licenseNumber) {
        return {
          verification: existingVerification,
          isVerified: true,
          extractedData: existingVerification.verificationData,
          providerUsed: existingVerification.providerId,
          responseTime: Date.now() - startTime,
        };
      }

      // Prepare verification request
      const verificationRequest: DriversLicenseVerificationRequest = {
        documentType: DocumentType.DRIVERS_LICENSE,
        documentNumber: input.licenseNumber,
        firstName: input.firstName,
        lastName: input.lastName,
        dateOfBirth: input.dateOfBirth,
        documentImageUrl: input.documentImageUrl,
      };

      // Perform verification
      const { result, log } = await this.providerManager.verifyDocument(
        countryCode,
        DocumentType.DRIVERS_LICENSE,
        verificationRequest
      );

      // Create verification entity
      const verification = KycVerification.create({
        userId: input.userId,
        verificationType: VerificationType.IDENTITY,
        documentType: DocumentType.DRIVERS_LICENSE,
        countryCode,
        providerId: log.attempts[log.attempts.length - 1]?.providerId || 'unknown',
        providerReference: result.providerReference,
        documentNumber: input.licenseNumber,
        verificationData: result.extractedData,
      });

      // Update verification status
      let finalVerification: KycVerification;
      if (result.success && result.verified) {
        finalVerification = verification.markAsVerified(result.providerResponse, result.extractedData);
      } else if (result.requiresManualReview) {
        finalVerification = verification.markForManualReview(
          result.failureReason || 'Verification requires manual review'
        );
      } else {
        finalVerification = verification.markAsFailed(
          result.failureReason || 'Verification failed',
          result.providerResponse
        );
      }

      const savedVerification = await this.verificationRepository.save(finalVerification);

      return {
        verification: savedVerification,
        isVerified: savedVerification.isVerified,
        extractedData: savedVerification.verificationData,
        providerUsed: savedVerification.providerId,
        responseTime: Date.now() - startTime,
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Drivers License verification failed for user ${input.userId}`, {
        error: errorMessage,
      });
      throw error;
    }
  }

  private validateInput(input: VerifyDriversLicenseInput, countryCode: string): void {
    if (!input.userId || input.userId.trim().length === 0) {
      throw new Error('User ID is required');
    }

    if (!input.licenseNumber || input.licenseNumber.trim().length === 0) {
      throw new Error('License number is required');
    }

    // Validate license number format
    const isValid = this.countryConfigService.validateDocumentNumber(
      countryCode,
      DocumentType.DRIVERS_LICENSE,
      input.licenseNumber
    );

    if (!isValid) {
      const rule = this.countryConfigService.getValidationRule(countryCode, DocumentType.DRIVERS_LICENSE);
      throw new Error(`Invalid license number format: ${rule.description}`);
    }

    // Check if drivers license verification is supported
    if (!this.countryConfigService.isDocumentSupported(countryCode, DocumentType.DRIVERS_LICENSE)) {
      throw new Error(`Drivers license verification is not supported in country ${countryCode}`);
    }
  }
}
```

### Step 2: Create Controller

Create or update `src/modules/kyc/kyc.controller.ts`:

```typescript
import {
  Controller,
  Post,
  Body,
  UseGuards,
  HttpCode,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../shared/guards/jwt-auth.guard';
import { GetUser } from '../../shared/decorators/get-user.decorator';
import { User } from '../../core/entities/user.entity';
import { VerifyDriversLicenseUseCase } from '../../core/use-cases/kyc/verify-drivers-license.use-case';
import { VerifyDriversLicenseDto } from './dto/verify-drivers-license.dto';

@ApiTags('KYC')
@Controller('kyc')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class KycController {
  private readonly logger = new Logger(KycController.name);

  constructor(
    private readonly verifyDriversLicenseUseCase: VerifyDriversLicenseUseCase,
    // ... other use cases
  ) {}

  @Post('verify/drivers-license')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Verify drivers license',
    description: 'Verify a drivers license using KYC providers'
  })
  @ApiResponse({ status: 200, description: 'Drivers license verification completed' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async verifyDriversLicense(
    @Body() dto: VerifyDriversLicenseDto,
    @GetUser() user: User,
  ) {
    this.logger.log(`Drivers license verification request from user ${user.id}`, {
      userId: user.id,
      licenseNumber: dto.licenseNumber.substring(0, 4) + '****',
      countryCode: dto.countryCode,
    });

    try {
      const result = await this.verifyDriversLicenseUseCase.execute({
        userId: user.id,
        licenseNumber: dto.licenseNumber,
        firstName: dto.firstName,
        lastName: dto.lastName,
        dateOfBirth: dto.dateOfBirth,
        documentImageUrl: dto.documentImageUrl,
        countryCode: dto.countryCode,
      });

      return {
        success: true,
        message: result.isVerified ? 'Drivers license verified successfully' : 'Drivers license verification failed',
        data: {
          verificationId: result.verification.id,
          isVerified: result.isVerified,
          status: result.verification.status,
          providerUsed: result.providerUsed,
          responseTime: result.responseTime,
          extractedData: result.extractedData,
          verifiedAt: result.verification.verifiedAt,
        },
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Drivers license verification failed for user ${user.id}`, {
        userId: user.id,
        error: errorMessage,
      });

      return {
        success: false,
        message: 'Drivers license verification failed',
        error: errorMessage,
        data: null,
      };
    }
  }
}
```

### Step 3: Create DTO

Create `src/modules/kyc/dto/verify-drivers-license.dto.ts`:

```typescript
import { IsString, IsOptional, IsDateString, IsUrl, Length, Matches } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class VerifyDriversLicenseDto {
  @ApiProperty({
    description: 'Drivers license number',
    example: 'ABC123456789',
    minLength: 8,
    maxLength: 20,
  })
  @IsString()
  @Length(8, 20, { message: 'License number must be between 8 and 20 characters' })
  licenseNumber: string;

  @ApiPropertyOptional({
    description: 'First name on the license',
    example: 'John',
  })
  @IsOptional()
  @IsString()
  firstName?: string;

  @ApiPropertyOptional({
    description: 'Last name on the license',
    example: 'Doe',
  })
  @IsOptional()
  @IsString()
  lastName?: string;

  @ApiPropertyOptional({
    description: 'Date of birth (YYYY-MM-DD)',
    example: '1990-01-01',
  })
  @IsOptional()
  @IsDateString()
  dateOfBirth?: string;

  @ApiPropertyOptional({
    description: 'URL of the license document image',
    example: 'https://storage.example.com/license.jpg',
  })
  @IsOptional()
  @IsUrl()
  documentImageUrl?: string;

  @ApiPropertyOptional({
    description: 'Country code (default: 234 for Nigeria)',
    example: '234',
  })
  @IsOptional()
  @IsString()
  @Matches(/^[0-9]{1,3}$/, { message: 'Country code must be 1-3 digits' })
  countryCode?: string;
}
```

### Step 4: Update Module

Update `src/modules/kyc/kyc.module.ts`:

```typescript
import { Module } from '@nestjs/common';
import { KycController } from './kyc.controller';

// Use Cases
import { VerifyBvnUseCase } from '../../core/use-cases/kyc/verify-bvn.use-case';
import { VerifyDriversLicenseUseCase } from '../../core/use-cases/kyc/verify-drivers-license.use-case';
// ... other use cases

// Infrastructure
import { KycInfrastructureModule } from '../../infrastructure/kyc/kyc-infrastructure.module';

@Module({
  imports: [KycInfrastructureModule],
  controllers: [KycController],
  providers: [
    // Use Cases
    VerifyBvnUseCase,
    VerifyDriversLicenseUseCase,
    // ... other use cases
  ],
  exports: [
    VerifyBvnUseCase,
    VerifyDriversLicenseUseCase,
    // ... other use cases
  ],
})
export class KycModule {}
```

## BVN Route Example

Here's a complete example of how the BVN verification route works:

### 1. BVN Use Case

```typescript
// src/core/use-cases/kyc/verify-bvn.use-case.ts
@Injectable()
export class VerifyBvnUseCase {
  async execute(input: VerifyBvnInput): Promise<VerifyBvnOutput> {
    // 1. Validate input (BVN format, required fields)
    this.validateInput(input, countryCode);

    // 2. Check for existing verification
    const existingVerification = await this.verificationRepository.getUserLatestVerification(
      input.userId,
      DocumentType.BVN
    );

    // 3. Return existing if valid and not expired
    if (existingVerification?.isVerified && !existingVerification.isExpired) {
      return { verification: existingVerification, isVerified: true, ... };
    }

    // 4. Create verification request
    const verificationRequest: BvnVerificationRequest = {
      documentType: DocumentType.BVN,
      documentNumber: input.bvn,
      firstName: input.firstName,
      lastName: input.lastName,
      dateOfBirth: input.dateOfBirth,
      phoneNumber: input.phoneNumber,
    };

    // 5. Use provider manager (handles failover automatically)
    const { result, log } = await this.providerManager.verifyDocument(
      countryCode,
      DocumentType.BVN,
      verificationRequest
    );

    // 6. Create verification entity
    const verification = KycVerification.create({
      userId: input.userId,
      verificationType: VerificationType.IDENTITY,
      documentType: DocumentType.BVN,
      countryCode,
      providerId: log.attempts[log.attempts.length - 1]?.providerId,
      providerReference: result.providerReference,
      documentNumber: input.bvn,
      verificationData: result.extractedData,
    });

    // 7. Update status based on result
    let finalVerification: KycVerification;
    if (result.success && result.verified) {
      finalVerification = verification.markAsVerified(result.providerResponse, result.extractedData);
    } else {
      finalVerification = verification.markAsFailed(result.failureReason, result.providerResponse);
    }

    // 8. Save to database
    const savedVerification = await this.verificationRepository.save(finalVerification);

    return {
      verification: savedVerification,
      isVerified: savedVerification.isVerified,
      extractedData: savedVerification.verificationData,
      providerUsed: savedVerification.providerId,
      responseTime: Date.now() - startTime,
    };
  }
}
```

### 2. BVN Controller

```typescript
// src/modules/kyc/kyc.controller.ts
@Post('verify/bvn')
@HttpCode(HttpStatus.OK)
@ApiOperation({ summary: 'Verify BVN' })
async verifyBvn(@Body() dto: VerifyBvnDto, @GetUser() user: User) {
  const result = await this.verifyBvnUseCase.execute({
    userId: user.id,
    bvn: dto.bvn,
    firstName: dto.firstName,
    lastName: dto.lastName,
    dateOfBirth: dto.dateOfBirth,
    phoneNumber: dto.phoneNumber,
    countryCode: dto.countryCode || '234',
  });

  return {
    success: true,
    message: result.isVerified ? 'BVN verified successfully' : 'BVN verification failed',
    data: {
      verificationId: result.verification.id,
      isVerified: result.isVerified,
      status: result.verification.status,
      providerUsed: result.providerUsed,
      responseTime: result.responseTime,
      extractedData: result.extractedData,
    },
  };
}
```

### 3. BVN DTO

```typescript
// src/modules/kyc/dto/verify-bvn.dto.ts
export class VerifyBvnDto {
  @ApiProperty({ description: 'Bank Verification Number (11 digits)', example: '***********' })
  @IsString()
  @Length(11, 11, { message: 'BVN must be exactly 11 digits' })
  @Matches(/^[0-9]{11}$/, { message: 'BVN must contain only digits' })
  bvn: string;

  @ApiPropertyOptional({ description: 'First name', example: 'John' })
  @IsOptional()
  @IsString()
  firstName?: string;

  @ApiPropertyOptional({ description: 'Last name', example: 'Doe' })
  @IsOptional()
  @IsString()
  lastName?: string;

  @ApiPropertyOptional({ description: 'Date of birth (YYYY-MM-DD)', example: '1990-01-01' })
  @IsOptional()
  @IsDateString()
  dateOfBirth?: string;

  @ApiPropertyOptional({ description: 'Phone number', example: '***********' })
  @IsOptional()
  @IsString()
  @Matches(/^[0-9]{11}$/, { message: 'Phone number must be 11 digits' })
  phoneNumber?: string;

  @ApiPropertyOptional({ description: 'Country code', example: '234' })
  @IsOptional()
  @IsString()
  countryCode?: string;
}
```

### 4. Provider Manager Flow

```typescript
// The provider manager handles the complexity:
async verifyDocument(countryCode: string, documentType: DocumentType, request: VerificationRequest) {
  // 1. Get country configuration
  const countryConfig = this.countryConfigService.getCountryConfig(countryCode);
  const providerConfig = countryConfig.providers[documentType];

  // 2. Try primary provider first
  const providersToTry = [providerConfig.primary, ...providerConfig.fallback];

  for (const providerId of providersToTry) {
    try {
      // 3. Create provider instance
      const provider = this.providerFactory.create(providerId);

      // 4. Verify document
      const result = await provider.verify(request, countryCode);

      // 5. If successful, return result
      if (result.success && result.verified) {
        return { result, log: { attempts: [{ providerId, success: true }] } };
      }
    } catch (error) {
      // 6. Log failure and try next provider
      this.logger.warn(`Provider ${providerId} failed, trying next...`);
    }
  }

  // 7. All providers failed
  throw new Error('All providers failed');
}
```

### 5. API Usage

```bash
# BVN Verification Request
curl -X POST http://localhost:3000/api/kyc/verify/bvn \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "bvn": "***********",
    "firstName": "John",
    "lastName": "Doe",
    "dateOfBirth": "1990-01-01",
    "phoneNumber": "***********"
  }'

# Response
{
  "success": true,
  "message": "BVN verified successfully",
  "data": {
    "verificationId": "uuid-here",
    "isVerified": true,
    "status": "VERIFIED",
    "providerUsed": "zeeh",
    "responseTime": 1234,
    "extractedData": {
      "firstName": "John",
      "lastName": "Doe",
      "dateOfBirth": "1990-01-01",
      "gender": "Male",
      "phoneNumber": "***********",
      "bvn": "***********"
    }
  }
}
```

## Testing

### 1. Unit Tests

```bash
# Run all KYC tests
npm test src/infrastructure/kyc/

# Run specific provider tests
npm test src/infrastructure/kyc/providers/zeeh-kyc.provider.test.ts

# Run use case tests
npm test src/core/use-cases/kyc/verify-bvn.use-case.test.ts
```

### 2. Integration Tests

```bash
# Test API endpoints
npm run test:e2e

# Test specific KYC endpoints
npm run test:e2e -- --grep "KYC"
```

### 3. Manual Testing

```bash
# Test Zeeh API connectivity
node test-zeeh-builtin.js

# Test BVN verification
node test-bvn-simple.js

# Test full integration
npm run test:bvn
```

## Troubleshooting

### Common Issues

1. **401 Authentication Errors**
   - Check API keys in `.env` file
   - Verify correct header format for provider
   - Ensure account is active with provider

2. **400 Bad Request Errors**
   - Validate input data format
   - Check document number format
   - Verify required fields are provided

3. **Provider Failover Not Working**
   - Check country configuration
   - Verify fallback providers are configured
   - Check provider health status

4. **Database Connection Issues**
   - Verify `DATABASE_URL` in `.env`
   - Run `npm run migrate`
   - Check PostgreSQL is running

### Debug Commands

```bash
# Check environment variables
node -e "require('dotenv').config(); console.log(process.env.ZEEH_API_KEY)"

# Test database connection
npx prisma db pull

# Check provider health
node -e "
const { ZeehKycProvider } = require('./dist/infrastructure/kyc/providers/zeeh-kyc.provider');
const provider = new ZeehKycProvider(config);
provider.checkHealth().then(console.log);
"
```

### Logging

Enable detailed logging by setting:

```bash
ZEEH_ENABLE_LOGGING=true
PREMBLY_ENABLE_LOGGING=true
LOG_LEVEL=debug
```

This will provide detailed request/response logs for debugging provider issues.

---

This documentation provides a complete guide for working with the KYC system. For specific implementation details, refer to the existing code examples in the codebase.
```
