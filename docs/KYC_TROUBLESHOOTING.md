# KYC System Troubleshooting Guide

## 🚨 Common Issues & Solutions

### 1. Authentication Issues (401 Errors)

#### Zeeh Authentication Failed
```bash
# Symptoms
- "Invalid authentication credentials"
- 401 status code from Zeeh API

# Diagnosis
node test-zeeh-builtin.js

# Solutions
1. Check API keys in .env file
   ZEEH_API_KEY=pk_your_actual_key
   ZEEH_SECRET_KEY=pv_your_actual_secret

2. Verify header format (<PERSON><PERSON><PERSON> uses Secret_Key, not x-api-key)
   headers: {
     'Content-Type': 'application/json',
     'Secret_Key': process.env.ZEEH_SECRET_KEY
   }

3. Check account status with Zeeh support
4. Verify API keys are for correct environment (sandbox vs production)
```

#### Prembly Authentication Failed
```bash
# Check Prembly configuration
PREMBLY_API_KEY=your_key
PREMBLY_SECRET_KEY=your_secret
PREMBLY_BASE_URL=https://api.prembly.com/v1

# Test Prembly connectivity
curl -X POST https://api.prembly.com/v1/identitypass/verification/bvn \
  -H "Content-Type: application/json" \
  -H "x-api-key: YOUR_API_KEY" \
  -H "app-id: YOUR_APP_ID"
```

### 2. Bad Request Issues (400 Errors)

#### Invalid Document Format
```bash
# BVN must be exactly 11 digits
✅ Valid: "***********"
❌ Invalid: "*********" (too short)
❌ Invalid: "***********2" (too long)
❌ Invalid: "*********0a" (contains letters)

# NIN must be exactly 11 digits
✅ Valid: "***********"
❌ Invalid: Same rules as BVN

# Phone numbers must be 11 digits (Nigerian format)
✅ Valid: "***********"
❌ Invalid: "+2348*********" (includes country code)
```

#### Missing Required Fields
```typescript
// Check validation rules in DTOs
export class VerifyBvnDto {
  @IsString()
  @Length(11, 11)
  @Matches(/^[0-9]{11}$/)
  bvn: string; // Required

  @IsOptional()
  @IsString()
  firstName?: string; // Optional but recommended
}
```

### 3. Provider Failover Issues

#### Primary Provider Down
```bash
# Check provider health
node -e "
const { ZeehKycProvider } = require('./dist/infrastructure/kyc/providers/zeeh-kyc.provider');
const provider = new ZeehKycProvider(config);
provider.checkHealth().then(console.log);
"

# Expected output for healthy provider:
{
  isHealthy: true,
  responseTime: 1234,
  lastChecked: '2024-01-01T12:00:00.000Z'
}
```

#### Fallback Not Configured
```typescript
// Check country configuration
const nigeriaConfig = {
  providers: {
    [DocumentType.BVN]: {
      primary: KYC_PROVIDERS.ZEEH,
      fallback: [KYC_PROVIDERS.PREMBLY], // Must have fallback
    }
  }
};
```

### 4. Database Connection Issues

#### Connection Failed
```bash
# Check DATABASE_URL
echo $DATABASE_URL

# Test connection
npx prisma db pull

# Reset if needed
npm run prisma:reset

# Check PostgreSQL is running
pg_isready -h localhost -p 5432
```

#### Migration Issues
```bash
# Check migration status
npx prisma migrate status

# Apply pending migrations
npx prisma migrate deploy

# Reset and re-migrate
npm run prisma:reset
```

### 5. Storage Issues

#### Cloudinary Upload Failed
```bash
# Check Cloudinary config
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret

# Test upload
node -e "
const cloudinary = require('cloudinary').v2;
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET
});
cloudinary.uploader.upload('test.jpg').then(console.log);
"
```

#### AWS S3 Issues
```bash
# Check AWS config
AWS_REGION=us-east-1
AWS_S3_BUCKET_NAME=your_bucket
AWS_ACCESS_KEY_ID=your_key
AWS_SECRET_ACCESS_KEY=your_secret

# Test S3 connection
aws s3 ls s3://your-bucket-name
```

### 6. Performance Issues

#### Slow Response Times
```bash
# Check provider response times
node test-bvn-simple.js

# Expected response times:
- BVN: < 3 seconds
- NIN: < 5 seconds
- CAC: < 10 seconds

# If slow, check:
1. Network connectivity
2. Provider API status
3. Database performance
4. Server resources
```

#### Memory Issues
```bash
# Check memory usage
node --max-old-space-size=4096 app.js

# Monitor memory
top -p $(pgrep node)

# Check for memory leaks
node --inspect app.js
```

### 7. Validation Errors

#### Country Not Supported
```typescript
// Error: "Country 999 not supported by Zeeh"
// Solution: Check supported countries
const supportedCountries = ['234', '233', '254'];

// Add new country support
private getCountrySupportedDocuments(countryCode: string): DocumentType[] {
  switch (countryCode) {
    case '999': // Add new country
      return [DocumentType.NATIONAL_ID];
    default:
      return [];
  }
}
```

#### Document Type Not Supported
```typescript
// Error: "Document type BVN not supported in country 233"
// Solution: Check country-specific document support

// Ghana (233) doesn't support BVN
const ghanaSupported = [
  DocumentType.DRIVERS_LICENSE,
  DocumentType.INTERNATIONAL_PASSPORT,
  DocumentType.VOTERS_CARD,
  DocumentType.TIN
];
```

## 🔧 Diagnostic Commands

### Environment Check
```bash
# Check all environment variables
node -e "
require('dotenv').config();
const required = [
  'ZEEH_API_KEY', 'ZEEH_SECRET_KEY',
  'DATABASE_URL', 'CLOUDINARY_CLOUD_NAME'
];
required.forEach(key => {
  console.log(\`\${key}: \${process.env[key] ? '✅ Set' : '❌ Missing'}\`);
});
"
```

### API Connectivity Test
```bash
# Test all providers
node -e "
const providers = ['zeeh', 'prembly'];
providers.forEach(async (provider) => {
  try {
    const health = await testProvider(provider);
    console.log(\`\${provider}: \${health.isHealthy ? '✅' : '❌'}\`);
  } catch (error) {
    console.log(\`\${provider}: ❌ \${error.message}\`);
  }
});
"
```

### Database Health Check
```bash
# Check database connection and tables
npx prisma db pull
npx prisma generate

# Check specific tables
psql $DATABASE_URL -c "SELECT COUNT(*) FROM kyc_verifications;"
```

### Log Analysis
```bash
# Check application logs
tail -f logs/application.log | grep -E "(ERROR|WARN|KYC)"

# Check specific verification
grep "verification-id-here" logs/application.log

# Check provider errors
grep "Provider.*failed" logs/application.log
```

## 🚨 Emergency Procedures

### All Providers Down
```bash
# 1. Check provider status pages
# 2. Enable maintenance mode
# 3. Queue verification requests
# 4. Notify users of temporary unavailability

# Emergency fallback to manual verification
export KYC_MANUAL_MODE=true
```

### Database Connection Lost
```bash
# 1. Check database server status
# 2. Verify connection string
# 3. Check network connectivity
# 4. Restart application if needed

# Emergency read-only mode
export DATABASE_READ_ONLY=true
```

### High Error Rate
```bash
# 1. Check error logs
tail -f logs/application.log | grep ERROR

# 2. Monitor error rate
grep "$(date '+%Y-%m-%d %H:%M')" logs/application.log | grep ERROR | wc -l

# 3. If > 10% error rate, investigate immediately
```

## 📊 Monitoring & Alerts

### Key Metrics to Monitor
```bash
# Success rate by provider
SELECT 
  provider_id,
  COUNT(*) as total,
  SUM(CASE WHEN status = 'VERIFIED' THEN 1 ELSE 0 END) as successful,
  (SUM(CASE WHEN status = 'VERIFIED' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) as success_rate
FROM kyc_verifications 
WHERE created_at > NOW() - INTERVAL '1 hour'
GROUP BY provider_id;

# Average response time
SELECT 
  document_type,
  AVG(response_time_ms) as avg_response_time
FROM kyc_verifications 
WHERE created_at > NOW() - INTERVAL '1 hour'
GROUP BY document_type;

# Error rate by hour
SELECT 
  DATE_TRUNC('hour', created_at) as hour,
  COUNT(*) as total,
  SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) as failed
FROM kyc_verifications 
WHERE created_at > NOW() - INTERVAL '24 hours'
GROUP BY hour
ORDER BY hour;
```

### Alert Thresholds
- Success rate < 90%: Warning
- Success rate < 80%: Critical
- Response time > 10s: Warning
- Response time > 30s: Critical
- Error rate > 5%: Warning
- Error rate > 10%: Critical

## 🆘 Getting Help

### Internal Escalation
1. Check logs and metrics
2. Run diagnostic commands
3. Review recent changes
4. Contact development team

### External Support
- **Zeeh**: <EMAIL>
- **Prembly**: <EMAIL>
- **Cloudinary**: <EMAIL>

### Documentation
- [System Documentation](./KYC_SYSTEM_DOCUMENTATION.md)
- [Quick Reference](./KYC_QUICK_REFERENCE.md)
- Provider API documentation

---

**Remember**: Always check logs first, test with diagnostic commands, and escalate if issues persist beyond 15 minutes.
