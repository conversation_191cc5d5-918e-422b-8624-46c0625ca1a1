-- CreateEnum
CREATE TYPE "AuditAction" AS ENUM ('CREATE', 'READ', 'UPDATE', 'DELETE', 'SHARE', 'UNSHARE', 'LOGIN', 'LOGOUT', 'PASSWORD_CHANGE', 'PERMISSION_GRANT', 'PERMISSION_REVOKE', 'ROLE_ASSIGN', 'ROLE_UNASSIGN');

-- AlterTable
ALTER TABLE "audit_logs" DROP COLUMN "action",
ADD COLUMN     "action" "AuditAction" NOT NULL,
ADD COLUMN     "account_id" TEXT,
ADD COLUMN     "after_values" JSONB,
ADD COLUMN     "before_values" JSONB,
ADD COLUMN     "ip_address" TEXT,
ADD COLUMN     "session_id" TEXT,
ADD COLUMN     "user_agent" TEXT,
ADD COLUMN     "wallet_id" TEXT,
ALTER COLUMN "entityId" SET DATA TYPE TEXT,
ALTER COLUMN "entityId" SET NOT NULL;

-- Rename column
ALTER TABLE "audit_logs" RENAME COLUMN "entityId" TO "entity_id";

-- CreateIndex
CREATE INDEX "audit_logs_userId_idx" ON "audit_logs"("user_id");

-- CreateIndex
CREATE INDEX "audit_logs_account_id_idx" ON "audit_logs"("account_id");

-- CreateIndex
CREATE INDEX "audit_logs_wallet_id_idx" ON "audit_logs"("wallet_id");

-- CreateIndex
CREATE INDEX "audit_logs_entity_entity_id_idx" ON "audit_logs"("entity", "entity_id");

-- CreateIndex
CREATE INDEX "audit_logs_action_idx" ON "audit_logs"("action");

-- CreateIndex
CREATE INDEX "audit_logs_created_at_idx" ON "audit_logs"("created_at");

-- AddForeignKey
ALTER TABLE "audit_logs" ADD CONSTRAINT "audit_logs_account_id_fkey" FOREIGN KEY ("account_id") REFERENCES "accounts"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "audit_logs" ADD CONSTRAINT "audit_logs_wallet_id_fkey" FOREIGN KEY ("wallet_id") REFERENCES "wallets"("id") ON DELETE SET NULL ON UPDATE CASCADE;
