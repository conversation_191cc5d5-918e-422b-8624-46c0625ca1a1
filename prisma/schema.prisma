generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                  String                @id @default(uuid())
  email               String                @unique
  firstName           String                @map("first_name")
  lastName            String                @map("last_name")
  phoneNumber         String                @unique @map("phone_number")
  bvn                 String?               @unique
  passwordHash        String                @map("password_hash")
  countryCode         String                @default("234") @map("country_code")
  appPinHash          String?               @map("app_pin_hash")
  createdAt           DateTime              @default(now()) @map("created_at")
  updatedAt           DateTime              @updatedAt @map("updated_at")
  refreshTokenHash    String?               @map("refresh_token_hash")
  deletedAt           DateTime?             @map("deleted_at")
  lastLogin           DateTime?             @map("last_login")
  accounts            Account[]
  wallets             Wallet[]
  categories          Category[]
  subcategories       SubCategory[]
  auditLogs           AuditLog[]
  sharedAccess        UserSharedAccess[]    // Access granted to this user
  grantedAccess       UserSharedAccess[]    @relation("GrantedAccess") // Access granted by this user
  roles               Role[]                // Custom roles created by this user
  userRoles           UserRole[]            // Assigned roles
  passwordResetTokens PasswordResetToken[]
  trustedDevices      TrustedDevice[]
  refreshTokens       RefreshToken[]
  kycVerifications    KycVerification[]

  @@map("users")
}

model Account {
  id        String    @id @default(uuid())
  userId    String    @map("user_id")
  name      String
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  user      User               @relation(fields: [userId], references: [id])
  wallets   Wallet[]
  shares    UserSharedAccess[]
  auditLogs AuditLog[]

  @@map("accounts")
}

model WalletType {
  id          String    @id @default(uuid())
  name        String
  description String?
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")
  deletedAt   DateTime? @map("deleted_at")

  wallets Wallet[]

  @@map("wallet_types")
}

model VirtualWalletAPI {
  id           String    @id @default(uuid())
  name         String
  baseUrl      String    @map("base_url")
  configSchema Json      @map("config_schema")
  isActive     Boolean   @map("is_active")
  createdAt    DateTime  @default(now()) @map("created_at")
  updatedAt    DateTime  @updatedAt @map("updated_at")
  deletedAt    DateTime? @map("deleted_at")

  wallets Wallet[]

  @@map("virtual_wallet_apis")
}

model Wallet {
  id                 String    @id @default(uuid())
  userId             String    @map("user_id")
  accountId          String?    @map("account_id")
  walletTypeId       String    @map("wallet_type_id")
  virtualWalletApiId String?   @map("virtual_wallet_api_id")
  label              String
  externalReference  String?    @map("external_reference")
  balance            Decimal   @default(0.00)
  currency           String    @default("NGN")
  isActive           Boolean   @map("is_active") @default(true)
  createdAt          DateTime  @default(now()) @map("created_at")
  updatedAt          DateTime  @updatedAt @map("updated_at")
  deletedAt          DateTime? @map("deleted_at")

  user             User               @relation(fields: [userId], references: [id])
  account          Account?            @relation(fields: [accountId], references: [id])
  walletType       WalletType         @relation(fields: [walletTypeId], references: [id])
  virtualWalletApi VirtualWalletAPI?  @relation(fields: [virtualWalletApiId], references: [id])
  transactions     Transaction[]
  shares           UserSharedAccess[]
  auditLogs        AuditLog[]

  @@map("wallets")
}

model Category {
  id        String    @id @default(uuid())
  userId    String    @map("user_id")
  name      String
  type      String // income or expense
  color     String?
  sortOrder Int       @default(0) @map("sort_order")
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  user          User               @relation(fields: [userId], references: [id])
  subcategories SubCategory[]
  splits        TransactionSplit[]

  @@index([userId, sortOrder])
  @@map("categories")
}

model SubCategory {
  id         String             @id @default(uuid())
  userId     String             @map("user_id")
  categoryId String             @map("category_id")
  name       String
  sortOrder  Int                @default(0) @map("sort_order")
  createdAt  DateTime           @default(now()) @map("created_at")
  updatedAt  DateTime           @updatedAt @map("updated_at")
  deletedAt  DateTime?          @map("deleted_at")
  user       User               @relation(fields: [userId], references: [id])
  category   Category           @relation(fields: [categoryId], references: [id])
  splits     TransactionSplit[]

  @@index([userId, categoryId, sortOrder])
  @@map("subcategories")
}

model Transaction {
  id              String    @id @default(uuid())
  walletId        String    @map("wallet_id")
  amount          Decimal
  transactionType String    @map("transaction_type")
  description     String?
  reference       String    @unique
  status          String
  completedAt     DateTime? @map("completed_at")
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @updatedAt @map("updated_at")
  deletedAt       DateTime? @map("deleted_at")

  wallet Wallet             @relation(fields: [walletId], references: [id])
  splits TransactionSplit[]

  @@map("transactions")
}

model TransactionSplit {
  id            String  @id @default(uuid())
  transactionId String  @map("transaction_id")
  subCategoryId String? @map("subcategory_id")
  categoryId    String? @map("category_id")
  amount        Decimal
  note          String?

  transaction Transaction  @relation(fields: [transactionId], references: [id])
  subcategory SubCategory? @relation(fields: [subCategoryId], references: [id])
  category    Category?    @relation(fields: [categoryId], references: [id])

  @@map("transaction_splits")
}

model Role {
  id           String   @id @default(uuid())
  userId       String?  @map("user_id") // null for system roles
  name         String
  description  String?
  isSystemRole Boolean  @default(false) @map("is_system_role")
  createdAt    DateTime @default(now()) @map("created_at")

  user         User?              @relation(fields: [userId], references: [id])
  permissions  RolePermission[]
  sharedAccess UserSharedAccess[]
  userRoles    UserRole[] // Users assigned to this role

  @@unique([userId, name]) // Unique per user, system roles have userId = null
  @@map("roles")
}

model UserRole {
  id        String   @id @default(uuid())
  userId    String   @map("user_id")
  roleId    String   @map("role_id")
  createdAt DateTime @default(now()) @map("created_at")

  user User @relation(fields: [userId], references: [id])
  role Role @relation(fields: [roleId], references: [id])

  @@unique([userId, roleId])
  @@map("user_roles")
}

model Permission {
  id        String   @id @default(uuid())
  name      String   @unique
  label     String
  createdAt DateTime @default(now())

  roles RolePermission[]

  @@map("permissions")
}

model RolePermission {
  id           String @id @default(uuid())
  roleId       String
  permissionId String

  role       Role       @relation(fields: [roleId], references: [id])
  permission Permission @relation(fields: [permissionId], references: [id])

  @@map("role_permissions")
}

model UserSharedAccess {
  id        String    @id @default(uuid())
  userId    String    @map("user_id")
  accountId String?   @map("account_id")   // For account-level sharing
  walletId  String?   @map("wallet_id")    // For wallet-level sharing
  roleId    String    @map("role_id")
  grantedBy String    @map("granted_by")   // Who granted this access
  shareType ShareType @map("share_type")   // "ACCOUNT" or "WALLET"
  createdAt DateTime  @default(now()) @map("created_at")

  user    User     @relation(fields: [userId], references: [id])
  account Account? @relation(fields: [accountId], references: [id])
  wallet  Wallet?  @relation(fields: [walletId], references: [id])
  role    Role     @relation(fields: [roleId], references: [id])
  grantor User     @relation("GrantedAccess", fields: [grantedBy], references: [id])

  @@unique([userId, accountId, roleId])
  @@unique([userId, walletId, roleId])
  @@map("user_shared_access")
}

enum ShareType {
  ACCOUNT
  WALLET
}

model AuditLog {
  id              String      @id @default(uuid())
  userId          String      @map("user_id")
  action          AuditAction
  entity          String
  entityId        String      @map("entity_id")
  accountId       String?     @map("account_id")   // For permission filtering
  walletId        String?     @map("wallet_id")    // For permission filtering
  beforeValues    Json?       @map("before_values") // State before change
  afterValues     Json?       @map("after_values")  // State after change
  metadata        Json?       // Additional context
  ipAddress       String?     @map("ip_address")
  userAgent       String?     @map("user_agent")
  sessionId       String?     @map("session_id")
  createdAt       DateTime    @default(now()) @map("created_at")

  user    User     @relation(fields: [userId], references: [id])
  account Account? @relation(fields: [accountId], references: [id])
  wallet  Wallet?  @relation(fields: [walletId], references: [id])

  @@index([userId])
  @@index([accountId])
  @@index([walletId])
  @@index([entity, entityId])
  @@index([action])
  @@index([createdAt])
  @@map("audit_logs")
}

enum AuditAction {
  CREATE
  READ
  UPDATE
  DELETE
  SHARE
  UNSHARE
  LOGIN
  LOGOUT
  PASSWORD_CHANGE
  PERMISSION_GRANT
  PERMISSION_REVOKE
  ROLE_ASSIGN
  ROLE_UNASSIGN
}

model PasswordResetToken {
  id         String    @id @default(uuid())
  userId     String    @map("user_id")
  token      String    @unique
  expiresAt  DateTime  @map("expires_at")
  usedAt     DateTime? @map("used_at")
  verifiedAt DateTime? @map("verified_at")
  createdAt  DateTime  @default(now()) @map("created_at")

  user User @relation(fields: [userId], references: [id])

  @@map("password_reset_tokens")
}

model AdminUser {
  id                String    @id @default(uuid())
  email             String    @unique
  firstName         String    @map("first_name")
  lastName          String    @map("last_name")
  passwordHash      String    @map("password_hash")
  isSuperAdmin      Boolean   @default(false) @map("is_super_admin")
  isActive          Boolean   @default(false) @map("is_active") // Requires SuperAdmin approval
  refreshTokenHash  String?   @map("refresh_token_hash")
  createdAt         DateTime  @default(now()) @map("created_at")
  updatedAt         DateTime  @updatedAt @map("updated_at")
  activatedAt       DateTime? @map("activated_at")
  activatedBy       String?   @map("activated_by") // SuperAdmin who activated this admin
  deletedAt         DateTime? @map("deleted_at")

  activator AdminUser? @relation("AdminActivation", fields: [activatedBy], references: [id])
  activatedAdmins AdminUser[] @relation("AdminActivation")

  @@map("admin_users")
}

model TrustedDevice {
  id        String   @id @default(uuid())
  userId    String   @map("user_id")
  deviceId  String   @map("device_id")
  createdAt DateTime @default(now()) @map("created_at")

  user User @relation(fields: [userId], references: [id])

  @@unique([userId, deviceId])
  @@map("trusted_devices")
}

model RefreshToken {
  id         String   @id @default(uuid())
  userId     String   @map("user_id")
  deviceId   String   @map("device_id")
  tokenHash  String   @map("token_hash")
  createdAt  DateTime @default(now()) @map("created_at")
  expiresAt  DateTime @map("expires_at")

  user User @relation(fields: [userId], references: [id])

  @@unique([userId, deviceId])
  @@map("refresh_tokens")
}

model KycVerification {
  id                  String    @id @default(uuid())
  userId              String    @map("user_id")
  verificationType    String    @map("verification_type") // IDENTITY, ADDRESS, BUSINESS, FACIAL
  documentType        String    @map("document_type") // BVN, NIN, DRIVERS_LICENSE, etc.
  status              String    @map("status") // PENDING, IN_PROGRESS, VERIFIED, FAILED, EXPIRED, REQUIRES_MANUAL_REVIEW
  countryCode         String    @map("country_code")
  providerId          String    @map("provider_id")
  providerReference   String    @map("provider_reference")
  documentNumber      String?   @map("document_number")
  documentImageUrl    String?   @map("document_image_url")
  verificationData    Json?     @map("verification_data")
  providerResponse    Json?     @map("provider_response")
  failureReason       String?   @map("failure_reason")
  verifiedAt          DateTime? @map("verified_at")
  expiresAt           DateTime? @map("expires_at")
  createdAt           DateTime  @default(now()) @map("created_at")
  updatedAt           DateTime  @updatedAt @map("updated_at")

  user User @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([userId, documentType])
  @@index([status])
  @@index([providerId])
  @@index([providerReference])
  @@map("kyc_verifications")
}
