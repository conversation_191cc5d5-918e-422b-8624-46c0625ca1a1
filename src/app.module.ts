import { Module } from '@nestjs/common';
import { AuthModule } from './modules/auth/auth.module';
import { AdminModule } from './modules/admin/admin.module';
import { AccountModule } from './modules/account/account.module';
import { CategoriesModule } from './modules/categories/categories.module';
import { UsersModule } from './modules/users/users.module';
import { PermissionsModule } from './modules/permissions/permissions.module';
import { RolesModule } from './modules/roles/roles.module';
import { SeedingModule } from './modules/seeding/seeding.module';
import { SharingModule } from './modules/sharing/sharing.module';
import { AuditModule } from './modules/audit/audit.module';
import { SharedModule } from './shared';
import { ConfigModule } from '@nestjs/config';
import { WalletModule } from './modules/wallet/wallet.module';
import { WalletTypeModule } from './modules/wallet-type/wallet-type.module';
import { HealthModule } from './modules/health/health.module';
import { StorageModule } from './infrastructure/storage/storage.module';
import { ThrottlerModule, ThrottlerGuard } from '@nestjs/throttler';
import { APP_GUARD } from '@nestjs/core';

@Module({
  controllers: [],
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    ThrottlerModule.forRoot([
      {
        name: 'short',
        ttl: 1000, // 1 second
        limit: 3, // 3 requests per second
      },
      {
        name: 'medium',
        ttl: 10000, // 10 seconds
        limit: 20, // 20 requests per 10 seconds
      },
      {
        name: 'long',
        ttl: 60000, // 1 minute
        limit: 100, // 100 requests per minute
      },
    ]),
    AuthModule,
    AdminModule,
    AccountModule,
    UsersModule,
    CategoriesModule,
    PermissionsModule,
    RolesModule,
    SeedingModule,
    SharingModule,
    AuditModule,
    SharedModule,
    WalletModule,
    WalletTypeModule,
    HealthModule,
    StorageModule,
  ],
  providers: [
    {
      provide: APP_GUARD,
      useClass: ThrottlerGuard,
    },
  ],
})
export class AppModule {}
