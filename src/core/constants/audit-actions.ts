/**
 * Audit action constants for tracking user activities
 */

export enum AuditAction {
  // CRUD Operations
  CREATE = 'CREATE',
  READ = 'READ',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',

  // Sharing Operations
  SHARE = 'SHARE',
  UNSHARE = 'UNSHARE',

  // Authentication Events
  LOGIN = 'LOGIN',
  LOGOUT = 'LOGOUT',
  PASSWORD_CHANGE = 'PASSWORD_CHANGE',

  // Permission Management
  PERMISSION_GRANT = 'PERMISSION_GRANT',
  PERMISSION_REVOKE = 'PERMISSION_REVOKE',
  ROLE_ASSIGN = 'ROLE_ASSIGN',
  ROLE_UNASSIGN = 'ROLE_UNASSIGN',
}

/**
 * Entity types that can be audited
 */
export enum AuditEntity {
  USER = 'USER',
  ACCOUNT = 'ACCOUNT',
  WALLET = 'WALLET',
  TRANSACTION = 'TRANSACTION',
  CATEGORY = 'CATEGORY',
  SUBCATEGORY = 'SUBCATEGORY',
  ROLE = 'ROLE',
  PERMISSION = 'PERMISSION',
  USER_SHARED_ACCESS = 'USER_SHARED_ACCESS',
  KYC_VERIFICATION = 'KYC_VERIFICATION',
}

/**
 * Audit action descriptions for human-readable logs
 */
export const AUDIT_ACTION_DESCRIPTIONS: Record<AuditAction, string> = {
  [AuditAction.CREATE]: 'Created',
  [AuditAction.READ]: 'Viewed',
  [AuditAction.UPDATE]: 'Updated',
  [AuditAction.DELETE]: 'Deleted',
  [AuditAction.SHARE]: 'Shared',
  [AuditAction.UNSHARE]: 'Unshared',
  [AuditAction.LOGIN]: 'Logged in',
  [AuditAction.LOGOUT]: 'Logged out',
  [AuditAction.PASSWORD_CHANGE]: 'Changed password',
  [AuditAction.PERMISSION_GRANT]: 'Granted permission',
  [AuditAction.PERMISSION_REVOKE]: 'Revoked permission',
  [AuditAction.ROLE_ASSIGN]: 'Assigned role',
  [AuditAction.ROLE_UNASSIGN]: 'Unassigned role',
};

/**
 * Entity descriptions for human-readable logs
 */
export const AUDIT_ENTITY_DESCRIPTIONS: Record<AuditEntity, string> = {
  [AuditEntity.USER]: 'User',
  [AuditEntity.ACCOUNT]: 'Account',
  [AuditEntity.WALLET]: 'Wallet',
  [AuditEntity.TRANSACTION]: 'Transaction',
  [AuditEntity.CATEGORY]: 'Category',
  [AuditEntity.SUBCATEGORY]: 'Subcategory',
  [AuditEntity.ROLE]: 'Role',
  [AuditEntity.PERMISSION]: 'Permission',
  [AuditEntity.USER_SHARED_ACCESS]: 'Shared Access',
  [AuditEntity.KYC_VERIFICATION]: 'KYC Verification',
};

/**
 * Actions that should capture before/after values
 */
export const ACTIONS_WITH_BEFORE_AFTER = [
  AuditAction.UPDATE,
  AuditAction.DELETE,
  AuditAction.SHARE,
  AuditAction.UNSHARE,
  AuditAction.PERMISSION_GRANT,
  AuditAction.PERMISSION_REVOKE,
  AuditAction.ROLE_ASSIGN,
  AuditAction.ROLE_UNASSIGN,
];

/**
 * Actions that require special permission checking
 */
export const SENSITIVE_ACTIONS = [
  AuditAction.DELETE,
  AuditAction.SHARE,
  AuditAction.UNSHARE,
  AuditAction.PERMISSION_GRANT,
  AuditAction.PERMISSION_REVOKE,
  AuditAction.ROLE_ASSIGN,
  AuditAction.ROLE_UNASSIGN,
  AuditAction.PASSWORD_CHANGE,
];
