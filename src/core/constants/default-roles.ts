export interface DefaultRole {
  name: string;
  description: string;
  permissions: string[]; // Permission names
}

export const DEFAULT_SYSTEM_ROLES: DefaultRole[] = [
  {
    name: 'Owner',
    description: 'System owner with full access to all features and settings',
    permissions: [
      // All permissions - Owner has everything
      'create_users',
      'view_users',
      'edit_users',
      'delete_users',
      'create_accounts',
      'view_accounts',
      'edit_accounts',
      'delete_accounts',
      'create_wallets',
      'view_wallets',
      'edit_wallets',
      'delete_wallets',
      'create_categories',
      'view_categories',
      'edit_categories',
      'delete_categories',
      'create_subcategories',
      'view_subcategories',
      'edit_subcategories',
      'delete_subcategories',
      'create_transactions',
      'view_transactions',
      'edit_transactions',
      'delete_transactions',
      'create_roles',
      'view_roles',
      'edit_roles',
      'delete_roles',
      'create_permissions',
      'view_permissions',
      'edit_permissions',
      'delete_permissions',
      'manage_system_settings',
      'view_audit_logs',
      'manage_user_access',
      'export_data',
      'import_data',
      'view_reports',
      'create_reports',
      'export_reports',
    ],
  },
  {
    name: 'Manager',
    description: 'Manager with full access to all features and settings',
    permissions: [
      // All permissions - Manager has everything (same as Owner for now)
      'create_users',
      'view_users',
      'edit_users',
      'delete_users',
      'create_accounts',
      'view_accounts',
      'edit_accounts',
      'delete_accounts',
      'create_wallets',
      'view_wallets',
      'edit_wallets',
      'delete_wallets',
      'create_categories',
      'view_categories',
      'edit_categories',
      'delete_categories',
      'create_subcategories',
      'view_subcategories',
      'edit_subcategories',
      'delete_subcategories',
      'create_transactions',
      'view_transactions',
      'edit_transactions',
      'delete_transactions',
      'create_roles',
      'view_roles',
      'edit_roles',
      'delete_roles',
      'create_permissions',
      'view_permissions',
      'edit_permissions',
      'delete_permissions',
      'manage_system_settings',
      'view_audit_logs',
      'manage_user_access',
      'export_data',
      'import_data',
      'view_reports',
      'create_reports',
      'export_reports',
    ],
  },
  {
    name: 'Manager',
    description: 'Management access with most permissions except system-level operations',
    permissions: [
      // User management
      'view_users',
      'edit_users',
      // Account management
      'create_accounts',
      'view_accounts',
      'edit_accounts',
      'delete_accounts',
      // Wallet management
      'create_wallets',
      'view_wallets',
      'edit_wallets',
      'delete_wallets',
      // Category management
      'create_categories',
      'view_categories',
      'edit_categories',
      'delete_categories',
      'create_subcategories',
      'view_subcategories',
      'edit_subcategories',
      'delete_subcategories',
      // Transaction management
      'create_transactions',
      'view_transactions',
      'edit_transactions',
      'delete_transactions',
      // Role and permission viewing
      'view_roles',
      'view_permissions',
      // Audit access
      'view_audit_logs',
      // User access management
      'manage_user_access',
      // Reports
      'view_reports',
      'create_reports',
      'export_reports',
      // Data operations
      'export_data',
      'import_data',
    ],
  },
  {
    name: 'Viewer',
    description:
      'Read-only access to view data without modification capabilities',
    permissions: [
      // Only view permissions - no create, edit, or delete
      'view_users',
      'view_accounts',
      'view_wallets',
      'view_categories',
      'view_subcategories',
      'view_transactions',
      'view_roles',
      'view_permissions',
      'view_reports',
      // Conditional audit access - will be checked at runtime
      'view_audit_logs',
    ],
  },
];
