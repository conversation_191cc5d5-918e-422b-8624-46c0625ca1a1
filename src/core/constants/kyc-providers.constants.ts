/**
 * KYC Provider Constants
 * 
 * Centralized constants for KYC provider identifiers.
 * This ensures consistency across the application and makes it easy to change provider names.
 */

export const KYC_PROVIDERS = {
  PREMBLY: 'prembly',
  ZEEH: 'zeeh',
  // Add more providers here as needed
  // SMILE_IDENTITY: 'smile_identity',
  // YOUVERIFY: 'youverify',
  // INTERNAL: 'internal',
} as const;

// Type for provider IDs
export type KycProviderId = typeof KYC_PROVIDERS[keyof typeof KYC_PROVIDERS];

// Array of all available providers (useful for validation)
export const AVAILABLE_PROVIDERS = Object.values(KYC_PROVIDERS);

// Provider display names (for UI/logging purposes)
export const PROVIDER_DISPLAY_NAMES: Record<KycProviderId, string> = {
  [KYC_PROVIDERS.PREMBLY]: 'Prembly',
  [KYC_PROVIDERS.ZEEH]: 'Zeeh',
};

// Provider descriptions
export const PROVIDER_DESCRIPTIONS: Record<KycProviderId, string> = {
  [KYC_PROVIDERS.PREMBLY]: 'Prembly Identity Verification Service',
  [KYC_PROVIDERS.ZEEH]: 'Zeeh Identity Verification Service',
};

// Helper function to validate provider ID
export function isValidProviderId(providerId: string): providerId is KycProviderId {
  return AVAILABLE_PROVIDERS.includes(providerId as KycProviderId);
}

// Helper function to get provider display name
export function getProviderDisplayName(providerId: KycProviderId): string {
  return PROVIDER_DISPLAY_NAMES[providerId] || providerId;
}

// Helper function to get provider description
export function getProviderDescription(providerId: KycProviderId): string {
  return PROVIDER_DESCRIPTIONS[providerId] || `${getProviderDisplayName(providerId)} verification service`;
}
