import { randomUUID } from 'crypto';
import { AuditAction, AuditEntity } from '../constants/audit-actions';

export interface AuditContext {
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
}

export interface CreateAuditLogProps {
  userId: string;
  action: AuditAction;
  entity: AuditEntity;
  entityId: string;
  accountId?: string;
  walletId?: string;
  beforeValues?: Record<string, any>;
  afterValues?: Record<string, any>;
  metadata?: Record<string, any>;
  context?: AuditContext;
}

export class AuditLog {
  constructor(
    public readonly id: string,
    public readonly userId: string,
    public readonly action: AuditAction,
    public readonly entity: AuditEntity,
    public readonly entityId: string,
    public readonly accountId: string | null = null,
    public readonly walletId: string | null = null,
    public readonly beforeValues: Record<string, any> | null = null,
    public readonly afterValues: Record<string, any> | null = null,
    public readonly metadata: Record<string, any> | null = null,
    public readonly ipAddress: string | null = null,
    public readonly userAgent: string | null = null,
    public readonly sessionId: string | null = null,
    public readonly createdAt: Date = new Date(),
  ) {
    this.validate();
  }

  static create(props: CreateAuditLogProps): AuditLog {
    return new AuditLog(
      randomUUID(),
      props.userId,
      props.action,
      props.entity,
      props.entityId,
      props.accountId || null,
      props.walletId || null,
      props.beforeValues || null,
      props.afterValues || null,
      props.metadata || null,
      props.context?.ipAddress || null,
      props.context?.userAgent || null,
      props.context?.sessionId || null,
    );
  }

  /**
   * Create an audit log for a CREATE action
   */
  static createForCreate(
    userId: string,
    entity: AuditEntity,
    entityId: string,
    data: Record<string, any>,
    options?: {
      accountId?: string;
      walletId?: string;
      context?: AuditContext;
      metadata?: Record<string, any>;
    },
  ): AuditLog {
    return AuditLog.create({
      userId,
      action: AuditAction.CREATE,
      entity,
      entityId,
      accountId: options?.accountId,
      walletId: options?.walletId,
      afterValues: data,
      metadata: options?.metadata,
      context: options?.context,
    });
  }

  /**
   * Create an audit log for an UPDATE action
   */
  static createForUpdate(
    userId: string,
    entity: AuditEntity,
    entityId: string,
    beforeValues: Record<string, any>,
    afterValues: Record<string, any>,
    options?: {
      accountId?: string;
      walletId?: string;
      context?: AuditContext;
      metadata?: Record<string, any>;
    },
  ): AuditLog {
    return AuditLog.create({
      userId,
      action: AuditAction.UPDATE,
      entity,
      entityId,
      accountId: options?.accountId,
      walletId: options?.walletId,
      beforeValues,
      afterValues,
      metadata: options?.metadata,
      context: options?.context,
    });
  }

  /**
   * Create an audit log for a DELETE action
   */
  static createForDelete(
    userId: string,
    entity: AuditEntity,
    entityId: string,
    deletedData: Record<string, any>,
    options?: {
      accountId?: string;
      walletId?: string;
      context?: AuditContext;
      metadata?: Record<string, any>;
    },
  ): AuditLog {
    return AuditLog.create({
      userId,
      action: AuditAction.DELETE,
      entity,
      entityId,
      accountId: options?.accountId,
      walletId: options?.walletId,
      beforeValues: deletedData,
      metadata: options?.metadata,
      context: options?.context,
    });
  }

  /**
   * Create an audit log for authentication events
   */
  static createForAuth(
    userId: string,
    action: AuditAction.LOGIN | AuditAction.LOGOUT | AuditAction.PASSWORD_CHANGE,
    options?: {
      context?: AuditContext;
      metadata?: Record<string, any>;
    },
  ): AuditLog {
    return AuditLog.create({
      userId,
      action,
      entity: AuditEntity.USER,
      entityId: userId,
      metadata: options?.metadata,
      context: options?.context,
    });
  }

  /**
   * Create an audit log for sharing actions
   */
  static createForSharing(
    userId: string,
    action: AuditAction.SHARE | AuditAction.UNSHARE,
    targetUserId: string,
    resourceType: 'account' | 'wallet',
    resourceId: string,
    roleId: string,
    options?: {
      context?: AuditContext;
      metadata?: Record<string, any>;
    },
  ): AuditLog {
    const accountId = resourceType === 'account' ? resourceId : undefined;
    const walletId = resourceType === 'wallet' ? resourceId : undefined;

    return AuditLog.create({
      userId,
      action,
      entity: AuditEntity.USER_SHARED_ACCESS,
      entityId: `${targetUserId}-${resourceId}`,
      accountId,
      walletId,
      metadata: {
        targetUserId,
        resourceType,
        resourceId,
        roleId,
        ...options?.metadata,
      },
      context: options?.context,
    });
  }

  private validate(): void {
    if (!this.userId || this.userId.trim().length === 0) {
      throw new Error('User ID is required');
    }

    if (!this.entityId || this.entityId.trim().length === 0) {
      throw new Error('Entity ID is required');
    }

    // Validate that at least one resource identifier is provided for resource-specific actions
    if (this.requiresResourceContext() && !this.accountId && !this.walletId) {
      throw new Error(
        `Action ${this.action} requires either accountId or walletId`,
      );
    }
  }

  /**
   * Check if this action requires resource context (account or wallet)
   */
  private requiresResourceContext(): boolean {
    const resourceSpecificEntities = [
      AuditEntity.ACCOUNT,
      AuditEntity.WALLET,
      AuditEntity.TRANSACTION,
      AuditEntity.USER_SHARED_ACCESS,
    ];

    return resourceSpecificEntities.includes(this.entity);
  }

  /**
   * Get a human-readable description of this audit log
   */
  getDescription(): string {
    const actionDesc = this.action.toLowerCase();
    const entityDesc = this.entity.toLowerCase();
    return `${actionDesc} ${entityDesc}`;
  }

  /**
   * Check if this audit log contains sensitive information
   */
  isSensitive(): boolean {
    const sensitiveActions = [
      AuditAction.DELETE,
      AuditAction.PASSWORD_CHANGE,
      AuditAction.PERMISSION_GRANT,
      AuditAction.PERMISSION_REVOKE,
    ];

    return sensitiveActions.includes(this.action);
  }

  /**
   * Get the changes made (for UPDATE actions)
   */
  getChanges(): Record<string, { from: any; to: any }> | null {
    if (this.action !== AuditAction.UPDATE || !this.beforeValues || !this.afterValues) {
      return null;
    }

    const changes: Record<string, { from: any; to: any }> = {};
    const allKeys = new Set([
      ...Object.keys(this.beforeValues),
      ...Object.keys(this.afterValues),
    ]);

    for (const key of allKeys) {
      const beforeValue = this.beforeValues[key];
      const afterValue = this.afterValues[key];

      if (JSON.stringify(beforeValue) !== JSON.stringify(afterValue)) {
        changes[key] = {
          from: beforeValue,
          to: afterValue,
        };
      }
    }

    return Object.keys(changes).length > 0 ? changes : null;
  }
}
