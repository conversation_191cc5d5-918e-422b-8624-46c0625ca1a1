import { randomUUID } from 'crypto';

export enum DocumentType {
  // Identity Documents
  NIN = 'NIN',
  BVN = 'BVN',
  NATIONAL_ID = 'NATIONAL_ID',
  DRIVERS_LICENSE = 'DRIVERS_LICENSE',
  INTERNATIONAL_PASSPORT = 'INTERNATIONAL_PASSPORT',
  VOTERS_CARD = 'VOTERS_CARD',
  
  // Address Documents
  UTILITY_BILL = 'UTILITY_BILL',
  BANK_STATEMENT = 'BANK_STATEMENT',
  TENANCY_AGREEMENT = 'TENANCY_AGREEMENT',
  EMPLOYER_LETTER = 'EMPLOYER_LETTER',
  
  // Business Documents
  CAC = 'CAC',
  TIN = 'TIN',
  BUSINESS_PERMIT = 'BUSINESS_PERMIT',
  
  // Biometric
  FACIAL_IMAGE = 'FACIAL_IMAGE',
  SELFIE = 'SELFIE'
}

export enum VerificationStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  VERIFIED = 'VERIFIED',
  FAILED = 'FAILED',
  EXPIRED = 'EXPIRED',
  REQUIRES_MANUAL_REVIEW = 'REQUIRES_MANUAL_REVIEW'
}

export enum VerificationType {
  IDENTITY = 'IDENTITY',
  ADDRESS = 'ADDRESS',
  BUSINESS = 'BUSINESS',
  FACIAL = 'FACIAL'
}

export class KycVerification {
  constructor(
    public readonly id: string,
    public readonly userId: string,
    public readonly verificationType: VerificationType,
    public readonly documentType: DocumentType,
    public readonly status: VerificationStatus,
    public readonly countryCode: string,
    public readonly providerId: string,
    public readonly providerReference: string,
    public readonly documentNumber?: string,
    public readonly documentImageUrl?: string,
    public readonly verificationData?: Record<string, any>,
    public readonly providerResponse?: Record<string, any>,
    public readonly failureReason?: string,
    public readonly verifiedAt?: Date,
    public readonly expiresAt?: Date,
    public readonly createdAt: Date = new Date(),
    public readonly updatedAt: Date = new Date(),
    validate: boolean = true,
  ) {
    if (validate) {
      this.validate();
    }
  }

  static create(props: {
    userId: string;
    verificationType: VerificationType;
    documentType: DocumentType;
    countryCode: string;
    providerId: string;
    providerReference: string;
    documentNumber?: string;
    documentImageUrl?: string;
    verificationData?: Record<string, any>;
    expiresAt?: Date;
  }): KycVerification {
    return new KycVerification(
      randomUUID(),
      props.userId,
      props.verificationType,
      props.documentType,
      VerificationStatus.PENDING,
      props.countryCode,
      props.providerId,
      props.providerReference,
      props.documentNumber,
      props.documentImageUrl,
      props.verificationData,
      undefined, // providerResponse
      undefined, // failureReason
      undefined, // verifiedAt
      props.expiresAt,
      new Date(),
      new Date(),
      true,
    );
  }

  static fromPrisma(prismaVerification: any): KycVerification {
    return new KycVerification(
      prismaVerification.id,
      prismaVerification.userId,
      prismaVerification.verificationType as VerificationType,
      prismaVerification.documentType as DocumentType,
      prismaVerification.status as VerificationStatus,
      prismaVerification.countryCode,
      prismaVerification.providerId,
      prismaVerification.providerReference,
      prismaVerification.documentNumber,
      prismaVerification.documentImageUrl,
      prismaVerification.verificationData,
      prismaVerification.providerResponse,
      prismaVerification.failureReason,
      prismaVerification.verifiedAt,
      prismaVerification.expiresAt,
      prismaVerification.createdAt,
      prismaVerification.updatedAt,
      false,
    );
  }

  private validate(): void {
    if (!this.userId || this.userId.trim().length === 0) {
      throw new Error('User ID is required');
    }

    if (!this.verificationType) {
      throw new Error('Verification type is required');
    }

    if (!this.documentType) {
      throw new Error('Document type is required');
    }

    if (!this.countryCode || !/^\d{1,4}$/.test(this.countryCode)) {
      throw new Error('Valid country code is required');
    }

    if (!this.providerId || this.providerId.trim().length === 0) {
      throw new Error('Provider ID is required');
    }

    if (!this.providerReference || this.providerReference.trim().length === 0) {
      throw new Error('Provider reference is required');
    }

    // Validate document type matches verification type
    this.validateDocumentTypeCompatibility();
  }

  private validateDocumentTypeCompatibility(): void {
    const identityDocs = [DocumentType.NIN, DocumentType.BVN, DocumentType.DRIVERS_LICENSE, 
                         DocumentType.INTERNATIONAL_PASSPORT, DocumentType.VOTERS_CARD];
    const addressDocs = [DocumentType.UTILITY_BILL, DocumentType.BANK_STATEMENT, 
                        DocumentType.TENANCY_AGREEMENT, DocumentType.EMPLOYER_LETTER];
    const businessDocs = [DocumentType.CAC, DocumentType.TIN, DocumentType.BUSINESS_PERMIT];
    const facialDocs = [DocumentType.FACIAL_IMAGE, DocumentType.SELFIE];

    switch (this.verificationType) {
      case VerificationType.IDENTITY:
        if (!identityDocs.includes(this.documentType)) {
          throw new Error(`Document type ${this.documentType} is not valid for identity verification`);
        }
        break;
      case VerificationType.ADDRESS:
        if (!addressDocs.includes(this.documentType)) {
          throw new Error(`Document type ${this.documentType} is not valid for address verification`);
        }
        break;
      case VerificationType.BUSINESS:
        if (!businessDocs.includes(this.documentType)) {
          throw new Error(`Document type ${this.documentType} is not valid for business verification`);
        }
        break;
      case VerificationType.FACIAL:
        if (!facialDocs.includes(this.documentType)) {
          throw new Error(`Document type ${this.documentType} is not valid for facial verification`);
        }
        break;
    }
  }

  get isVerified(): boolean {
    return this.status === VerificationStatus.VERIFIED;
  }

  get isPending(): boolean {
    return this.status === VerificationStatus.PENDING || this.status === VerificationStatus.IN_PROGRESS;
  }

  get isFailed(): boolean {
    return this.status === VerificationStatus.FAILED;
  }

  get isExpired(): boolean {
    return this.expiresAt ? new Date() > this.expiresAt : false;
  }

  get requiresManualReview(): boolean {
    return this.status === VerificationStatus.REQUIRES_MANUAL_REVIEW;
  }

  markAsVerified(providerResponse: Record<string, any>, verificationData?: Record<string, any>): KycVerification {
    return new KycVerification(
      this.id,
      this.userId,
      this.verificationType,
      this.documentType,
      VerificationStatus.VERIFIED,
      this.countryCode,
      this.providerId,
      this.providerReference,
      this.documentNumber,
      this.documentImageUrl,
      verificationData || this.verificationData,
      providerResponse,
      undefined, // clear failure reason
      new Date(), // verifiedAt
      this.expiresAt,
      this.createdAt,
      new Date(), // updatedAt
      false,
    );
  }

  markAsFailed(failureReason: string, providerResponse?: Record<string, any>): KycVerification {
    return new KycVerification(
      this.id,
      this.userId,
      this.verificationType,
      this.documentType,
      VerificationStatus.FAILED,
      this.countryCode,
      this.providerId,
      this.providerReference,
      this.documentNumber,
      this.documentImageUrl,
      this.verificationData,
      providerResponse || this.providerResponse,
      failureReason,
      this.verifiedAt,
      this.expiresAt,
      this.createdAt,
      new Date(), // updatedAt
      false,
    );
  }

  markAsInProgress(): KycVerification {
    return new KycVerification(
      this.id,
      this.userId,
      this.verificationType,
      this.documentType,
      VerificationStatus.IN_PROGRESS,
      this.countryCode,
      this.providerId,
      this.providerReference,
      this.documentNumber,
      this.documentImageUrl,
      this.verificationData,
      this.providerResponse,
      this.failureReason,
      this.verifiedAt,
      this.expiresAt,
      this.createdAt,
      new Date(), // updatedAt
      false,
    );
  }

  markForManualReview(reason: string): KycVerification {
    return new KycVerification(
      this.id,
      this.userId,
      this.verificationType,
      this.documentType,
      VerificationStatus.REQUIRES_MANUAL_REVIEW,
      this.countryCode,
      this.providerId,
      this.providerReference,
      this.documentNumber,
      this.documentImageUrl,
      this.verificationData,
      this.providerResponse,
      reason,
      this.verifiedAt,
      this.expiresAt,
      this.createdAt,
      new Date(), // updatedAt
      false,
    );
  }
}
