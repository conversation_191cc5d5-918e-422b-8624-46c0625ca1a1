import { AuditLog } from '../../entities/audit-log.entity';
import { AuditAction, AuditEntity } from '../../constants/audit-actions';

export interface AuditLogFilters {
  userId?: string;
  accountId?: string;
  walletId?: string;
  entity?: AuditEntity;
  action?: AuditAction;
  entityId?: string;
  startDate?: Date;
  endDate?: Date;
  ipAddress?: string;
}

export interface AuditLogPagination {
  page: number;
  limit: number;
  sortBy?: 'createdAt' | 'action' | 'entity';
  sortOrder?: 'asc' | 'desc';
}

export interface AuditLogQueryResult {
  auditLogs: AuditLog[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface AuditLogRepositoryPort {
  /**
   * Save an audit log entry
   */
  save(auditLog: AuditLog): Promise<AuditLog>;

  /**
   * Find audit log by ID
   */
  findById(id: string): Promise<AuditLog | null>;

  /**
   * Find audit logs with filters and pagination
   */
  findMany(
    filters: AuditLogFilters,
    pagination: AuditLogPagination,
  ): Promise<AuditLogQueryResult>;

  /**
   * Find audit logs for a specific user with permission filtering
   * This method should respect the user's access to accounts/wallets
   */
  findForUser(
    requestingUserId: string,
    filters: AuditLogFilters,
    pagination: AuditLogPagination,
    accessibleAccountIds?: string[],
    accessibleWalletIds?: string[],
  ): Promise<AuditLogQueryResult>;

  /**
   * Find audit logs for a specific account
   */
  findByAccountId(
    accountId: string,
    filters?: Omit<AuditLogFilters, 'accountId'>,
    pagination?: AuditLogPagination,
  ): Promise<AuditLogQueryResult>;

  /**
   * Find audit logs for a specific wallet
   */
  findByWalletId(
    walletId: string,
    filters?: Omit<AuditLogFilters, 'walletId'>,
    pagination?: AuditLogPagination,
  ): Promise<AuditLogQueryResult>;

  /**
   * Find audit logs for a specific entity
   */
  findByEntity(
    entity: AuditEntity,
    entityId: string,
    filters?: Omit<AuditLogFilters, 'entity' | 'entityId'>,
    pagination?: AuditLogPagination,
  ): Promise<AuditLogQueryResult>;

  /**
   * Count audit logs matching filters
   */
  count(filters: AuditLogFilters): Promise<number>;

  /**
   * Delete old audit logs (for data retention)
   */
  deleteOlderThan(date: Date): Promise<number>;

  /**
   * Get audit log statistics
   */
  getStatistics(
    filters: AuditLogFilters,
  ): Promise<{
    totalLogs: number;
    actionBreakdown: Record<AuditAction, number>;
    entityBreakdown: Record<AuditEntity, number>;
    dailyActivity: Array<{ date: string; count: number }>;
  }>;
}

export const AUDIT_LOG_REPOSITORY = Symbol('AuditLogRepository');
