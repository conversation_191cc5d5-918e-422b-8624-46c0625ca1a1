import { DocumentType } from '../../entities/kyc-verification.entity';

export interface DocumentUploadRequest {
  file: Buffer | string; // File buffer or base64 string
  fileName: string;
  mimeType: string;
  documentType: DocumentType;
  userId: string;
  metadata?: Record<string, any>;
}

export interface DocumentUploadResult {
  success: boolean;
  documentUrl: string;
  publicId: string;
  secureUrl: string;
  format: string;
  size: number;
  width?: number;
  height?: number;
  metadata?: Record<string, any>;
  errorMessage?: string;
}

export interface DocumentDeleteRequest {
  publicId: string;
  documentUrl?: string;
}

export interface DocumentDeleteResult {
  success: boolean;
  publicId: string;
  errorMessage?: string;
}

export interface DocumentTransformOptions {
  width?: number;
  height?: number;
  quality?: number;
  format?: 'jpg' | 'png' | 'pdf' | 'webp';
  crop?: 'fill' | 'fit' | 'scale' | 'crop';
  gravity?: 'auto' | 'center' | 'face' | 'faces';
}

export interface DocumentAccessOptions {
  expiresIn?: number; // seconds
  secure?: boolean;
  transformation?: DocumentTransformOptions;
}

export interface SecureDocumentUrl {
  url: string;
  expiresAt: Date;
  isSecure: boolean;
}

export const DOCUMENT_STORAGE_PORT = Symbol('DOCUMENT_STORAGE_PORT');

export interface DocumentStoragePort {
  // Upload operations
  uploadDocument(request: DocumentUploadRequest): Promise<DocumentUploadResult>;
  uploadMultipleDocuments(requests: DocumentUploadRequest[]): Promise<DocumentUploadResult[]>;

  // Download/Access operations
  getDocumentUrl(publicId: string, options?: DocumentAccessOptions): Promise<string>;
  getSecureDocumentUrl(publicId: string, options?: DocumentAccessOptions): Promise<SecureDocumentUrl>;
  downloadDocument(publicId: string): Promise<Buffer>;

  // Transformation operations
  transformDocument(publicId: string, options: DocumentTransformOptions): Promise<string>;
  generateThumbnail(publicId: string, width?: number, height?: number): Promise<string>;

  // Management operations
  deleteDocument(request: DocumentDeleteRequest): Promise<DocumentDeleteResult>;
  deleteMultipleDocuments(requests: DocumentDeleteRequest[]): Promise<DocumentDeleteResult[]>;

  // Metadata operations
  getDocumentMetadata(publicId: string): Promise<Record<string, any>>;
  updateDocumentMetadata(publicId: string, metadata: Record<string, any>): Promise<boolean>;

  // Validation operations
  validateDocument(publicId: string): Promise<boolean>;
  getDocumentInfo(publicId: string): Promise<{
    publicId: string;
    format: string;
    size: number;
    width?: number;
    height?: number;
    createdAt: Date;
    metadata?: Record<string, any>;
  }>;

  // Security operations
  generateSignedUploadUrl(options: {
    documentType: DocumentType;
    userId: string;
    expiresIn?: number;
    maxFileSize?: number;
    allowedFormats?: string[];
  }): Promise<{
    uploadUrl: string;
    uploadParams: Record<string, any>;
    expiresAt: Date;
  }>;

  // Cleanup operations
  deleteExpiredDocuments(): Promise<number>;
  deleteUserDocuments(userId: string): Promise<number>;
}
