import { DocumentType } from '../../entities/kyc-verification.entity';

export interface VerificationRequest {
  documentType: DocumentType;
  documentNumber: string;
  documentImageUrl?: string;
  firstName?: string;
  lastName?: string;
  dateOfBirth?: string;
  phoneNumber?: string;
  additionalData?: Record<string, any>;
}

export interface BvnVerificationRequest extends VerificationRequest {
  documentType: DocumentType.BVN;
  documentNumber: string; // 11-digit BVN
  firstName?: string;
  lastName?: string;
  dateOfBirth?: string;
  phoneNumber?: string;
}

export interface NinVerificationRequest extends VerificationRequest {
  documentType: DocumentType.NIN;
  documentNumber: string; // 11-digit NIN
  firstName?: string;
  lastName?: string;
  dateOfBirth?: string;
}

export interface NationalIdVerificationRequest extends VerificationRequest {
  documentType: DocumentType.NATIONAL_ID;
  documentNumber: string;
  firstName?: string;
  lastName?: string;
  dateOfBirth?: string;
}

export interface DriversLicenseVerificationRequest extends VerificationRequest {
  documentType: DocumentType.DRIVERS_LICENSE;
  documentNumber: string;
  documentImageUrl: string; // Required for drivers license
  firstName?: string;
  lastName?: string;
  dateOfBirth?: string;
}

export interface PassportVerificationRequest extends VerificationRequest {
  documentType: DocumentType.INTERNATIONAL_PASSPORT;
  documentNumber: string;
  documentImageUrl: string; // Required for passport
  firstName?: string;
  lastName?: string;
  dateOfBirth?: string;
}

export interface VotersCardVerificationRequest extends VerificationRequest {
  documentType: DocumentType.VOTERS_CARD;
  documentNumber: string;
  documentImageUrl: string; // Required for voters card
  firstName?: string;
  lastName?: string;
}

export interface UtilityBillVerificationRequest extends VerificationRequest {
  documentType: DocumentType.UTILITY_BILL;
  documentImageUrl: string; // Required for utility bill
  address: string;
  firstName?: string;
  lastName?: string;
}

export interface BankStatementVerificationRequest extends VerificationRequest {
  documentType: DocumentType.BANK_STATEMENT;
  documentImageUrl: string; // Required for bank statement
  address: string;
  firstName?: string;
  lastName?: string;
}

export interface CacVerificationRequest extends VerificationRequest {
  documentType: DocumentType.CAC;
  documentNumber: string; // RC number
  companyName: string;
  documentImageUrl?: string;
}

export interface TinVerificationRequest extends VerificationRequest {
  documentType: DocumentType.TIN;
  documentNumber: string; // TIN number
  companyName?: string;
  firstName?: string;
  lastName?: string;
}

export interface FacialVerificationRequest extends VerificationRequest {
  documentType: DocumentType.FACIAL_IMAGE | DocumentType.SELFIE;
  documentImageUrl: string; // Required for facial verification
  referenceImageUrl?: string; // For comparison
  firstName?: string;
  lastName?: string;
}

export interface VerificationResult {
  success: boolean;
  verified: boolean;
  confidence?: number;
  providerReference: string;
  providerResponse: Record<string, any>;
  extractedData?: {
    firstName?: string;
    lastName?: string;
    dateOfBirth?: string;
    phoneNumber?: string;
    address?: string;
    gender?: string;
    nationality?: string;
    issueDate?: string;
    expiryDate?: string;
    [key: string]: any;
  };
  failureReason?: string;
  requiresManualReview?: boolean;
}

export interface ProviderHealthStatus {
  isHealthy: boolean;
  responseTime?: number;
  lastChecked: Date;
  errorMessage?: string;
}

export const KYC_PROVIDER_PORT = Symbol('KYC_PROVIDER_PORT');

export interface KycProviderPort {
  readonly providerId: string;
  readonly supportedDocuments: DocumentType[];
  readonly supportedCountries: string[];

  // Health check
  checkHealth(): Promise<ProviderHealthStatus>;

  // Identity verification methods
  verifyBvn(request: BvnVerificationRequest): Promise<VerificationResult>;
  verifyNin(request: NinVerificationRequest): Promise<VerificationResult>;
  verifyDriversLicense(request: DriversLicenseVerificationRequest): Promise<VerificationResult>;
  verifyInternationalPassport(request: PassportVerificationRequest): Promise<VerificationResult>;
  verifyVotersCard(request: VotersCardVerificationRequest): Promise<VerificationResult>;

  // Address verification methods
  verifyUtilityBill(request: UtilityBillVerificationRequest): Promise<VerificationResult>;
  verifyBankStatement(request: BankStatementVerificationRequest): Promise<VerificationResult>;

  // Business verification methods
  verifyCac(request: CacVerificationRequest): Promise<VerificationResult>;
  verifyTin(request: TinVerificationRequest): Promise<VerificationResult>;

  // Facial verification methods
  verifyFacial(request: FacialVerificationRequest): Promise<VerificationResult>;

  // Generic verification method
  verify(request: VerificationRequest): Promise<VerificationResult>;

  // Webhook verification (for async results)
  verifyWebhookSignature?(payload: string, signature: string): boolean;
  parseWebhookPayload?(payload: any): {
    providerReference: string;
    status: 'verified' | 'failed' | 'requires_review';
    result: VerificationResult;
  };
}
