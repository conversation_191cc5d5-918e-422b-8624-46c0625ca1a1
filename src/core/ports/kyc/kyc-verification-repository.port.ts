import { KycVerification, DocumentType, VerificationStatus, VerificationType } from '../../entities/kyc-verification.entity';

export interface KycVerificationFilters {
  userId?: string;
  documentType?: DocumentType;
  verificationType?: VerificationType;
  status?: VerificationStatus;
  countryCode?: string;
  providerId?: string;
  createdAfter?: Date;
  createdBefore?: Date;
  verifiedAfter?: Date;
  verifiedBefore?: Date;
}

export interface KycVerificationSummary {
  userId: string;
  totalVerifications: number;
  verifiedCount: number;
  pendingCount: number;
  failedCount: number;
  identityVerified: boolean;
  addressVerified: boolean;
  businessVerified: boolean;
  facialVerified: boolean;
  overallKycStatus: 'incomplete' | 'partial' | 'complete';
  lastVerificationDate?: Date;
}

export const KYC_VERIFICATION_REPOSITORY = Symbol('KYC_VERIFICATION_REPOSITORY');

export interface KycVerificationRepositoryPort {
  // Basic CRUD operations
  save(verification: KycVerification): Promise<KycVerification>;
  findById(id: string): Promise<KycVerification | null>;
  findByProviderReference(providerReference: string): Promise<KycVerification | null>;
  update(verification: KycVerification): Promise<KycVerification>;
  delete(id: string): Promise<void>;

  // Query operations
  findByUserId(userId: string): Promise<KycVerification[]>;
  findByUserIdAndDocumentType(userId: string, documentType: DocumentType): Promise<KycVerification[]>;
  findByUserIdAndVerificationType(userId: string, verificationType: VerificationType): Promise<KycVerification[]>;
  findByFilters(filters: KycVerificationFilters): Promise<KycVerification[]>;

  // Status-specific queries
  findPendingVerifications(): Promise<KycVerification[]>;
  findExpiredVerifications(): Promise<KycVerification[]>;
  findVerificationsRequiringManualReview(): Promise<KycVerification[]>;

  // User-specific queries
  getUserKycSummary(userId: string): Promise<KycVerificationSummary>;
  getUserLatestVerification(userId: string, documentType: DocumentType): Promise<KycVerification | null>;
  getUserVerifiedDocuments(userId: string): Promise<DocumentType[]>;

  // Analytics and reporting
  getVerificationCountByStatus(status: VerificationStatus): Promise<number>;
  getVerificationCountByProvider(providerId: string): Promise<number>;
  getVerificationCountByCountry(countryCode: string): Promise<number>;
  getVerificationCountByDocumentType(documentType: DocumentType): Promise<number>;

  // Bulk operations
  markExpiredVerifications(): Promise<number>;
  deleteOldVerifications(olderThan: Date): Promise<number>;

  // Provider-specific queries
  findByProviderId(providerId: string): Promise<KycVerification[]>;
  getProviderSuccessRate(providerId: string, fromDate?: Date): Promise<number>;
}
