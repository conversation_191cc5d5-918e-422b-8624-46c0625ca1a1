import { Injectable } from '@nestjs/common';
import { AuditAction, AuditEntity } from '../constants/audit-actions';

interface AuditKey {
  userId: string;
  action: AuditAction;
  entity: AuditEntity;
  entityId: string;
  timestamp: number; // Unix timestamp in seconds
}

@Injectable()
export class AuditDeduplicationService {
  private recentAudits = new Map<string, number>();
  private readonly DEDUPLICATION_WINDOW_MS = 5000; // 5 seconds
  private readonly CLEANUP_INTERVAL_MS = 60000; // 1 minute

  constructor() {
    // Periodically clean up old entries
    setInterval(() => {
      this.cleanup();
    }, this.CLEANUP_INTERVAL_MS);
  }

  /**
   * Check if an audit log should be skipped due to recent duplicate
   */
  shouldSkipAudit(
    userId: string,
    action: AuditAction,
    entity: AuditEntity,
    entityId: string,
  ): boolean {
    const now = Date.now();
    const key = this.generateKey(userId, action, entity, entityId, now);
    
    const lastAuditTime = this.recentAudits.get(key);
    
    if (lastAuditTime && (now - lastAuditTime) < this.DEDUPLICATION_WINDOW_MS) {
      // Duplicate detected within the time window
      return true;
    }

    // Record this audit
    this.recentAudits.set(key, now);
    return false;
  }

  /**
   * Generate a unique key for audit deduplication
   */
  private generateKey(
    userId: string,
    action: AuditAction,
    entity: AuditEntity,
    entityId: string,
    timestamp: number,
  ): string {
    // Round timestamp to seconds to allow for small timing differences
    const roundedTimestamp = Math.floor(timestamp / 1000);
    return `${userId}:${action}:${entity}:${entityId}:${roundedTimestamp}`;
  }

  /**
   * Clean up old entries to prevent memory leaks
   */
  private cleanup(): void {
    const now = Date.now();
    const cutoff = now - (this.DEDUPLICATION_WINDOW_MS * 2); // Keep entries for 2x the window

    for (const [key, timestamp] of this.recentAudits.entries()) {
      if (timestamp < cutoff) {
        this.recentAudits.delete(key);
      }
    }
  }

  /**
   * Clear all deduplication data (useful for testing)
   */
  clear(): void {
    this.recentAudits.clear();
  }
}
