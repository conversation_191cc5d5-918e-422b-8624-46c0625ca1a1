import { Injectable, Inject } from '@nestjs/common';
import { AuditLog, AuditContext, CreateAuditLogProps } from '../entities/audit-log.entity';
import { AuditAction, AuditEntity } from '../constants/audit-actions';
import {
  AuditLogRepositoryPort,
  AUDIT_LOG_REPOSITORY,
} from '../ports/audit/audit-log-repository.port';
import { AuditDeduplicationService } from './audit-deduplication.service';

export interface AuditServiceOptions {
  accountId?: string;
  walletId?: string;
  context?: AuditContext;
  metadata?: Record<string, any>;
}

@Injectable()
export class AuditService {
  constructor(
    @Inject(AUDIT_LOG_REPOSITORY)
    private readonly auditLogRepository: AuditLogRepositoryPort,
    private readonly auditDeduplicationService: AuditDeduplicationService,
  ) {}

  /**
   * Log a CREATE action
   */
  async logCreate(
    userId: string,
    entity: AuditEntity,
    entityId: string,
    data: Record<string, any>,
    options?: AuditServiceOptions,
  ): Promise<void> {
    // Check for duplicate audit logs
    if (this.auditDeduplicationService.shouldSkipAudit(userId, AuditAction.CREATE, entity, entityId)) {
      return; // Skip duplicate audit log
    }

    const auditLog = AuditLog.createForCreate(
      userId,
      entity,
      entityId,
      data,
      options,
    );

    await this.auditLogRepository.save(auditLog);
  }

  /**
   * Log an UPDATE action
   */
  async logUpdate(
    userId: string,
    entity: AuditEntity,
    entityId: string,
    beforeValues: Record<string, any>,
    afterValues: Record<string, any>,
    options?: AuditServiceOptions,
  ): Promise<void> {
    const auditLog = AuditLog.createForUpdate(
      userId,
      entity,
      entityId,
      beforeValues,
      afterValues,
      options,
    );

    await this.auditLogRepository.save(auditLog);
  }

  /**
   * Log a DELETE action
   */
  async logDelete(
    userId: string,
    entity: AuditEntity,
    entityId: string,
    deletedData: Record<string, any>,
    options?: AuditServiceOptions,
  ): Promise<void> {
    const auditLog = AuditLog.createForDelete(
      userId,
      entity,
      entityId,
      deletedData,
      options,
    );

    await this.auditLogRepository.save(auditLog);
  }

  /**
   * Log a READ action (for sensitive data access)
   */
  async logRead(
    userId: string,
    entity: AuditEntity,
    entityId: string,
    options?: AuditServiceOptions,
  ): Promise<void> {
    const auditLog = AuditLog.create({
      userId,
      action: AuditAction.READ,
      entity,
      entityId,
      accountId: options?.accountId,
      walletId: options?.walletId,
      metadata: options?.metadata,
      context: options?.context,
    });

    await this.auditLogRepository.save(auditLog);
  }

  /**
   * Log authentication events
   */
  async logAuth(
    userId: string,
    action: AuditAction.LOGIN | AuditAction.LOGOUT | AuditAction.PASSWORD_CHANGE,
    context?: AuditContext,
    metadata?: Record<string, any>,
  ): Promise<void> {
    const auditLog = AuditLog.createForAuth(userId, action, {
      context,
      metadata,
    });

    await this.auditLogRepository.save(auditLog);
  }

  /**
   * Log sharing actions
   */
  async logSharing(
    userId: string,
    action: AuditAction.SHARE | AuditAction.UNSHARE,
    targetUserId: string,
    resourceType: 'account' | 'wallet',
    resourceId: string,
    roleId: string,
    context?: AuditContext,
    metadata?: Record<string, any>,
  ): Promise<void> {
    const auditLog = AuditLog.createForSharing(
      userId,
      action,
      targetUserId,
      resourceType,
      resourceId,
      roleId,
      { context, metadata },
    );

    await this.auditLogRepository.save(auditLog);
  }

  /**
   * Log permission changes
   */
  async logPermissionChange(
    userId: string,
    action: AuditAction.PERMISSION_GRANT | AuditAction.PERMISSION_REVOKE,
    targetUserId: string,
    permissionName: string,
    resourceType?: 'account' | 'wallet',
    resourceId?: string,
    context?: AuditContext,
    metadata?: Record<string, any>,
  ): Promise<void> {
    const auditLog = AuditLog.create({
      userId,
      action,
      entity: AuditEntity.PERMISSION,
      entityId: `${targetUserId}-${permissionName}`,
      accountId: resourceType === 'account' ? resourceId : undefined,
      walletId: resourceType === 'wallet' ? resourceId : undefined,
      metadata: {
        targetUserId,
        permissionName,
        resourceType,
        resourceId,
        ...metadata,
      },
      context,
    });

    await this.auditLogRepository.save(auditLog);
  }

  /**
   * Log role assignment changes
   */
  async logRoleChange(
    userId: string,
    action: AuditAction.ROLE_ASSIGN | AuditAction.ROLE_UNASSIGN,
    targetUserId: string,
    roleId: string,
    roleName: string,
    resourceType?: 'account' | 'wallet',
    resourceId?: string,
    context?: AuditContext,
    metadata?: Record<string, any>,
  ): Promise<void> {
    const auditLog = AuditLog.create({
      userId,
      action,
      entity: AuditEntity.ROLE,
      entityId: `${targetUserId}-${roleId}`,
      accountId: resourceType === 'account' ? resourceId : undefined,
      walletId: resourceType === 'wallet' ? resourceId : undefined,
      metadata: {
        targetUserId,
        roleId,
        roleName,
        resourceType,
        resourceId,
        ...metadata,
      },
      context,
    });

    await this.auditLogRepository.save(auditLog);
  }

  /**
   * Log a custom action
   */
  async logCustom(props: CreateAuditLogProps): Promise<void> {
    const auditLog = AuditLog.create(props);
    await this.auditLogRepository.save(auditLog);
  }

  /**
   * Extract audit context from HTTP request
   */
  extractContextFromRequest(request: any): AuditContext {
    return {
      ipAddress: this.getClientIp(request),
      userAgent: request.headers['user-agent'],
      sessionId: request.session?.id || request.headers['x-session-id'],
    };
  }

  /**
   * Get client IP address from request
   */
  private getClientIp(request: any): string {
    return (
      request.headers['x-forwarded-for']?.split(',')[0] ||
      request.headers['x-real-ip'] ||
      request.connection?.remoteAddress ||
      request.socket?.remoteAddress ||
      request.ip ||
      'unknown'
    );
  }

  /**
   * Sanitize sensitive data before logging
   */
  sanitizeData(data: Record<string, any>): Record<string, any> {
    const sensitiveFields = [
      'password',
      'passwordHash',
      'token',
      'secret',
      'key',
      'pin',
      'pinHash',
      'appPinHash',
      'refreshToken',
      'refreshTokenHash',
    ];

    const sanitized = { ...data };

    for (const field of sensitiveFields) {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]';
      }
    }

    return sanitized;
  }

  /**
   * Get changes between two objects
   */
  getChanges(
    before: Record<string, any>,
    after: Record<string, any>,
  ): Record<string, { from: any; to: any }> {
    const changes: Record<string, { from: any; to: any }> = {};
    const allKeys = new Set([...Object.keys(before), ...Object.keys(after)]);

    for (const key of allKeys) {
      const beforeValue = before[key];
      const afterValue = after[key];

      if (JSON.stringify(beforeValue) !== JSON.stringify(afterValue)) {
        changes[key] = {
          from: beforeValue,
          to: afterValue,
        };
      }
    }

    return changes;
  }
}
