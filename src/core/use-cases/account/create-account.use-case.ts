import { Inject, Injectable } from '@nestjs/common';
import { Account } from '../../entities/account.entity';
import {
  ACCOUNT_REPOSITORY,
  AccountRepositoryPort,
} from '../../ports/account/account-repository.port';
import { AuditService } from '../../services/audit.service';
import { AuditEntity } from '../../constants/audit-actions';

export interface CreateAccountInput {
  userId: string;
  name: string;
  auditContext?: {
    ipAddress?: string;
    userAgent?: string;
    sessionId?: string;
  };
}

@Injectable()
export class CreateAccountUseCase {
  constructor(
    @Inject(ACCOUNT_REPOSITORY)
    private readonly accountRepository: AccountRepositoryPort,
    private readonly auditService: AuditService,
  ) {}

  async execute(input: CreateAccountInput): Promise<Account> {
    const account = Account.create({
      userId: input.userId,
      name: input.name,
    });

    const createdAccount = await this.accountRepository.create(account);

    // Log the account creation
    await this.auditService.logCreate(
      input.userId,
      AuditEntity.ACCOUNT,
      createdAccount.id,
      this.auditService.sanitizeData({
        name: createdAccount.name,
        userId: createdAccount.userId,
      }),
      {
        accountId: createdAccount.id,
        context: input.auditContext,
        metadata: {
          description: 'Account created',
        },
      },
    );

    return createdAccount;
  }
}
