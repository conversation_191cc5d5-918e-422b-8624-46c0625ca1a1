import { Inject, Injectable } from '@nestjs/common';
import { Account } from '../../entities/account.entity';
import {
  ACCOUNT_REPOSITORY,
  AccountRepositoryPort,
} from '../../ports/account/account-repository.port';
// AuditService removed - using automatic audit decorators in controller

export interface CreateAccountInput {
  userId: string;
  name: string;
  auditContext?: {
    ipAddress?: string;
    userAgent?: string;
    sessionId?: string;
  };
}

@Injectable()
export class CreateAccountUseCase {
  constructor(
    @Inject(ACCOUNT_REPOSITORY)
    private readonly accountRepository: AccountRepositoryPort,
  ) {}

  async execute(input: CreateAccountInput): Promise<Account> {
    const account = Account.create({
      userId: input.userId,
      name: input.name,
    });

    return this.accountRepository.create(account);
    // Audit logging is handled automatically by the @AuditCreate decorator
  }
}
