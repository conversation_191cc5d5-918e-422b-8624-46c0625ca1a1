import { Inject, Injectable } from '@nestjs/common';
import { Account } from '../../entities/account.entity';
import {
  ACCOUNT_REPOSITORY,
  AccountRepositoryPort,
} from '../../ports/account/account-repository.port';
import { AuditService } from '../../services/audit.service';
import { AuditEntity } from '../../constants/audit-actions';

export interface UpdateAccountInput {
  id: string;
  name: string;
  userId: string; // User performing the update
  auditContext?: {
    ipAddress?: string;
    userAgent?: string;
    sessionId?: string;
  };
}

@Injectable()
export class UpdateAccountUseCase {
  constructor(
    @Inject(ACCOUNT_REPOSITORY)
    private readonly accountRepository: AccountRepositoryPort,
    private readonly auditService: AuditService,
  ) {}

  async execute(input: UpdateAccountInput): Promise<Account> {
    const existingAccount = await this.accountRepository.findById(input.id);
    if (!existingAccount) {
      throw new Error(`Account with id '${input.id}' not found`);
    }

    // Capture before values for audit
    const beforeValues = this.auditService.sanitizeData({
      name: existingAccount.name,
    });

    const updatedAccount = existingAccount.updateName(input.name);
    const savedAccount = await this.accountRepository.update(updatedAccount);

    // Capture after values for audit
    const afterValues = this.auditService.sanitizeData({
      name: savedAccount.name,
    });

    // Log the account update
    await this.auditService.logUpdate(
      input.userId,
      AuditEntity.ACCOUNT,
      savedAccount.id,
      beforeValues,
      afterValues,
      {
        accountId: savedAccount.id,
        context: input.auditContext,
        metadata: {
          description: 'Account updated',
        },
      },
    );

    return savedAccount;
  }
}
