import { Injectable, Inject } from '@nestjs/common';
import {
  UserSharedAccessRepositoryPort,
  USER_SHARED_ACCESS_REPOSITORY,
} from '../../ports/sharing/user-shared-access-repository.port';
import {
  AccountRepositoryPort,
  ACCOUNT_REPOSITORY,
} from '../../ports/account/account-repository.port';
import {
  WalletRepositoryPort,
  WALLET_REPOSITORY,
} from '../../ports/wallet/wallet-repository.port';

export interface CheckAuditAccessInput {
  userId: string;
  targetAccountId?: string;
  targetWalletId?: string;
}

export interface CheckAuditAccessOutput {
  hasAccess: boolean;
  accessibleAccountIds: string[];
  accessibleWalletIds: string[];
  isOwner: boolean;
  hasAuditPermission: boolean;
}

@Injectable()
export class CheckAuditAccessUseCase {
  constructor(
    @Inject(USER_SHARED_ACCESS_REPOSITORY)
    private readonly userSharedAccessRepository: UserSharedAccessRepositoryPort,
    @Inject(ACCOUNT_REPOSITORY)
    private readonly accountRepository: AccountRepositoryPort,
    @Inject(WALLET_REPOSITORY)
    private readonly walletRepository: WalletRepositoryPort,
  ) {}

  async execute(input: CheckAuditAccessInput): Promise<CheckAuditAccessOutput> {
    const { userId, targetAccountId, targetWalletId } = input;

    // Get user's permissions for audit logs
    const hasAuditPermission = await this.checkAuditPermission(userId);

    if (!hasAuditPermission) {
      return {
        hasAccess: false,
        accessibleAccountIds: [],
        accessibleWalletIds: [],
        isOwner: false,
        hasAuditPermission: false,
      };
    }

    // Get all accounts and wallets the user has access to
    const accessibleAccountIds = await this.getAccessibleAccountIds(userId);
    const accessibleWalletIds = await this.getAccessibleWalletIds(userId);

    // Check if user is owner of any resources
    const isOwner = await this.checkOwnership(userId);

    // Determine if user has access to the specific target resource
    let hasAccess = true; // Default to true if no specific target

    if (targetAccountId) {
      hasAccess = accessibleAccountIds.includes(targetAccountId);
    } else if (targetWalletId) {
      hasAccess = accessibleWalletIds.includes(targetWalletId);
    }

    return {
      hasAccess,
      accessibleAccountIds,
      accessibleWalletIds,
      isOwner,
      hasAuditPermission,
    };
  }

  /**
   * Check if user has audit viewing permission
   */
  private async checkAuditPermission(userId: string): Promise<boolean> {
    // Check if user has view_audit_logs permission on any resource
    const accountPermissions = await this.userSharedAccessRepository.getUserAccountPermissions(
      userId,
      undefined, // Check all accounts
    );

    const walletPermissions = await this.userSharedAccessRepository.getUserWalletPermissions(
      userId,
      undefined, // Check all wallets
    );

    const allPermissions = [...accountPermissions, ...walletPermissions];
    return allPermissions.includes('view_audit_logs');
  }

  /**
   * Get all account IDs the user has access to
   */
  private async getAccessibleAccountIds(userId: string): Promise<string[]> {
    const accessibleAccountIds = new Set<string>();

    // Get user's own accounts
    const ownAccounts = await this.accountRepository.findByUserId(userId);
    ownAccounts.forEach(account => accessibleAccountIds.add(account.id));

    // Get shared accounts
    const sharedAccess = await this.userSharedAccessRepository.findByUserId(userId);
    sharedAccess.forEach(access => {
      if (access.accountId) {
        accessibleAccountIds.add(access.accountId);
      }
    });

    return Array.from(accessibleAccountIds);
  }

  /**
   * Get all wallet IDs the user has access to
   */
  private async getAccessibleWalletIds(userId: string): Promise<string[]> {
    const accessibleWalletIds = new Set<string>();

    // Get user's own wallets
    const ownWallets = await this.walletRepository.findByUserId(userId);
    ownWallets.forEach(wallet => accessibleWalletIds.add(wallet.id));

    // Get shared wallets
    const sharedAccess = await this.userSharedAccessRepository.findByUserId(userId);
    sharedAccess.forEach(access => {
      if (access.walletId) {
        accessibleWalletIds.add(access.walletId);
      }
    });

    // Get wallets from shared accounts
    const accessibleAccountIds = await this.getAccessibleAccountIds(userId);
    for (const accountId of accessibleAccountIds) {
      const accountWallets = await this.walletRepository.findByAccountId(accountId);
      accountWallets.forEach(wallet => accessibleWalletIds.add(wallet.id));
    }

    return Array.from(accessibleWalletIds);
  }

  /**
   * Check if user owns any resources (has Owner role)
   */
  private async checkOwnership(userId: string): Promise<boolean> {
    // Check if user has any accounts (account owners)
    const ownAccounts = await this.accountRepository.findByUserId(userId);
    if (ownAccounts.length > 0) {
      return true;
    }

    // Check if user has Owner role on any shared resources
    const sharedAccess = await this.userSharedAccessRepository.findByUserId(userId);
    const hasOwnerRole = sharedAccess.some(access => 
      access.role && access.role.name === 'Owner'
    );

    return hasOwnerRole;
  }

  /**
   * Filter audit logs based on user's access permissions
   */
  async filterAuditLogAccess(
    userId: string,
    auditLogAccountId?: string,
    auditLogWalletId?: string,
  ): Promise<boolean> {
    const accessCheck = await this.execute({
      userId,
      targetAccountId: auditLogAccountId,
      targetWalletId: auditLogWalletId,
    });

    if (!accessCheck.hasAuditPermission) {
      return false;
    }

    // If audit log is not tied to any specific resource, allow if user has audit permission
    if (!auditLogAccountId && !auditLogWalletId) {
      return true;
    }

    // Check specific resource access
    if (auditLogAccountId) {
      return accessCheck.accessibleAccountIds.includes(auditLogAccountId);
    }

    if (auditLogWalletId) {
      return accessCheck.accessibleWalletIds.includes(auditLogWalletId);
    }

    return false;
  }
}
