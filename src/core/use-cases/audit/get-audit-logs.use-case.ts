import { Injectable, Inject } from '@nestjs/common';
import {
  AuditLogRepositoryPort,
  AUDIT_LOG_REPOSITORY,
  AuditLogFilters,
  AuditLogPagination,
  AuditLogQueryResult,
} from '../../ports/audit/audit-log-repository.port';
import { CheckAuditAccessUseCase } from './check-audit-access.use-case';
import { AuditAction, AuditEntity } from '../../constants/audit-actions';

export interface GetAuditLogsInput {
  userId: string;
  filters?: {
    accountId?: string;
    walletId?: string;
    entity?: AuditEntity;
    action?: AuditAction;
    entityId?: string;
    startDate?: Date;
    endDate?: Date;
    targetUserId?: string; // Filter by the user who performed the action
  };
  pagination?: {
    page?: number;
    limit?: number;
    sortBy?: 'createdAt' | 'action' | 'entity';
    sortOrder?: 'asc' | 'desc';
  };
}

export interface GetAuditLogsOutput extends AuditLogQueryResult {
  userHasFullAccess: boolean;
  accessSummary: {
    accessibleAccountIds: string[];
    accessibleWalletIds: string[];
    isOwner: boolean;
  };
}

@Injectable()
export class GetAuditLogsUseCase {
  constructor(
    @Inject(AUDIT_LOG_REPOSITORY)
    private readonly auditLogRepository: AuditLogRepositoryPort,
    private readonly checkAuditAccessUseCase: CheckAuditAccessUseCase,
  ) {}

  async execute(input: GetAuditLogsInput): Promise<GetAuditLogsOutput> {
    const { userId, filters = {}, pagination = {} } = input;

    // Check user's audit access permissions
    const accessCheck = await this.checkAuditAccessUseCase.execute({
      userId,
      targetAccountId: filters.accountId,
      targetWalletId: filters.walletId,
    });

    if (!accessCheck.hasAuditPermission) {
      throw new Error('Insufficient permissions to view audit logs');
    }

    if (!accessCheck.hasAccess && (filters.accountId || filters.walletId)) {
      throw new Error('No access to audit logs for the specified resource');
    }

    // Build audit log filters
    const auditFilters: AuditLogFilters = {
      entity: filters.entity,
      action: filters.action,
      entityId: filters.entityId,
      startDate: filters.startDate,
      endDate: filters.endDate,
      userId: filters.targetUserId, // Filter by who performed the action
    };

    // Add resource filters if specified
    if (filters.accountId) {
      auditFilters.accountId = filters.accountId;
    }
    if (filters.walletId) {
      auditFilters.walletId = filters.walletId;
    }

    // Build pagination
    const auditPagination: AuditLogPagination = {
      page: pagination.page || 1,
      limit: Math.min(pagination.limit || 50, 100), // Max 100 per page
      sortBy: pagination.sortBy || 'createdAt',
      sortOrder: pagination.sortOrder || 'desc',
    };

    // Get audit logs with permission filtering
    const result = await this.auditLogRepository.findForUser(
      userId,
      auditFilters,
      auditPagination,
      accessCheck.accessibleAccountIds,
      accessCheck.accessibleWalletIds,
    );

    return {
      ...result,
      userHasFullAccess: accessCheck.isOwner,
      accessSummary: {
        accessibleAccountIds: accessCheck.accessibleAccountIds,
        accessibleWalletIds: accessCheck.accessibleWalletIds,
        isOwner: accessCheck.isOwner,
      },
    };
  }

  /**
   * Get audit logs for a specific account
   */
  async getAccountAuditLogs(
    userId: string,
    accountId: string,
    filters?: Omit<GetAuditLogsInput['filters'], 'accountId'>,
    pagination?: GetAuditLogsInput['pagination'],
  ): Promise<GetAuditLogsOutput> {
    return this.execute({
      userId,
      filters: { ...filters, accountId },
      pagination,
    });
  }

  /**
   * Get audit logs for a specific wallet
   */
  async getWalletAuditLogs(
    userId: string,
    walletId: string,
    filters?: Omit<GetAuditLogsInput['filters'], 'walletId'>,
    pagination?: GetAuditLogsInput['pagination'],
  ): Promise<GetAuditLogsOutput> {
    return this.execute({
      userId,
      filters: { ...filters, walletId },
      pagination,
    });
  }

  /**
   * Get audit logs for a specific entity
   */
  async getEntityAuditLogs(
    userId: string,
    entity: AuditEntity,
    entityId: string,
    filters?: Omit<GetAuditLogsInput['filters'], 'entity' | 'entityId'>,
    pagination?: GetAuditLogsInput['pagination'],
  ): Promise<GetAuditLogsOutput> {
    return this.execute({
      userId,
      filters: { ...filters, entity, entityId },
      pagination,
    });
  }

  /**
   * Get user's own audit logs (actions they performed)
   */
  async getUserAuditLogs(
    userId: string,
    targetUserId: string,
    filters?: Omit<GetAuditLogsInput['filters'], 'targetUserId'>,
    pagination?: GetAuditLogsInput['pagination'],
  ): Promise<GetAuditLogsOutput> {
    return this.execute({
      userId,
      filters: { ...filters, targetUserId },
      pagination,
    });
  }

  /**
   * Get audit log statistics for accessible resources
   */
  async getAuditStatistics(
    userId: string,
    filters?: GetAuditLogsInput['filters'],
  ): Promise<{
    totalLogs: number;
    actionBreakdown: Record<AuditAction, number>;
    entityBreakdown: Record<AuditEntity, number>;
    dailyActivity: Array<{ date: string; count: number }>;
    accessSummary: {
      accessibleAccountIds: string[];
      accessibleWalletIds: string[];
      isOwner: boolean;
    };
  }> {
    // Check user's audit access permissions
    const accessCheck = await this.checkAuditAccessUseCase.execute({
      userId,
      targetAccountId: filters?.accountId,
      targetWalletId: filters?.walletId,
    });

    if (!accessCheck.hasAuditPermission) {
      throw new Error('Insufficient permissions to view audit statistics');
    }

    // Build audit log filters
    const auditFilters: AuditLogFilters = {
      entity: filters?.entity,
      action: filters?.action,
      entityId: filters?.entityId,
      startDate: filters?.startDate,
      endDate: filters?.endDate,
      userId: filters?.targetUserId,
    };

    // Add resource filters if specified and accessible
    if (filters?.accountId && accessCheck.accessibleAccountIds.includes(filters.accountId)) {
      auditFilters.accountId = filters.accountId;
    }
    if (filters?.walletId && accessCheck.accessibleWalletIds.includes(filters.walletId)) {
      auditFilters.walletId = filters.walletId;
    }

    const statistics = await this.auditLogRepository.getStatistics(auditFilters);

    return {
      ...statistics,
      accessSummary: {
        accessibleAccountIds: accessCheck.accessibleAccountIds,
        accessibleWalletIds: accessCheck.accessibleWalletIds,
        isOwner: accessCheck.isOwner,
      },
    };
  }
}
