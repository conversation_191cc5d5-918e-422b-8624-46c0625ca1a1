import { User } from '../../entities/user.entity';
import { AuthToken } from '../../entities/auth-token.entity';
import {
  USER_REPOSITORY,
  UserRepositoryPort,
} from '../../ports/auth/user-repository.port';
import {
  PASSWORD_HASHER_REPOSITORY,
  PasswordHasherPort,
} from '../../ports/auth/password-hasher.port';
import {
  USER_AUTHENTICATOR,
  UserAuthenticatorPort,
} from '../../ports/auth/user-authenticator.port';
import { InvalidCredentialsException } from '../../exceptions/auth.exceptions';
import { Inject } from '@nestjs/common';
import { RefreshTokenRepositoryPort } from '../../ports/auth/refresh-token-repository.port';
import { RefreshToken } from '../../entities/refresh-token.entity';
// AuditService removed - using automatic audit decorators in controller

export interface LoginUserInput {
  email: string;
  password: string;
  deviceId: string;
  auditContext?: {
    ipAddress?: string;
    userAgent?: string;
    sessionId?: string;
  };
}

export interface LoginUserOutput {
  user: User;
  tokens: AuthToken;
}

export class LoginUserUseCase {
  constructor(
    @Inject(USER_REPOSITORY)
    private readonly userRepository: UserRepositoryPort,
    @Inject(PASSWORD_HASHER_REPOSITORY)
    private readonly passwordHasher: PasswordHasherPort,
    @Inject(USER_AUTHENTICATOR)
    private readonly userAuthenticator: UserAuthenticatorPort,
    @Inject('REFRESH_TOKEN_REPOSITORY')
    private readonly refreshTokenRepo: RefreshTokenRepositoryPort,
  ) {}

  async execute(input: LoginUserInput): Promise<LoginUserOutput> {
    // Find user by email
    const user = await this.userRepository.findByEmail(input.email);
    if (!user) {
      throw new InvalidCredentialsException();
    }

    // Verify password
    const isValidPassword = await this.passwordHasher.verify(
      input.password,
      user.passwordHash,
    );
    if (!isValidPassword) {
      throw new InvalidCredentialsException();
    }

    // Generate tokens
    const { accessToken, refreshToken } =
      await this.userAuthenticator.generateTokens(user.id, user.email);

    // Hash refresh token and save per device
    const refreshTokenHash = await this.passwordHasher.hash(refreshToken);
    const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days
    const refreshTokenEntity = new RefreshToken(
      crypto.randomUUID(),
      user.id,
      input.deviceId,
      refreshTokenHash,
      new Date(),
      expiresAt,
    );
    await this.refreshTokenRepo.save(refreshTokenEntity);

    // Update lastLogin
    await this.userRepository.updateLastLogin(user.id, new Date());

    // Create auth token entity
    const tokens = new AuthToken(accessToken, refreshToken, 3600);

    return {
      user,
      tokens,
    };
    // Audit logging is handled automatically by the @Audit decorator
  }
}
