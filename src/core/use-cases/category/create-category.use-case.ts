import { Injectable, Inject } from '@nestjs/common';
import { Category } from '../../entities/category.entity';
import {
  CATEGORY_REPOSITORY,
  CategoryRepositoryPort,
} from '../../ports/category/category-repository.port';
import { AuditService } from '../../services/audit.service';
import { AuditEntity } from '../../constants/audit-actions';

export interface CreateCategoryRequest {
  userId: string;
  name: string;
  type: string;
  color?: string;
  auditContext?: {
    ipAddress?: string;
    userAgent?: string;
    sessionId?: string;
  };
}

@Injectable()
export class CreateCategoryUseCase {
  constructor(
    @Inject(CATEGORY_REPOSITORY)
    private readonly categoryRepository: CategoryRepositoryPort,
    private readonly auditService: AuditService,
  ) {}

  async execute(request: CreateCategoryRequest): Promise<Category> {
    // Check if category with same name already exists for this user
    const existingCategory = await this.categoryRepository.findByUserIdAndName(
      request.userId,
      request.name,
    );

    if (existingCategory) {
      throw new Error('Category with this name already exists');
    }

    // Create new category
    const category = Category.create({
      userId: request.userId,
      name: request.name.trim(),
      type: request.type.toLowerCase(),
      color: request.color,
    });

    const savedCategory = await this.categoryRepository.save(category);

    // Audit logging is handled automatically by the @AuditCreate decorator
    // in the controller layer

    return savedCategory;
  }
}
