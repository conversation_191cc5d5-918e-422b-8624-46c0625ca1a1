import { Injectable, Inject } from '@nestjs/common';
import {
  CATEGORY_REPOSITORY,
  CategoryRepositoryPort,
} from '../../ports/category/category-repository.port';
import {
  SUBCATEGORY_REPOSITORY,
  SubCategoryRepositoryPort,
} from '../../ports/category/subcategory-repository.port';
import { AuditService } from '../../services/audit.service';
import { AuditEntity } from '../../constants/audit-actions';

export interface DeleteCategoryRequest {
  id: string;
  userId: string;
  auditContext?: {
    ipAddress?: string;
    userAgent?: string;
    sessionId?: string;
  };
}

@Injectable()
export class DeleteCategoryUseCase {
  constructor(
    @Inject(CATEGORY_REPOSITORY)
    private readonly categoryRepository: CategoryRepositoryPort,
    @Inject(SUBCATEGORY_REPOSITORY)
    private readonly subcategoryRepository: SubCategoryRepositoryPort,
    private readonly auditService: AuditService,
  ) {}

  async execute(request: DeleteCategoryRequest): Promise<void> {
    // Find existing category
    const existingCategory = await this.categoryRepository.findById(request.id);
    if (!existingCategory) {
      throw new Error('Category not found');
    }

    // Check if user owns this category
    if (existingCategory.userId !== request.userId) {
      throw new Error('You do not have permission to delete this category');
    }

    // Check if category is already deleted
    if (existingCategory.isDeleted()) {
      throw new Error('Category is already deleted');
    }

    // Get subcategories before deletion for audit
    const subcategories = await this.subcategoryRepository.findByCategoryId(
      request.id,
    );

    // Capture category data before deletion for audit
    const deletedCategoryData = this.auditService.sanitizeData({
      name: existingCategory.name,
      type: existingCategory.type,
      color: existingCategory.color,
      userId: existingCategory.userId,
      subcategoriesCount: subcategories.filter(sub => !sub.isDeleted()).length,
    });

    // Soft delete the category
    await this.categoryRepository.softDelete(request.id);

    // Log the category deletion
    await this.auditService.logDelete(
      request.userId,
      AuditEntity.CATEGORY,
      request.id,
      deletedCategoryData,
      {
        context: request.auditContext,
        metadata: {
          description: `Category "${existingCategory.name}" deleted`,
          categoryType: existingCategory.type,
          subcategoriesAffected: subcategories.filter(sub => !sub.isDeleted()).length,
        },
      },
    );

    // Also soft delete all subcategories under this category
    for (const subcategory of subcategories) {
      if (!subcategory.isDeleted()) {
        await this.subcategoryRepository.softDelete(subcategory.id);

        // Log each subcategory deletion
        await this.auditService.logDelete(
          request.userId,
          AuditEntity.SUBCATEGORY,
          subcategory.id,
          this.auditService.sanitizeData({
            name: subcategory.name,
            categoryId: subcategory.categoryId,
            userId: subcategory.userId,
          }),
          {
            context: request.auditContext,
            metadata: {
              description: `Subcategory "${subcategory.name}" deleted (cascade from category deletion)`,
              parentCategoryName: existingCategory.name,
              cascadeDelete: true,
            },
          },
        );
      }
    }
  }
}
