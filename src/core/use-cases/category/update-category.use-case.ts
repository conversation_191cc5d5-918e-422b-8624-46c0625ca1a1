import { Injectable, Inject } from '@nestjs/common';
import { Category } from '../../entities/category.entity';
import {
  CATEGORY_REPOSITORY,
  CategoryRepositoryPort,
} from '../../ports/category/category-repository.port';
// AuditService removed - using automatic audit decorators in controller

export interface UpdateCategoryRequest {
  id: string;
  userId: string;
  name?: string;
  type?: string;
  color?: string;
  auditContext?: {
    ipAddress?: string;
    userAgent?: string;
    sessionId?: string;
  };
}

@Injectable()
export class UpdateCategoryUseCase {
  constructor(
    @Inject(CATEGORY_REPOSITORY)
    private readonly categoryRepository: CategoryRepositoryPort,
  ) {}

  async execute(request: UpdateCategoryRequest): Promise<Category> {
    // Find existing category
    const existingCategory = await this.categoryRepository.findById(request.id);
    if (!existingCategory) {
      throw new Error('Category not found');
    }

    // Check if user owns this category
    if (existingCategory.userId !== request.userId) {
      throw new Error('You do not have permission to update this category');
    }

    // Check if category is deleted
    if (existingCategory.isDeleted()) {
      throw new Error('Cannot update deleted category');
    }

    // If name is being updated, check for duplicates
    if (request.name && request.name !== existingCategory.name) {
      const duplicateCategory =
        await this.categoryRepository.findByUserIdAndName(
          request.userId,
          request.name,
        );
      if (duplicateCategory && duplicateCategory.id !== request.id) {
        throw new Error('Category with this name already exists');
      }
    }

    // Update category
    const updatedCategory = existingCategory.update({
      name: request.name?.trim(),
      type: request.type?.toLowerCase(),
      color: request.color,
    });

    return await this.categoryRepository.update(updatedCategory);
    // Audit logging is handled automatically by the @AuditUpdate decorator
  }
}
