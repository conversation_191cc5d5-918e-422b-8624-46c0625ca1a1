import { Injectable, Inject } from '@nestjs/common';
import { Category } from '../../entities/category.entity';
import {
  CATEGORY_REPOSITORY,
  CategoryRepositoryPort,
} from '../../ports/category/category-repository.port';
import { AuditService } from '../../services/audit.service';
import { AuditEntity } from '../../constants/audit-actions';

export interface UpdateCategoryRequest {
  id: string;
  userId: string;
  name?: string;
  type?: string;
  color?: string;
  auditContext?: {
    ipAddress?: string;
    userAgent?: string;
    sessionId?: string;
  };
}

@Injectable()
export class UpdateCategoryUseCase {
  constructor(
    @Inject(CATEGORY_REPOSITORY)
    private readonly categoryRepository: CategoryRepositoryPort,
    private readonly auditService: AuditService,
  ) {}

  async execute(request: UpdateCategoryRequest): Promise<Category> {
    // Find existing category
    const existingCategory = await this.categoryRepository.findById(request.id);
    if (!existingCategory) {
      throw new Error('Category not found');
    }

    // Check if user owns this category
    if (existingCategory.userId !== request.userId) {
      throw new Error('You do not have permission to update this category');
    }

    // Check if category is deleted
    if (existingCategory.isDeleted()) {
      throw new Error('Cannot update deleted category');
    }

    // If name is being updated, check for duplicates
    if (request.name && request.name !== existingCategory.name) {
      const duplicateCategory =
        await this.categoryRepository.findByUserIdAndName(
          request.userId,
          request.name,
        );
      if (duplicateCategory && duplicateCategory.id !== request.id) {
        throw new Error('Category with this name already exists');
      }
    }

    // Capture before values for audit
    const beforeValues = this.auditService.sanitizeData({
      name: existingCategory.name,
      type: existingCategory.type,
      color: existingCategory.color,
    });

    // Update category
    const updatedCategory = existingCategory.update({
      name: request.name?.trim(),
      type: request.type?.toLowerCase(),
      color: request.color,
    });

    const savedCategory = await this.categoryRepository.update(updatedCategory);

    // Capture after values for audit
    const afterValues = this.auditService.sanitizeData({
      name: savedCategory.name,
      type: savedCategory.type,
      color: savedCategory.color,
    });

    // Log the category update
    await this.auditService.logUpdate(
      request.userId,
      AuditEntity.CATEGORY,
      savedCategory.id,
      beforeValues,
      afterValues,
      {
        context: request.auditContext,
        metadata: {
          description: `Category "${savedCategory.name}" updated`,
          categoryType: savedCategory.type,
        },
      },
    );

    return savedCategory;
  }
}
