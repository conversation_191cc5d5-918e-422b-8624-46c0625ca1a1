import { Injectable, Inject, Logger } from '@nestjs/common';
import {
  KYC_VERIFICATION_REPOSITORY,
  KycVerificationRepositoryPort,
  KycVerificationSummary,
} from '../../ports/kyc/kyc-verification-repository.port';
import {
  KycVerification,
  DocumentType,
  VerificationType,
} from '../../entities/kyc-verification.entity';
import { CountryKycConfigService } from '../../../infrastructure/kyc/country-kyc-config.service';

export interface KycStatusOutput {
  userId: string;
  overallStatus: 'incomplete' | 'partial' | 'complete';
  completionPercentage: number;
  summary: KycVerificationSummary;
  verificationsByType: {
    identity: {
      status: 'not_started' | 'in_progress' | 'verified' | 'failed';
      verifiedDocuments: DocumentType[];
      pendingDocuments: DocumentType[];
      failedDocuments: DocumentType[];
      lastVerificationDate?: Date;
    };
    address: {
      status: 'not_started' | 'in_progress' | 'verified' | 'failed';
      verifiedDocuments: DocumentType[];
      pendingDocuments: DocumentType[];
      failedDocuments: DocumentType[];
      lastVerificationDate?: Date;
    };
    business: {
      status: 'not_started' | 'in_progress' | 'verified' | 'failed';
      verifiedDocuments: DocumentType[];
      pendingDocuments: DocumentType[];
      failedDocuments: DocumentType[];
      lastVerificationDate?: Date;
    };
    facial: {
      status: 'not_started' | 'in_progress' | 'verified' | 'failed';
      verifiedDocuments: DocumentType[];
      pendingDocuments: DocumentType[];
      failedDocuments: DocumentType[];
      lastVerificationDate?: Date;
    };
  };
  requirements: {
    countryCode: string;
    minimumIdentityDocuments: number;
    minimumAddressDocuments: number;
    minimumBusinessDocuments: number;
    facialVerificationRequired: boolean;
  };
  nextSteps: string[];
  canProceed: boolean;
}

export interface GetKycStatusInput {
  userId: string;
  countryCode?: string;
}

@Injectable()
export class GetKycStatusUseCase {
  private readonly logger = new Logger(GetKycStatusUseCase.name);

  constructor(
    @Inject(KYC_VERIFICATION_REPOSITORY)
    private readonly verificationRepository: KycVerificationRepositoryPort,
    private readonly countryConfigService: CountryKycConfigService,
  ) {}

  async execute(input: GetKycStatusInput): Promise<KycStatusOutput> {
    const countryCode = input.countryCode || '234'; // Default to Nigeria

    this.logger.log(`Getting KYC status for user ${input.userId}`, {
      userId: input.userId,
      countryCode,
    });

    try {
      // Get user's verification summary
      const summary = await this.verificationRepository.getUserKycSummary(
        input.userId,
      );

      // Get all user verifications
      const allVerifications = await this.verificationRepository.findByUserId(
        input.userId,
      );

      // Get country requirements
      const requirements =
        this.countryConfigService.getKycRequirements(countryCode);

      // Analyze verifications by type
      const verificationsByType =
        this.analyzeVerificationsByType(allVerifications);

      // Calculate overall status and completion percentage
      const { overallStatus, completionPercentage, canProceed } =
        this.calculateOverallStatus(verificationsByType, requirements);

      // Generate next steps
      const nextSteps = this.generateNextSteps(
        verificationsByType,
        requirements,
        countryCode,
      );

      const result: KycStatusOutput = {
        userId: input.userId,
        overallStatus,
        completionPercentage,
        summary,
        verificationsByType,
        requirements: {
          countryCode,
          minimumIdentityDocuments: requirements.minimumIdentityDocuments,
          minimumAddressDocuments: requirements.minimumAddressDocuments,
          minimumBusinessDocuments: requirements.minimumBusinessDocuments,
          facialVerificationRequired: requirements.facialVerificationRequired,
        },
        nextSteps,
        canProceed,
      };

      this.logger.log(`KYC status retrieved for user ${input.userId}`, {
        userId: input.userId,
        overallStatus,
        completionPercentage,
        canProceed,
      });

      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Failed to get KYC status for user ${input.userId}`, {
        error: errorMessage,
      });
      throw error;
    }
  }

  private analyzeVerificationsByType(verifications: KycVerification[]) {
    const identityDocs = [
      DocumentType.BVN,
      DocumentType.NIN,
      DocumentType.DRIVERS_LICENSE,
      DocumentType.INTERNATIONAL_PASSPORT,
      DocumentType.VOTERS_CARD,
    ];
    const addressDocs = [
      DocumentType.UTILITY_BILL,
      DocumentType.BANK_STATEMENT,
      DocumentType.TENANCY_AGREEMENT,
      DocumentType.EMPLOYER_LETTER,
    ];
    const businessDocs = [
      DocumentType.CAC,
      DocumentType.TIN,
      DocumentType.BUSINESS_PERMIT,
    ];
    const facialDocs = [DocumentType.FACIAL_IMAGE, DocumentType.SELFIE];

    const analyzeType = (documentTypes: DocumentType[]) => {
      const typeVerifications = verifications.filter((v) =>
        documentTypes.includes(v.documentType),
      );

      const verified = typeVerifications.filter(
        (v) => v.isVerified && !v.isExpired,
      );
      const pending = typeVerifications.filter((v) => v.isPending);
      const failed = typeVerifications.filter((v) => v.isFailed);

      const verifiedDocuments = verified.map((v) => v.documentType);
      const pendingDocuments = pending.map((v) => v.documentType);
      const failedDocuments = failed.map((v) => v.documentType);

      let status: 'not_started' | 'in_progress' | 'verified' | 'failed';
      if (verified.length > 0) {
        status = 'verified';
      } else if (pending.length > 0) {
        status = 'in_progress';
      } else if (failed.length > 0) {
        status = 'failed';
      } else {
        status = 'not_started';
      }

      const lastVerificationDate =
        typeVerifications.length > 0
          ? new Date(
              Math.max(...typeVerifications.map((v) => v.createdAt.getTime())),
            )
          : undefined;

      return {
        status,
        verifiedDocuments,
        pendingDocuments,
        failedDocuments,
        lastVerificationDate,
      };
    };

    return {
      identity: analyzeType(identityDocs),
      address: analyzeType(addressDocs),
      business: analyzeType(businessDocs),
      facial: analyzeType(facialDocs),
    };
  }

  private calculateOverallStatus(verificationsByType: any, requirements: any) {
    const identityMet =
      verificationsByType.identity.verifiedDocuments.length >=
      requirements.minimumIdentityDocuments;
    const addressMet =
      verificationsByType.address.verifiedDocuments.length >=
      requirements.minimumAddressDocuments;
    const businessMet =
      requirements.minimumBusinessDocuments === 0 ||
      verificationsByType.business.verifiedDocuments.length >=
        requirements.minimumBusinessDocuments;
    const facialMet =
      !requirements.facialVerificationRequired ||
      verificationsByType.facial.verifiedDocuments.length > 0;

    const totalRequirements = 4; // identity, address, business, facial
    let metRequirements = 0;

    if (identityMet) metRequirements++;
    if (addressMet) metRequirements++;
    if (businessMet) metRequirements++;
    if (facialMet) metRequirements++;

    const completionPercentage = Math.round(
      (metRequirements / totalRequirements) * 100,
    );

    let overallStatus: 'incomplete' | 'partial' | 'complete';
    if (identityMet && addressMet && businessMet && facialMet) {
      overallStatus = 'complete';
    } else if (metRequirements > 0) {
      overallStatus = 'partial';
    } else {
      overallStatus = 'incomplete';
    }

    const canProceed = identityMet && addressMet; // Minimum requirements to proceed

    return { overallStatus, completionPercentage, canProceed };
  }

  private generateNextSteps(
    verificationsByType: any,
    requirements: any,
    countryCode: string,
  ): string[] {
    const steps: string[] = [];

    // Identity verification steps
    if (
      verificationsByType.identity.verifiedDocuments.length <
      requirements.minimumIdentityDocuments
    ) {
      if (verificationsByType.identity.status === 'not_started') {
        steps.push(
          'Complete identity verification by providing a valid BVN or NIN',
        );
      } else if (verificationsByType.identity.status === 'failed') {
        steps.push('Retry identity verification with correct information');
      } else if (verificationsByType.identity.status === 'in_progress') {
        steps.push('Wait for identity verification to complete');
      }
    }

    // Address verification steps
    if (
      verificationsByType.address.verifiedDocuments.length <
      requirements.minimumAddressDocuments
    ) {
      if (verificationsByType.address.status === 'not_started') {
        steps.push(
          'Upload a utility bill or bank statement for address verification',
        );
      } else if (verificationsByType.address.status === 'failed') {
        steps.push('Retry address verification with a clear, recent document');
      } else if (verificationsByType.address.status === 'in_progress') {
        steps.push('Wait for address verification to complete');
      }
    }

    // Business verification steps (if required)
    if (
      requirements.minimumBusinessDocuments > 0 &&
      verificationsByType.business.verifiedDocuments.length <
        requirements.minimumBusinessDocuments
    ) {
      if (verificationsByType.business.status === 'not_started') {
        steps.push('Provide business registration documents (CAC, TIN)');
      } else if (verificationsByType.business.status === 'failed') {
        steps.push('Retry business verification with valid documents');
      } else if (verificationsByType.business.status === 'in_progress') {
        steps.push('Wait for business verification to complete');
      }
    }

    // Facial verification steps (if required)
    if (
      requirements.facialVerificationRequired &&
      verificationsByType.facial.verifiedDocuments.length === 0
    ) {
      if (verificationsByType.facial.status === 'not_started') {
        steps.push('Complete facial verification by taking a selfie');
      } else if (verificationsByType.facial.status === 'failed') {
        steps.push('Retry facial verification with a clear selfie');
      } else if (verificationsByType.facial.status === 'in_progress') {
        steps.push('Wait for facial verification to complete');
      }
    }

    if (steps.length === 0) {
      steps.push('Your KYC verification is complete!');
    }

    return steps;
  }

  async getUserVerificationHistory(userId: string): Promise<KycVerification[]> {
    const verifications =
      await this.verificationRepository.findByUserId(userId);
    return verifications.sort(
      (a, b) => b.createdAt.getTime() - a.createdAt.getTime(),
    );
  }

  async getVerificationsByDocumentType(
    userId: string,
    documentType: DocumentType,
  ): Promise<KycVerification[]> {
    return this.verificationRepository.findByUserIdAndDocumentType(
      userId,
      documentType,
    );
  }
}
