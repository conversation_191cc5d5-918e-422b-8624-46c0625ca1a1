import { Injectable, Inject, Logger } from '@nestjs/common';
import { KycVerification, DocumentType, VerificationType } from '../../entities/kyc-verification.entity';
import { 
  KYC_VERIFICATION_REPOSITORY, 
  KycVerificationRepositoryPort 
} from '../../ports/kyc/kyc-verification-repository.port';
import { BvnVerificationRequest } from '../../ports/kyc/kyc-provider.port';
import { KycProviderManager } from '../../../infrastructure/kyc/kyc-provider-manager';
import { CountryKycConfigService } from '../../../infrastructure/kyc/country-kyc-config.service';

export interface VerifyBvnInput {
  userId: string;
  bvn: string;
  firstName?: string;
  lastName?: string;
  dateOfBirth?: string;
  phoneNumber?: string;
  countryCode?: string;
}

export interface VerifyBvnOutput {
  verification: KycVerification;
  isVerified: boolean;
  extractedData?: Record<string, any>;
  providerUsed: string;
  responseTime: number;
}

@Injectable()
export class VerifyBvnUseCase {
  private readonly logger = new Logger(VerifyBvnUseCase.name);

  constructor(
    @Inject(KYC_VERIFICATION_REPOSITORY)
    private readonly verificationRepository: KycVerificationRepositoryPort,
    private readonly providerManager: KycProviderManager,
    private readonly countryConfigService: CountryKycConfigService,
  ) {}

  async execute(input: VerifyBvnInput): Promise<VerifyBvnOutput> {
    const startTime = Date.now();
    const countryCode = input.countryCode || '234'; // Default to Nigeria

    this.logger.log(`Starting BVN verification for user ${input.userId}`, {
      userId: input.userId,
      countryCode,
      bvn: input.bvn.substring(0, 3) + '********', // Log only first 3 digits for security
    });

    try {
      // Validate input
      this.validateInput(input, countryCode);

      // Check if user already has a verified BVN
      const existingVerification = await this.verificationRepository.getUserLatestVerification(
        input.userId,
        DocumentType.BVN
      );

      if (existingVerification?.isVerified && !existingVerification.isExpired) {
        this.logger.log(`User ${input.userId} already has verified BVN`, {
          verificationId: existingVerification.id,
        });

        return {
          verification: existingVerification,
          isVerified: true,
          extractedData: existingVerification.verificationData,
          providerUsed: existingVerification.providerId,
          responseTime: Date.now() - startTime,
        };
      }

      // Prepare verification request
      const verificationRequest: BvnVerificationRequest = {
        documentType: DocumentType.BVN,
        documentNumber: input.bvn,
        firstName: input.firstName,
        lastName: input.lastName,
        dateOfBirth: input.dateOfBirth,
        phoneNumber: input.phoneNumber,
      };

      // Perform verification using provider manager
      const { result, log } = await this.providerManager.verifyDocument(
        countryCode,
        DocumentType.BVN,
        verificationRequest
      );

      // Create verification entity
      const verification = KycVerification.create({
        userId: input.userId,
        verificationType: VerificationType.IDENTITY,
        documentType: DocumentType.BVN,
        countryCode,
        providerId: log.attempts[log.attempts.length - 1]?.providerId || 'unknown',
        providerReference: result.providerReference,
        documentNumber: input.bvn,
        verificationData: result.extractedData,
      });

      // Update verification status based on result
      let finalVerification: KycVerification;
      if (result.success && result.verified) {
        finalVerification = verification.markAsVerified(result.providerResponse, result.extractedData);
        this.logger.log(`BVN verification successful for user ${input.userId}`, {
          verificationId: verification.id,
          providerId: finalVerification.providerId,
        });
      } else if (result.requiresManualReview) {
        finalVerification = verification.markForManualReview(
          result.failureReason || 'Verification requires manual review'
        );
        this.logger.warn(`BVN verification requires manual review for user ${input.userId}`, {
          verificationId: verification.id,
          reason: result.failureReason,
        });
      } else {
        finalVerification = verification.markAsFailed(
          result.failureReason || 'Verification failed',
          result.providerResponse
        );
        this.logger.warn(`BVN verification failed for user ${input.userId}`, {
          verificationId: verification.id,
          reason: result.failureReason,
        });
      }

      // Save verification to repository
      const savedVerification = await this.verificationRepository.save(finalVerification);

      const responseTime = Date.now() - startTime;
      this.logger.log(`BVN verification completed for user ${input.userId}`, {
        verificationId: savedVerification.id,
        status: savedVerification.status,
        responseTime,
      });

      return {
        verification: savedVerification,
        isVerified: savedVerification.isVerified,
        extractedData: savedVerification.verificationData,
        providerUsed: savedVerification.providerId,
        responseTime,
      };

    } catch (error) {
      const responseTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      this.logger.error(`BVN verification failed for user ${input.userId}`, {
        error: errorMessage,
        responseTime,
      });

      throw error;
    }
  }

  private validateInput(input: VerifyBvnInput, countryCode: string): void {
    if (!input.userId || input.userId.trim().length === 0) {
      throw new Error('User ID is required');
    }

    if (!input.bvn || input.bvn.trim().length === 0) {
      throw new Error('BVN is required');
    }

    // Validate BVN format using country configuration
    const isValid = this.countryConfigService.validateDocumentNumber(
      countryCode,
      DocumentType.BVN,
      input.bvn
    );

    if (!isValid) {
      const rule = this.countryConfigService.getValidationRule(countryCode, DocumentType.BVN);
      throw new Error(`Invalid BVN format: ${rule.description}`);
    }

    // Check if BVN verification is supported in the country
    if (!this.countryConfigService.isDocumentSupported(countryCode, DocumentType.BVN)) {
      throw new Error(`BVN verification is not supported in country ${countryCode}`);
    }
  }

  async getUserBvnStatus(userId: string): Promise<{
    hasVerifiedBvn: boolean;
    verification?: KycVerification;
    lastAttempt?: Date;
  }> {
    const verifications = await this.verificationRepository.findByUserIdAndDocumentType(
      userId,
      DocumentType.BVN
    );

    if (verifications.length === 0) {
      return { hasVerifiedBvn: false };
    }

    // Sort by creation date, most recent first
    verifications.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
    const latestVerification = verifications[0];

    return {
      hasVerifiedBvn: latestVerification.isVerified && !latestVerification.isExpired,
      verification: latestVerification,
      lastAttempt: latestVerification.createdAt,
    };
  }

  async retryBvnVerification(verificationId: string): Promise<VerifyBvnOutput> {
    const existingVerification = await this.verificationRepository.findById(verificationId);
    if (!existingVerification) {
      throw new Error('Verification not found');
    }

    if (existingVerification.documentType !== DocumentType.BVN) {
      throw new Error('Verification is not for BVN document type');
    }

    if (existingVerification.isVerified) {
      throw new Error('Verification is already successful');
    }

    // Retry with the same parameters
    return this.execute({
      userId: existingVerification.userId,
      bvn: existingVerification.documentNumber!,
      countryCode: existingVerification.countryCode,
    });
  }
}
