import { Injectable, Inject, Logger } from '@nestjs/common';
import { KycVerification, DocumentType, VerificationType } from '../../entities/kyc-verification.entity';
import { 
  KYC_VERIFICATION_REPOSITORY, 
  KycVerificationRepositoryPort 
} from '../../ports/kyc/kyc-verification-repository.port';
import { CacVerificationRequest } from '../../ports/kyc/kyc-provider.port';
import { KycProviderManager } from '../../../infrastructure/kyc/kyc-provider-manager';
import { CountryKycConfigService } from '../../../infrastructure/kyc/country-kyc-config.service';

export interface VerifyCacInput {
  userId: string;
  rcNumber: string;
  companyName: string;
  countryCode?: string;
}

export interface VerifyCacOutput {
  verification: KycVerification;
  isVerified: boolean;
  extractedData?: Record<string, any>;
  providerUsed: string;
  responseTime: number;
}

@Injectable()
export class VerifyCacUseCase {
  private readonly logger = new Logger(VerifyCacUseCase.name);

  constructor(
    @Inject(KYC_VERIFICATION_REPOSITORY)
    private readonly verificationRepository: KycVerificationRepositoryPort,
    private readonly providerManager: KycProviderManager,
    private readonly countryConfigService: CountryKycConfigService,
  ) {}

  async execute(input: VerifyCacInput): Promise<VerifyCacOutput> {
    const startTime = Date.now();
    const countryCode = input.countryCode || '234'; // Default to Nigeria

    this.logger.log(`Starting CAC verification for user ${input.userId}`, {
      userId: input.userId,
      countryCode,
      rcNumber: input.rcNumber,
      companyName: input.companyName,
    });

    try {
      // Validate input
      this.validateInput(input, countryCode);

      // Check if user already has a verified CAC for this company
      const existingVerification = await this.verificationRepository.getUserLatestVerification(
        input.userId,
        DocumentType.CAC
      );

      if (existingVerification?.isVerified && 
          !existingVerification.isExpired && 
          existingVerification.documentNumber === input.rcNumber) {
        this.logger.log(`User ${input.userId} already has verified CAC for RC ${input.rcNumber}`, {
          verificationId: existingVerification.id,
        });

        return {
          verification: existingVerification,
          isVerified: true,
          extractedData: existingVerification.verificationData,
          providerUsed: existingVerification.providerId,
          responseTime: Date.now() - startTime,
        };
      }

      // Prepare verification request
      const verificationRequest: CacVerificationRequest = {
        documentType: DocumentType.CAC,
        documentNumber: input.rcNumber,
        companyName: input.companyName,
      };

      // Perform verification using provider manager
      const { result, log } = await this.providerManager.verifyDocument(
        countryCode,
        DocumentType.CAC,
        verificationRequest
      );

      // Create verification entity
      const verification = KycVerification.create({
        userId: input.userId,
        verificationType: VerificationType.BUSINESS,
        documentType: DocumentType.CAC,
        countryCode,
        providerId: log.attempts[log.attempts.length - 1]?.providerId || 'unknown',
        providerReference: result.providerReference,
        documentNumber: input.rcNumber,
        verificationData: result.extractedData,
      });

      // Update verification status based on result
      let finalVerification: KycVerification;
      if (result.success && result.verified) {
        finalVerification = verification.markAsVerified(result.providerResponse, result.extractedData);
        this.logger.log(`CAC verification successful for user ${input.userId}`, {
          verificationId: verification.id,
          providerId: finalVerification.providerId,
        });
      } else if (result.requiresManualReview) {
        finalVerification = verification.markForManualReview(
          result.failureReason || 'Verification requires manual review'
        );
        this.logger.warn(`CAC verification requires manual review for user ${input.userId}`, {
          verificationId: verification.id,
          reason: result.failureReason,
        });
      } else {
        finalVerification = verification.markAsFailed(
          result.failureReason || 'Verification failed',
          result.providerResponse
        );
        this.logger.warn(`CAC verification failed for user ${input.userId}`, {
          verificationId: verification.id,
          reason: result.failureReason,
        });
      }

      // Save verification to repository
      const savedVerification = await this.verificationRepository.save(finalVerification);

      const responseTime = Date.now() - startTime;
      this.logger.log(`CAC verification completed for user ${input.userId}`, {
        verificationId: savedVerification.id,
        status: savedVerification.status,
        responseTime,
      });

      return {
        verification: savedVerification,
        isVerified: savedVerification.isVerified,
        extractedData: savedVerification.verificationData,
        providerUsed: savedVerification.providerId,
        responseTime,
      };

    } catch (error) {
      const responseTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      this.logger.error(`CAC verification failed for user ${input.userId}`, {
        error: errorMessage,
        responseTime,
      });

      throw error;
    }
  }

  private validateInput(input: VerifyCacInput, countryCode: string): void {
    if (!input.userId || input.userId.trim().length === 0) {
      throw new Error('User ID is required');
    }

    if (!input.rcNumber || input.rcNumber.trim().length === 0) {
      throw new Error('RC Number is required');
    }

    if (!input.companyName || input.companyName.trim().length === 0) {
      throw new Error('Company name is required');
    }

    // Validate RC Number format using country configuration
    const isValid = this.countryConfigService.validateDocumentNumber(
      countryCode,
      DocumentType.CAC,
      input.rcNumber
    );

    if (!isValid) {
      const rule = this.countryConfigService.getValidationRule(countryCode, DocumentType.CAC);
      throw new Error(`Invalid RC Number format: ${rule.description}`);
    }

    // Check if CAC verification is supported in the country
    if (!this.countryConfigService.isDocumentSupported(countryCode, DocumentType.CAC)) {
      throw new Error(`CAC verification is not supported in country ${countryCode}`);
    }
  }

  async getUserCacStatus(userId: string): Promise<{
    hasVerifiedCac: boolean;
    verification?: KycVerification;
    lastAttempt?: Date;
  }> {
    const verifications = await this.verificationRepository.findByUserIdAndDocumentType(
      userId,
      DocumentType.CAC
    );

    if (verifications.length === 0) {
      return { hasVerifiedCac: false };
    }

    // Sort by creation date, most recent first
    verifications.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
    const latestVerification = verifications[0];

    return {
      hasVerifiedCac: latestVerification.isVerified && !latestVerification.isExpired,
      verification: latestVerification,
      lastAttempt: latestVerification.createdAt,
    };
  }

  async getUserCacVerifications(userId: string): Promise<KycVerification[]> {
    const verifications = await this.verificationRepository.findByUserIdAndDocumentType(
      userId,
      DocumentType.CAC
    );

    return verifications.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }

  async retryCacVerification(verificationId: string): Promise<VerifyCacOutput> {
    const existingVerification = await this.verificationRepository.findById(verificationId);
    if (!existingVerification) {
      throw new Error('Verification not found');
    }

    if (existingVerification.documentType !== DocumentType.CAC) {
      throw new Error('Verification is not for CAC document type');
    }

    if (existingVerification.isVerified) {
      throw new Error('Verification is already successful');
    }

    // Extract company name from verification data or use a default
    const companyName = existingVerification.verificationData?.companyName || 
                       existingVerification.verificationData?.company_name || 
                       'Unknown Company';

    // Retry with the same parameters
    return this.execute({
      userId: existingVerification.userId,
      rcNumber: existingVerification.documentNumber!,
      companyName,
      countryCode: existingVerification.countryCode,
    });
  }
}
