import { Injectable, Inject, Logger } from '@nestjs/common';
import { KycVerification, DocumentType, VerificationType } from '../../entities/kyc-verification.entity';
import { 
  KYC_VERIFICATION_REPOSITORY, 
  KycVerificationRepositoryPort 
} from '../../ports/kyc/kyc-verification-repository.port';
import { 
  DOCUMENT_STORAGE_PORT, 
  DocumentStoragePort 
} from '../../ports/kyc/document-storage.port';
import { NinVerificationRequest } from '../../ports/kyc/kyc-provider.port';
import { KycProviderManager } from '../../../infrastructure/kyc/kyc-provider-manager';
import { CountryKycConfigService } from '../../../infrastructure/kyc/country-kyc-config.service';

export interface VerifyNinInput {
  userId: string;
  nin: string;
  firstName?: string;
  lastName?: string;
  dateOfBirth?: string;
  documentImage?: Buffer | string; // Optional document image
  countryCode?: string;
}

export interface VerifyNinOutput {
  verification: KycVerification;
  isVerified: boolean;
  extractedData?: Record<string, any>;
  providerUsed: string;
  responseTime: number;
  documentImageUrl?: string;
}

@Injectable()
export class VerifyNinUseCase {
  private readonly logger = new Logger(VerifyNinUseCase.name);

  constructor(
    @Inject(KYC_VERIFICATION_REPOSITORY)
    private readonly verificationRepository: KycVerificationRepositoryPort,
    @Inject(DOCUMENT_STORAGE_PORT)
    private readonly documentStorage: DocumentStoragePort,
    private readonly providerManager: KycProviderManager,
    private readonly countryConfigService: CountryKycConfigService,
  ) {}

  async execute(input: VerifyNinInput): Promise<VerifyNinOutput> {
    const startTime = Date.now();
    const countryCode = input.countryCode || '234'; // Default to Nigeria

    this.logger.log(`Starting NIN verification for user ${input.userId}`, {
      userId: input.userId,
      countryCode,
      nin: input.nin.substring(0, 3) + '********', // Log only first 3 digits for security
    });

    try {
      // Validate input
      this.validateInput(input, countryCode);

      // Check if user already has a verified NIN
      const existingVerification = await this.verificationRepository.getUserLatestVerification(
        input.userId,
        DocumentType.NIN
      );

      if (existingVerification?.isVerified && !existingVerification.isExpired) {
        this.logger.log(`User ${input.userId} already has verified NIN`, {
          verificationId: existingVerification.id,
        });

        return {
          verification: existingVerification,
          isVerified: true,
          extractedData: existingVerification.verificationData,
          providerUsed: existingVerification.providerId,
          responseTime: Date.now() - startTime,
          documentImageUrl: existingVerification.documentImageUrl,
        };
      }

      // Upload document image if provided
      let documentImageUrl: string | undefined;
      if (input.documentImage) {
        const uploadResult = await this.documentStorage.uploadDocument({
          file: input.documentImage,
          fileName: `nin_${input.userId}_${Date.now()}`,
          mimeType: 'image/jpeg', // Assume JPEG, could be enhanced to detect
          documentType: DocumentType.NIN,
          userId: input.userId,
          metadata: {
            documentNumber: input.nin,
            uploadedAt: new Date().toISOString(),
          },
        });

        if (!uploadResult.success) {
          throw new Error(`Failed to upload document image: ${uploadResult.errorMessage}`);
        }

        documentImageUrl = uploadResult.secureUrl;
        this.logger.log(`Document image uploaded successfully`, {
          userId: input.userId,
          documentUrl: documentImageUrl,
        });
      }

      // Prepare verification request
      const verificationRequest: NinVerificationRequest = {
        documentType: DocumentType.NIN,
        documentNumber: input.nin,
        firstName: input.firstName,
        lastName: input.lastName,
        dateOfBirth: input.dateOfBirth,
        documentImageUrl,
      };

      // Perform verification using provider manager
      const { result, log } = await this.providerManager.verifyDocument(
        countryCode,
        DocumentType.NIN,
        verificationRequest
      );

      // Create verification entity
      const verification = KycVerification.create({
        userId: input.userId,
        verificationType: VerificationType.IDENTITY,
        documentType: DocumentType.NIN,
        countryCode,
        providerId: log.attempts[log.attempts.length - 1]?.providerId || 'unknown',
        providerReference: result.providerReference,
        documentNumber: input.nin,
        documentImageUrl,
        verificationData: result.extractedData,
      });

      // Update verification status based on result
      let finalVerification: KycVerification;
      if (result.success && result.verified) {
        finalVerification = verification.markAsVerified(result.providerResponse, result.extractedData);
        this.logger.log(`NIN verification successful for user ${input.userId}`, {
          verificationId: verification.id,
          providerId: finalVerification.providerId,
        });
      } else if (result.requiresManualReview) {
        finalVerification = verification.markForManualReview(
          result.failureReason || 'Verification requires manual review'
        );
        this.logger.warn(`NIN verification requires manual review for user ${input.userId}`, {
          verificationId: verification.id,
          reason: result.failureReason,
        });
      } else {
        finalVerification = verification.markAsFailed(
          result.failureReason || 'Verification failed',
          result.providerResponse
        );
        this.logger.warn(`NIN verification failed for user ${input.userId}`, {
          verificationId: verification.id,
          reason: result.failureReason,
        });

        // Clean up uploaded image if verification failed
        if (documentImageUrl) {
          try {
            await this.documentStorage.deleteDocument({ documentUrl: documentImageUrl });
          } catch (cleanupError) {
            this.logger.warn(`Failed to cleanup document image after verification failure`, {
              documentUrl: documentImageUrl,
              error: cleanupError,
            });
          }
        }
      }

      // Save verification to repository
      const savedVerification = await this.verificationRepository.save(finalVerification);

      const responseTime = Date.now() - startTime;
      this.logger.log(`NIN verification completed for user ${input.userId}`, {
        verificationId: savedVerification.id,
        status: savedVerification.status,
        responseTime,
      });

      return {
        verification: savedVerification,
        isVerified: savedVerification.isVerified,
        extractedData: savedVerification.verificationData,
        providerUsed: savedVerification.providerId,
        responseTime,
        documentImageUrl: savedVerification.documentImageUrl,
      };

    } catch (error) {
      const responseTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      this.logger.error(`NIN verification failed for user ${input.userId}`, {
        error: errorMessage,
        responseTime,
      });

      throw error;
    }
  }

  private validateInput(input: VerifyNinInput, countryCode: string): void {
    if (!input.userId || input.userId.trim().length === 0) {
      throw new Error('User ID is required');
    }

    if (!input.nin || input.nin.trim().length === 0) {
      throw new Error('NIN is required');
    }

    // Validate NIN format using country configuration
    const isValid = this.countryConfigService.validateDocumentNumber(
      countryCode,
      DocumentType.NIN,
      input.nin
    );

    if (!isValid) {
      const rule = this.countryConfigService.getValidationRule(countryCode, DocumentType.NIN);
      throw new Error(`Invalid NIN format: ${rule.description}`);
    }

    // Check if NIN verification is supported in the country
    if (!this.countryConfigService.isDocumentSupported(countryCode, DocumentType.NIN)) {
      throw new Error(`NIN verification is not supported in country ${countryCode}`);
    }

    // Check if document image is required for this country
    const rule = this.countryConfigService.getValidationRule(countryCode, DocumentType.NIN);
    if (rule.requiresImage && !input.documentImage) {
      throw new Error('Document image is required for NIN verification in this country');
    }
  }

  async getUserNinStatus(userId: string): Promise<{
    hasVerifiedNin: boolean;
    verification?: KycVerification;
    lastAttempt?: Date;
  }> {
    const verifications = await this.verificationRepository.findByUserIdAndDocumentType(
      userId,
      DocumentType.NIN
    );

    if (verifications.length === 0) {
      return { hasVerifiedNin: false };
    }

    // Sort by creation date, most recent first
    verifications.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
    const latestVerification = verifications[0];

    return {
      hasVerifiedNin: latestVerification.isVerified && !latestVerification.isExpired,
      verification: latestVerification,
      lastAttempt: latestVerification.createdAt,
    };
  }

  async retryNinVerification(verificationId: string): Promise<VerifyNinOutput> {
    const existingVerification = await this.verificationRepository.findById(verificationId);
    if (!existingVerification) {
      throw new Error('Verification not found');
    }

    if (existingVerification.documentType !== DocumentType.NIN) {
      throw new Error('Verification is not for NIN document type');
    }

    if (existingVerification.isVerified) {
      throw new Error('Verification is already successful');
    }

    // Retry with the same parameters
    return this.execute({
      userId: existingVerification.userId,
      nin: existingVerification.documentNumber!,
      countryCode: existingVerification.countryCode,
      // Note: We can't retry with the same image as we don't store the original file
    });
  }

  async generateUploadUrl(userId: string): Promise<{
    uploadUrl: string;
    uploadParams: Record<string, any>;
    expiresAt: Date;
  }> {
    return this.documentStorage.generateSignedUploadUrl({
      documentType: DocumentType.NIN,
      userId,
      expiresIn: 3600, // 1 hour
      maxFileSize: 5 * 1024 * 1024, // 5MB
      allowedFormats: ['jpg', 'jpeg', 'png', 'pdf'],
    });
  }
}
