import { Injectable, Inject, Logger } from '@nestjs/common';
import { KycVerification, DocumentType, VerificationType } from '../../entities/kyc-verification.entity';
import { 
  KYC_VERIFICATION_REPOSITORY, 
  KycVerificationRepositoryPort 
} from '../../ports/kyc/kyc-verification-repository.port';
import { TinVerificationRequest } from '../../ports/kyc/kyc-provider.port';
import { KycProviderManager } from '../../../infrastructure/kyc/kyc-provider-manager';
import { CountryKycConfigService } from '../../../infrastructure/kyc/country-kyc-config.service';

export interface VerifyTinInput {
  userId: string;
  tin: string;
  countryCode?: string;
}

export interface VerifyTinOutput {
  verification: KycVerification;
  isVerified: boolean;
  extractedData?: Record<string, any>;
  providerUsed: string;
  responseTime: number;
}

@Injectable()
export class VerifyTinUseCase {
  private readonly logger = new Logger(VerifyTinUseCase.name);

  constructor(
    @Inject(KYC_VERIFICATION_REPOSITORY)
    private readonly verificationRepository: KycVerificationRepositoryPort,
    private readonly providerManager: KycProviderManager,
    private readonly countryConfigService: CountryKycConfigService,
  ) {}

  async execute(input: VerifyTinInput): Promise<VerifyTinOutput> {
    const startTime = Date.now();
    const countryCode = input.countryCode || '234'; // Default to Nigeria

    this.logger.log(`Starting TIN verification for user ${input.userId}`, {
      userId: input.userId,
      countryCode,
      tin: input.tin.substring(0, 4) + '****', // Log only first 4 characters for security
    });

    try {
      // Validate input
      this.validateInput(input, countryCode);

      // Check if user already has a verified TIN
      const existingVerification = await this.verificationRepository.getUserLatestVerification(
        input.userId,
        DocumentType.TIN
      );

      if (existingVerification?.isVerified && 
          !existingVerification.isExpired && 
          existingVerification.documentNumber === input.tin) {
        this.logger.log(`User ${input.userId} already has verified TIN`, {
          verificationId: existingVerification.id,
        });

        return {
          verification: existingVerification,
          isVerified: true,
          extractedData: existingVerification.verificationData,
          providerUsed: existingVerification.providerId,
          responseTime: Date.now() - startTime,
        };
      }

      // Prepare verification request
      const verificationRequest: TinVerificationRequest = {
        documentType: DocumentType.TIN,
        documentNumber: input.tin,
      };

      // Perform verification using provider manager
      const { result, log } = await this.providerManager.verifyDocument(
        countryCode,
        DocumentType.TIN,
        verificationRequest
      );

      // Create verification entity
      const verification = KycVerification.create({
        userId: input.userId,
        verificationType: VerificationType.BUSINESS,
        documentType: DocumentType.TIN,
        countryCode,
        providerId: log.attempts[log.attempts.length - 1]?.providerId || 'unknown',
        providerReference: result.providerReference,
        documentNumber: input.tin,
        verificationData: result.extractedData,
      });

      // Update verification status based on result
      let finalVerification: KycVerification;
      if (result.success && result.verified) {
        finalVerification = verification.markAsVerified(result.providerResponse, result.extractedData);
        this.logger.log(`TIN verification successful for user ${input.userId}`, {
          verificationId: verification.id,
          providerId: finalVerification.providerId,
        });
      } else if (result.requiresManualReview) {
        finalVerification = verification.markForManualReview(
          result.failureReason || 'Verification requires manual review'
        );
        this.logger.warn(`TIN verification requires manual review for user ${input.userId}`, {
          verificationId: verification.id,
          reason: result.failureReason,
        });
      } else {
        finalVerification = verification.markAsFailed(
          result.failureReason || 'Verification failed',
          result.providerResponse
        );
        this.logger.warn(`TIN verification failed for user ${input.userId}`, {
          verificationId: verification.id,
          reason: result.failureReason,
        });
      }

      // Save verification to repository
      const savedVerification = await this.verificationRepository.save(finalVerification);

      const responseTime = Date.now() - startTime;
      this.logger.log(`TIN verification completed for user ${input.userId}`, {
        verificationId: savedVerification.id,
        status: savedVerification.status,
        responseTime,
      });

      return {
        verification: savedVerification,
        isVerified: savedVerification.isVerified,
        extractedData: savedVerification.verificationData,
        providerUsed: savedVerification.providerId,
        responseTime,
      };

    } catch (error) {
      const responseTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      this.logger.error(`TIN verification failed for user ${input.userId}`, {
        error: errorMessage,
        responseTime,
      });

      throw error;
    }
  }

  private validateInput(input: VerifyTinInput, countryCode: string): void {
    if (!input.userId || input.userId.trim().length === 0) {
      throw new Error('User ID is required');
    }

    if (!input.tin || input.tin.trim().length === 0) {
      throw new Error('TIN is required');
    }

    // Validate TIN format using country configuration
    const isValid = this.countryConfigService.validateDocumentNumber(
      countryCode,
      DocumentType.TIN,
      input.tin
    );

    if (!isValid) {
      const rule = this.countryConfigService.getValidationRule(countryCode, DocumentType.TIN);
      throw new Error(`Invalid TIN format: ${rule.description}`);
    }

    // Check if TIN verification is supported in the country
    if (!this.countryConfigService.isDocumentSupported(countryCode, DocumentType.TIN)) {
      throw new Error(`TIN verification is not supported in country ${countryCode}`);
    }
  }

  async getUserTinStatus(userId: string): Promise<{
    hasVerifiedTin: boolean;
    verification?: KycVerification;
    lastAttempt?: Date;
  }> {
    const verifications = await this.verificationRepository.findByUserIdAndDocumentType(
      userId,
      DocumentType.TIN
    );

    if (verifications.length === 0) {
      return { hasVerifiedTin: false };
    }

    // Sort by creation date, most recent first
    verifications.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
    const latestVerification = verifications[0];

    return {
      hasVerifiedTin: latestVerification.isVerified && !latestVerification.isExpired,
      verification: latestVerification,
      lastAttempt: latestVerification.createdAt,
    };
  }

  async getUserTinVerifications(userId: string): Promise<KycVerification[]> {
    const verifications = await this.verificationRepository.findByUserIdAndDocumentType(
      userId,
      DocumentType.TIN
    );

    return verifications.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }

  async retryTinVerification(verificationId: string): Promise<VerifyTinOutput> {
    const existingVerification = await this.verificationRepository.findById(verificationId);
    if (!existingVerification) {
      throw new Error('Verification not found');
    }

    if (existingVerification.documentType !== DocumentType.TIN) {
      throw new Error('Verification is not for TIN document type');
    }

    if (existingVerification.isVerified) {
      throw new Error('Verification is already successful');
    }

    // Retry with the same parameters
    return this.execute({
      userId: existingVerification.userId,
      tin: existingVerification.documentNumber!,
      countryCode: existingVerification.countryCode,
    });
  }
}
