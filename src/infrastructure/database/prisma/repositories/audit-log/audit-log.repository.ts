import { Injectable, Inject } from '@nestjs/common';
import { PrismaClient, AuditAction as PrismaAuditAction } from '@prisma/client';
import {
  AuditLogRepositoryPort,
  AuditLogFilters,
  AuditLogPagination,
  AuditLogQueryResult,
} from '../../../../../core/ports/audit/audit-log-repository.port';
import { AuditLog } from '../../../../../core/entities/audit-log.entity';
import { AuditAction, AuditEntity } from '../../../../../core/constants/audit-actions';
import { DATABASE_PORT, DatabasePort } from '../../../../../core/ports/database/database.port';

@Injectable()
export class PrismaAuditLogRepository implements AuditLogRepositoryPort {
  private prisma: PrismaClient;

  constructor(
    @Inject(DATABASE_PORT)
    private readonly database: DatabasePort,
  ) {
    this.prisma = this.database.getClient() as PrismaClient;
  }

  private mapToDomain(prismaAuditLog: any): AuditLog {
    return new AuditLog(
      prismaAuditLog.id,
      prismaAuditLog.userId,
      prismaAuditLog.action as AuditAction,
      prismaAuditLog.entity as AuditEntity,
      prismaAuditLog.entityId,
      prismaAuditLog.accountId,
      prismaAuditLog.walletId,
      prismaAuditLog.beforeValues,
      prismaAuditLog.afterValues,
      prismaAuditLog.metadata,
      prismaAuditLog.ipAddress,
      prismaAuditLog.userAgent,
      prismaAuditLog.sessionId,
      prismaAuditLog.createdAt,
    );
  }

  private mapToPrisma(auditLog: AuditLog) {
    return {
      id: auditLog.id,
      userId: auditLog.userId,
      action: auditLog.action as PrismaAuditAction,
      entity: auditLog.entity,
      entityId: auditLog.entityId,
      accountId: auditLog.accountId,
      walletId: auditLog.walletId,
      beforeValues: auditLog.beforeValues,
      afterValues: auditLog.afterValues,
      metadata: auditLog.metadata,
      ipAddress: auditLog.ipAddress,
      userAgent: auditLog.userAgent,
      sessionId: auditLog.sessionId,
      createdAt: auditLog.createdAt,
    };
  }

  private buildWhereClause(filters: AuditLogFilters) {
    const where: any = {};

    if (filters.userId) {
      where.userId = filters.userId;
    }

    if (filters.accountId) {
      where.accountId = filters.accountId;
    }

    if (filters.walletId) {
      where.walletId = filters.walletId;
    }

    if (filters.entity) {
      where.entity = filters.entity;
    }

    if (filters.action) {
      where.action = filters.action as PrismaAuditAction;
    }

    if (filters.entityId) {
      where.entityId = filters.entityId;
    }

    if (filters.startDate || filters.endDate) {
      where.createdAt = {};
      if (filters.startDate) {
        where.createdAt.gte = filters.startDate;
      }
      if (filters.endDate) {
        where.createdAt.lte = filters.endDate;
      }
    }

    if (filters.ipAddress) {
      where.ipAddress = filters.ipAddress;
    }

    return where;
  }

  async save(auditLog: AuditLog): Promise<AuditLog> {
    const savedAuditLog = await this.prisma.auditLog.create({
      data: this.mapToPrisma(auditLog),
    });
    return this.mapToDomain(savedAuditLog);
  }

  async findById(id: string): Promise<AuditLog | null> {
    const auditLog = await this.prisma.auditLog.findUnique({
      where: { id },
    });
    return auditLog ? this.mapToDomain(auditLog) : null;
  }

  async findMany(
    filters: AuditLogFilters,
    pagination: AuditLogPagination,
  ): Promise<AuditLogQueryResult> {
    const where = this.buildWhereClause(filters);
    const skip = (pagination.page - 1) * pagination.limit;

    const [auditLogs, total] = await Promise.all([
      this.prisma.auditLog.findMany({
        where,
        skip,
        take: pagination.limit,
        orderBy: {
          [pagination.sortBy || 'createdAt']: pagination.sortOrder || 'desc',
        },
      }),
      this.prisma.auditLog.count({ where }),
    ]);

    return {
      auditLogs: auditLogs.map(this.mapToDomain),
      total,
      page: pagination.page,
      limit: pagination.limit,
      totalPages: Math.ceil(total / pagination.limit),
    };
  }

  async findForUser(
    requestingUserId: string,
    filters: AuditLogFilters,
    pagination: AuditLogPagination,
    accessibleAccountIds?: string[],
    accessibleWalletIds?: string[],
  ): Promise<AuditLogQueryResult> {
    const where = this.buildWhereClause(filters);

    // Add permission filtering
    const permissionFilter: any = {
      OR: [
        // User's own actions
        { userId: requestingUserId },
        // Actions on accessible accounts
        ...(accessibleAccountIds && accessibleAccountIds.length > 0
          ? [{ accountId: { in: accessibleAccountIds } }]
          : []),
        // Actions on accessible wallets
        ...(accessibleWalletIds && accessibleWalletIds.length > 0
          ? [{ walletId: { in: accessibleWalletIds } }]
          : []),
      ],
    };

    const finalWhere = {
      ...where,
      ...permissionFilter,
    };

    const skip = (pagination.page - 1) * pagination.limit;

    const [auditLogs, total] = await Promise.all([
      this.prisma.auditLog.findMany({
        where: finalWhere,
        skip,
        take: pagination.limit,
        orderBy: {
          [pagination.sortBy || 'createdAt']: pagination.sortOrder || 'desc',
        },
      }),
      this.prisma.auditLog.count({ where: finalWhere }),
    ]);

    return {
      auditLogs: auditLogs.map(this.mapToDomain),
      total,
      page: pagination.page,
      limit: pagination.limit,
      totalPages: Math.ceil(total / pagination.limit),
    };
  }

  async findByAccountId(
    accountId: string,
    filters: Omit<AuditLogFilters, 'accountId'> = {},
    pagination: AuditLogPagination = { page: 1, limit: 50 },
  ): Promise<AuditLogQueryResult> {
    return this.findMany({ ...filters, accountId }, pagination);
  }

  async findByWalletId(
    walletId: string,
    filters: Omit<AuditLogFilters, 'walletId'> = {},
    pagination: AuditLogPagination = { page: 1, limit: 50 },
  ): Promise<AuditLogQueryResult> {
    return this.findMany({ ...filters, walletId }, pagination);
  }

  async findByEntity(
    entity: AuditEntity,
    entityId: string,
    filters: Omit<AuditLogFilters, 'entity' | 'entityId'> = {},
    pagination: AuditLogPagination = { page: 1, limit: 50 },
  ): Promise<AuditLogQueryResult> {
    return this.findMany({ ...filters, entity, entityId }, pagination);
  }

  async count(filters: AuditLogFilters): Promise<number> {
    const where = this.buildWhereClause(filters);
    return this.prisma.auditLog.count({ where });
  }

  async deleteOlderThan(date: Date): Promise<number> {
    const result = await this.prisma.auditLog.deleteMany({
      where: {
        createdAt: {
          lt: date,
        },
      },
    });
    return result.count;
  }

  async getStatistics(filters: AuditLogFilters): Promise<{
    totalLogs: number;
    actionBreakdown: Record<AuditAction, number>;
    entityBreakdown: Record<AuditEntity, number>;
    dailyActivity: Array<{ date: string; count: number }>;
  }> {
    const where = this.buildWhereClause(filters);

    const [totalLogs, actionStats, entityStats, dailyStats] = await Promise.all([
      this.prisma.auditLog.count({ where }),
      this.prisma.auditLog.groupBy({
        by: ['action'],
        where,
        _count: true,
      }),
      this.prisma.auditLog.groupBy({
        by: ['entity'],
        where,
        _count: true,
      }),
      this.prisma.auditLog.groupBy({
        by: ['createdAt'],
        where,
        _count: true,
        orderBy: { createdAt: 'desc' },
        take: 30, // Last 30 days
      }),
    ]);

    const actionBreakdown = Object.values(AuditAction).reduce(
      (acc, action) => {
        acc[action] = 0;
        return acc;
      },
      {} as Record<AuditAction, number>,
    );

    actionStats.forEach((stat) => {
      actionBreakdown[stat.action as AuditAction] = stat._count;
    });

    const entityBreakdown = Object.values(AuditEntity).reduce(
      (acc, entity) => {
        acc[entity] = 0;
        return acc;
      },
      {} as Record<AuditEntity, number>,
    );

    entityStats.forEach((stat) => {
      entityBreakdown[stat.entity as AuditEntity] = stat._count;
    });

    const dailyActivity = dailyStats.map((stat) => ({
      date: stat.createdAt.toISOString().split('T')[0],
      count: stat._count,
    }));

    return {
      totalLogs,
      actionBreakdown,
      entityBreakdown,
      dailyActivity,
    };
  }
}
