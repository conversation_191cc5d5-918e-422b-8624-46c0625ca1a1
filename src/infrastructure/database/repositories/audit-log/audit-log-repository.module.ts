import { Module } from '@nestjs/common';
import { AUDIT_LOG_REPOSITORY } from '../../../../core/ports/audit/audit-log-repository.port';
import { PrismaAuditLogRepository } from '../../../database/prisma/repositories/audit-log/audit-log.repository';
import { PrismaDatabase } from '../../../database/prisma/prisma-database';
import { DATABASE_PORT } from '../../../../core/ports/database/database.port';

@Module({
  providers: [
    {
      provide: AUDIT_LOG_REPOSITORY,
      useClass: PrismaAuditLogRepository,
    },
    {
      provide: DATABASE_PORT,
      useClass: PrismaDatabase,
    },
  ],
  exports: [AUDIT_LOG_REPOSITORY, DATABASE_PORT],
})
export class AuditRepositoryModule {}
