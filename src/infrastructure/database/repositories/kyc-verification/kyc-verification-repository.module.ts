import { Module } from '@nestjs/common';
import { KYC_VERIFICATION_REPOSITORY } from '../../../../core/ports/kyc/kyc-verification-repository.port';
import { PrismaKycVerificationRepository } from './prisma-kyc-verification.repository';
import { PrismaDatabase } from '../../prisma/prisma-database';

@Module({
  providers: [
    PrismaDatabase,
    {
      provide: KYC_VERIFICATION_REPOSITORY,
      useClass: PrismaKycVerificationRepository,
    },
  ],
  exports: [KYC_VERIFICATION_REPOSITORY],
})
export class KycVerificationRepositoryModule {}
