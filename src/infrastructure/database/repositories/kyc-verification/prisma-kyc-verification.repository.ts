import { Injectable, Logger } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';
import {
  KycVerificationRepositoryPort,
  KycVerificationFilters,
  KycVerificationSummary,
} from '../../../../core/ports/kyc/kyc-verification-repository.port';
import { 
  KycVerification, 
  DocumentType, 
  VerificationStatus, 
  VerificationType 
} from '../../../../core/entities/kyc-verification.entity';
import { PrismaDatabase } from '../../prisma/prisma-database';

@Injectable()
export class PrismaKycVerificationRepository implements KycVerificationRepositoryPort {
  private readonly logger = new Logger(PrismaKycVerificationRepository.name);

  constructor(private readonly prismaDatabase: PrismaDatabase) {}

  private get prisma(): PrismaClient {
    return this.prismaDatabase.getClient();
  }

  async save(verification: KycVerification): Promise<KycVerification> {
    try {
      const data = {
        id: verification.id,
        userId: verification.userId,
        verificationType: verification.verificationType,
        documentType: verification.documentType,
        status: verification.status,
        countryCode: verification.countryCode,
        providerId: verification.providerId,
        providerReference: verification.providerReference,
        documentNumber: verification.documentNumber,
        documentImageUrl: verification.documentImageUrl,
        verificationData: verification.verificationData,
        providerResponse: verification.providerResponse,
        failureReason: verification.failureReason,
        verifiedAt: verification.verifiedAt,
        expiresAt: verification.expiresAt,
        createdAt: verification.createdAt,
        updatedAt: verification.updatedAt,
      };

      const saved = await this.prisma.kycVerification.create({ data });
      return KycVerification.fromPrisma(saved);
    } catch (error) {
      this.logger.error('Failed to save KYC verification', {
        verificationId: verification.id,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  async findById(id: string): Promise<KycVerification | null> {
    try {
      const verification = await this.prisma.kycVerification.findUnique({
        where: { id },
      });

      return verification ? KycVerification.fromPrisma(verification) : null;
    } catch (error) {
      this.logger.error('Failed to find KYC verification by ID', {
        id,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  async findByProviderReference(providerReference: string): Promise<KycVerification | null> {
    try {
      const verification = await this.prisma.kycVerification.findFirst({
        where: { providerReference },
      });

      return verification ? KycVerification.fromPrisma(verification) : null;
    } catch (error) {
      this.logger.error('Failed to find KYC verification by provider reference', {
        providerReference,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  async update(verification: KycVerification): Promise<KycVerification> {
    try {
      const data = {
        verificationType: verification.verificationType,
        documentType: verification.documentType,
        status: verification.status,
        countryCode: verification.countryCode,
        providerId: verification.providerId,
        providerReference: verification.providerReference,
        documentNumber: verification.documentNumber,
        documentImageUrl: verification.documentImageUrl,
        verificationData: verification.verificationData,
        providerResponse: verification.providerResponse,
        failureReason: verification.failureReason,
        verifiedAt: verification.verifiedAt,
        expiresAt: verification.expiresAt,
        updatedAt: new Date(),
      };

      const updated = await this.prisma.kycVerification.update({
        where: { id: verification.id },
        data,
      });

      return KycVerification.fromPrisma(updated);
    } catch (error) {
      this.logger.error('Failed to update KYC verification', {
        verificationId: verification.id,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  async delete(id: string): Promise<void> {
    try {
      await this.prisma.kycVerification.delete({
        where: { id },
      });
    } catch (error) {
      this.logger.error('Failed to delete KYC verification', {
        id,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  async findByUserId(userId: string): Promise<KycVerification[]> {
    try {
      const verifications = await this.prisma.kycVerification.findMany({
        where: { userId },
        orderBy: { createdAt: 'desc' },
      });

      return verifications.map(v => KycVerification.fromPrisma(v));
    } catch (error) {
      this.logger.error('Failed to find KYC verifications by user ID', {
        userId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  async findByUserIdAndDocumentType(userId: string, documentType: DocumentType): Promise<KycVerification[]> {
    try {
      const verifications = await this.prisma.kycVerification.findMany({
        where: { 
          userId,
          documentType,
        },
        orderBy: { createdAt: 'desc' },
      });

      return verifications.map(v => KycVerification.fromPrisma(v));
    } catch (error) {
      this.logger.error('Failed to find KYC verifications by user ID and document type', {
        userId,
        documentType,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  async findByUserIdAndVerificationType(userId: string, verificationType: VerificationType): Promise<KycVerification[]> {
    try {
      const verifications = await this.prisma.kycVerification.findMany({
        where: { 
          userId,
          verificationType,
        },
        orderBy: { createdAt: 'desc' },
      });

      return verifications.map(v => KycVerification.fromPrisma(v));
    } catch (error) {
      this.logger.error('Failed to find KYC verifications by user ID and verification type', {
        userId,
        verificationType,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  async findByFilters(filters: KycVerificationFilters): Promise<KycVerification[]> {
    try {
      const where: any = {};

      if (filters.userId) where.userId = filters.userId;
      if (filters.documentType) where.documentType = filters.documentType;
      if (filters.verificationType) where.verificationType = filters.verificationType;
      if (filters.status) where.status = filters.status;
      if (filters.countryCode) where.countryCode = filters.countryCode;
      if (filters.providerId) where.providerId = filters.providerId;
      
      if (filters.createdAfter || filters.createdBefore) {
        where.createdAt = {};
        if (filters.createdAfter) where.createdAt.gte = filters.createdAfter;
        if (filters.createdBefore) where.createdAt.lte = filters.createdBefore;
      }

      if (filters.verifiedAfter || filters.verifiedBefore) {
        where.verifiedAt = {};
        if (filters.verifiedAfter) where.verifiedAt.gte = filters.verifiedAfter;
        if (filters.verifiedBefore) where.verifiedAt.lte = filters.verifiedBefore;
      }

      const verifications = await this.prisma.kycVerification.findMany({
        where,
        orderBy: { createdAt: 'desc' },
      });

      return verifications.map(v => KycVerification.fromPrisma(v));
    } catch (error) {
      this.logger.error('Failed to find KYC verifications by filters', {
        filters,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  async findPendingVerifications(): Promise<KycVerification[]> {
    return this.findByFilters({
      status: VerificationStatus.PENDING,
    });
  }

  async findExpiredVerifications(): Promise<KycVerification[]> {
    try {
      const verifications = await this.prisma.kycVerification.findMany({
        where: {
          expiresAt: {
            lt: new Date(),
          },
          status: {
            not: VerificationStatus.EXPIRED,
          },
        },
        orderBy: { createdAt: 'desc' },
      });

      return verifications.map(v => KycVerification.fromPrisma(v));
    } catch (error) {
      this.logger.error('Failed to find expired KYC verifications', {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  async findVerificationsRequiringManualReview(): Promise<KycVerification[]> {
    return this.findByFilters({
      status: VerificationStatus.REQUIRES_MANUAL_REVIEW,
    });
  }

  async getUserKycSummary(userId: string): Promise<KycVerificationSummary> {
    try {
      const verifications = await this.findByUserId(userId);
      
      const totalVerifications = verifications.length;
      const verifiedCount = verifications.filter(v => v.isVerified && !v.isExpired).length;
      const pendingCount = verifications.filter(v => v.isPending).length;
      const failedCount = verifications.filter(v => v.isFailed).length;

      const identityVerified = verifications.some(v => 
        v.verificationType === VerificationType.IDENTITY && v.isVerified && !v.isExpired
      );
      const addressVerified = verifications.some(v => 
        v.verificationType === VerificationType.ADDRESS && v.isVerified && !v.isExpired
      );
      const businessVerified = verifications.some(v => 
        v.verificationType === VerificationType.BUSINESS && v.isVerified && !v.isExpired
      );
      const facialVerified = verifications.some(v => 
        v.verificationType === VerificationType.FACIAL && v.isVerified && !v.isExpired
      );

      let overallKycStatus: 'incomplete' | 'partial' | 'complete';
      if (identityVerified && addressVerified && businessVerified && facialVerified) {
        overallKycStatus = 'complete';
      } else if (identityVerified || addressVerified || businessVerified || facialVerified) {
        overallKycStatus = 'partial';
      } else {
        overallKycStatus = 'incomplete';
      }

      const lastVerificationDate = verifications.length > 0 
        ? verifications[0].createdAt 
        : undefined;

      return {
        userId,
        totalVerifications,
        verifiedCount,
        pendingCount,
        failedCount,
        identityVerified,
        addressVerified,
        businessVerified,
        facialVerified,
        overallKycStatus,
        lastVerificationDate,
      };
    } catch (error) {
      this.logger.error('Failed to get user KYC summary', {
        userId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  async getUserLatestVerification(userId: string, documentType: DocumentType): Promise<KycVerification | null> {
    try {
      const verification = await this.prisma.kycVerification.findFirst({
        where: { 
          userId,
          documentType,
        },
        orderBy: { createdAt: 'desc' },
      });

      return verification ? KycVerification.fromPrisma(verification) : null;
    } catch (error) {
      this.logger.error('Failed to get user latest verification', {
        userId,
        documentType,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  async getUserVerifiedDocuments(userId: string): Promise<DocumentType[]> {
    try {
      const verifications = await this.prisma.kycVerification.findMany({
        where: { 
          userId,
          status: VerificationStatus.VERIFIED,
        },
        select: { documentType: true },
        distinct: ['documentType'],
      });

      return verifications.map(v => v.documentType as DocumentType);
    } catch (error) {
      this.logger.error('Failed to get user verified documents', {
        userId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  async getVerificationCountByStatus(status: VerificationStatus): Promise<number> {
    try {
      return await this.prisma.kycVerification.count({
        where: { status },
      });
    } catch (error) {
      this.logger.error('Failed to get verification count by status', {
        status,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  async getVerificationCountByProvider(providerId: string): Promise<number> {
    try {
      return await this.prisma.kycVerification.count({
        where: { providerId },
      });
    } catch (error) {
      this.logger.error('Failed to get verification count by provider', {
        providerId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  async getVerificationCountByCountry(countryCode: string): Promise<number> {
    try {
      return await this.prisma.kycVerification.count({
        where: { countryCode },
      });
    } catch (error) {
      this.logger.error('Failed to get verification count by country', {
        countryCode,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  async getVerificationCountByDocumentType(documentType: DocumentType): Promise<number> {
    try {
      return await this.prisma.kycVerification.count({
        where: { documentType },
      });
    } catch (error) {
      this.logger.error('Failed to get verification count by document type', {
        documentType,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  async markExpiredVerifications(): Promise<number> {
    try {
      const result = await this.prisma.kycVerification.updateMany({
        where: {
          expiresAt: {
            lt: new Date(),
          },
          status: {
            not: VerificationStatus.EXPIRED,
          },
        },
        data: {
          status: VerificationStatus.EXPIRED,
          updatedAt: new Date(),
        },
      });

      return result.count;
    } catch (error) {
      this.logger.error('Failed to mark expired verifications', {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  async deleteOldVerifications(olderThan: Date): Promise<number> {
    try {
      const result = await this.prisma.kycVerification.deleteMany({
        where: {
          createdAt: {
            lt: olderThan,
          },
        },
      });

      return result.count;
    } catch (error) {
      this.logger.error('Failed to delete old verifications', {
        olderThan,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  async findByProviderId(providerId: string): Promise<KycVerification[]> {
    return this.findByFilters({ providerId });
  }

  async getProviderSuccessRate(providerId: string, fromDate?: Date): Promise<number> {
    try {
      const where: any = { providerId };
      if (fromDate) {
        where.createdAt = { gte: fromDate };
      }

      const total = await this.prisma.kycVerification.count({ where });
      
      if (total === 0) return 0;

      const successful = await this.prisma.kycVerification.count({
        where: {
          ...where,
          status: VerificationStatus.VERIFIED,
        },
      });

      return (successful / total) * 100;
    } catch (error) {
      this.logger.error('Failed to get provider success rate', {
        providerId,
        fromDate,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }
}
