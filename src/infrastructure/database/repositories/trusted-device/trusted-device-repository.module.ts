import { Module } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';
import { PrismaTrustedDeviceRepository } from '../../prisma/repositories/trusted-device/trusted-device.repository';

export const TRUSTED_DEVICE_REPOSITORY = 'TRUSTED_DEVICE_REPOSITORY';

@Module({
  providers: [
    PrismaClient,
    {
      provide: TRUSTED_DEVICE_REPOSITORY,
      useFactory: (prisma: PrismaClient) => new PrismaTrustedDeviceRepository(prisma),
      inject: [PrismaClient],
    },
  ],
  exports: [TRUSTED_DEVICE_REPOSITORY],
})
export class TrustedDeviceRepositoryModule {}
