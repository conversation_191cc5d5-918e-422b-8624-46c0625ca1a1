import { Injectable } from '@nestjs/common';
import { DocumentType } from '../../core/entities/kyc-verification.entity';
import { KYC_PROVIDERS, KycProviderId } from '../../core/constants/kyc-providers.constants';

export interface DocumentValidationRule {
  format: RegExp;
  length?: number;
  minLength?: number;
  maxLength?: number;
  required: boolean;
  requiresImage: boolean;
  description: string;
}

export interface ProviderConfig {
  primary: KycProviderId;
  fallback: KycProviderId[];
  config: {
    endpoint?: string;
    requiresImage: boolean;
    timeout?: number;
    retryAttempts?: number;
    [key: string]: any;
  };
}

export interface CountryKycConfig {
  countryCode: string;
  countryName: string;
  supportedDocuments: DocumentType[];
  providers: {
    [documentType: string]: ProviderConfig;
  };
  validationRules: {
    [documentType: string]: DocumentValidationRule;
  };
  kycRequirements: {
    minimumIdentityDocuments: number;
    minimumAddressDocuments: number;
    minimumBusinessDocuments: number;
    facialVerificationRequired: boolean;
    documentExpiryMonths: number;
  };
}

@Injectable()
export class CountryKycConfigService {
  private readonly configs: Map<string, CountryKycConfig> = new Map();

  constructor() {
    this.initializeConfigs();
  }

  private initializeConfigs(): void {
    // Nigeria Configuration
    const nigeriaConfig: CountryKycConfig = {
      countryCode: '234',
      countryName: 'Nigeria',
      supportedDocuments: [
        DocumentType.BVN,
        DocumentType.NIN,
        DocumentType.DRIVERS_LICENSE,
        DocumentType.INTERNATIONAL_PASSPORT,
        DocumentType.VOTERS_CARD,
        DocumentType.UTILITY_BILL,
        DocumentType.BANK_STATEMENT,
        DocumentType.CAC,
        DocumentType.TIN,
        DocumentType.FACIAL_IMAGE,
        DocumentType.SELFIE,
      ],
      providers: {
        [DocumentType.BVN]: {
          primary: KYC_PROVIDERS.PREMBLY,
          fallback: [KYC_PROVIDERS.ZEEH],
          config: {
            endpoint: '/bvn',
            requiresImage: false,
            timeout: 30000,
            retryAttempts: 2,
          },
        },
        [DocumentType.NIN]: {
          primary: KYC_PROVIDERS.ZEEH,
          fallback: [KYC_PROVIDERS.PREMBLY],
          config: {
            endpoint: '/nin',
            requiresImage: true,
            timeout: 30000,
            retryAttempts: 2,
          },
        },
        [DocumentType.DRIVERS_LICENSE]: {
          primary: KYC_PROVIDERS.ZEEH,
          fallback: [KYC_PROVIDERS.PREMBLY],
          config: {
            endpoint: '/drivers-license',
            requiresImage: true,
            timeout: 45000,
            retryAttempts: 2,
          },
        },
        [DocumentType.INTERNATIONAL_PASSPORT]: {
          primary: KYC_PROVIDERS.PREMBLY,
          fallback: [KYC_PROVIDERS.ZEEH],
          config: {
            endpoint: '/passport',
            requiresImage: true,
            timeout: 45000,
            retryAttempts: 2,
          },
        },
        [DocumentType.VOTERS_CARD]: {
          primary: KYC_PROVIDERS.PREMBLY,
          fallback: [KYC_PROVIDERS.ZEEH],
          config: {
            endpoint: '/voters-card',
            requiresImage: true,
            timeout: 45000,
            retryAttempts: 2,
          },
        },
        [DocumentType.CAC]: {
          primary: KYC_PROVIDERS.ZEEH,
          fallback: [KYC_PROVIDERS.PREMBLY],
          config: {
            endpoint: '/cac',
            requiresImage: false,
            timeout: 30000,
            retryAttempts: 2,
          },
        },
        [DocumentType.TIN]: {
          primary: KYC_PROVIDERS.ZEEH,
          fallback: [KYC_PROVIDERS.PREMBLY],
          config: {
            endpoint: '/tin',
            requiresImage: false,
            timeout: 30000,
            retryAttempts: 2,
          },
        },
      },
      validationRules: {
        [DocumentType.BVN]: {
          format: /^\d{11}$/,
          length: 11,
          required: true,
          requiresImage: false,
          description: 'Bank Verification Number must be exactly 11 digits',
        },
        [DocumentType.NIN]: {
          format: /^\d{11}$/,
          length: 11,
          required: true,
          requiresImage: true,
          description:
            'National Identification Number must be exactly 11 digits',
        },
        [DocumentType.DRIVERS_LICENSE]: {
          format: /^[A-Z]{3}[0-9]{6}[A-Z]{2}$/,
          required: true,
          requiresImage: true,
          description: 'Nigerian Drivers License format: AAA123456AA',
        },
        [DocumentType.INTERNATIONAL_PASSPORT]: {
          format: /^[A-Z]\d{8}$/,
          length: 9,
          required: true,
          requiresImage: true,
          description: 'Nigerian International Passport format: A********',
        },
        [DocumentType.VOTERS_CARD]: {
          format: /^\d{19}$/,
          length: 19,
          required: true,
          requiresImage: true,
          description: 'Permanent Voters Card number must be 19 digits',
        },
        [DocumentType.CAC]: {
          format: /^RC\d{6,7}$/,
          minLength: 8,
          maxLength: 9,
          required: true,
          requiresImage: false,
          description:
            'Corporate Affairs Commission registration number format: RC123456',
        },
        [DocumentType.TIN]: {
          format: /^\d{8}-\d{4}$/,
          length: 13,
          required: true,
          requiresImage: false,
          description: 'Tax Identification Number format: ********-1234',
        },
        [DocumentType.UTILITY_BILL]: {
          format: /.+/,
          required: false,
          requiresImage: true,
          description: 'Utility bill image required for address verification',
        },
        [DocumentType.BANK_STATEMENT]: {
          format: /.+/,
          required: false,
          requiresImage: true,
          description: 'Bank statement image required for address verification',
        },
        [DocumentType.FACIAL_IMAGE]: {
          format: /.+/,
          required: true,
          requiresImage: true,
          description: 'Facial image required for facial verification',
        },
      },
      kycRequirements: {
        minimumIdentityDocuments: 1,
        minimumAddressDocuments: 1,
        minimumBusinessDocuments: 0,
        facialVerificationRequired: true,
        documentExpiryMonths: 24,
      },
    };

    this.configs.set('234', nigeriaConfig);

    // Ghana Configuration
    const ghanaConfig: CountryKycConfig = {
      countryCode: '233',
      countryName: 'Ghana',
      supportedDocuments: [
        DocumentType.DRIVERS_LICENSE,
        DocumentType.INTERNATIONAL_PASSPORT,
        DocumentType.VOTERS_CARD,
        DocumentType.TIN,
        DocumentType.FACIAL_IMAGE,
        DocumentType.SELFIE,
      ],
      providers: {
        [DocumentType.DRIVERS_LICENSE]: {
          primary: KYC_PROVIDERS.ZEEH,
          fallback: [KYC_PROVIDERS.PREMBLY],
          config: {
            endpoint: '/drivers-license',
            requiresImage: true,
            timeout: 30000,
            retryAttempts: 2,
          },
        },
        [DocumentType.INTERNATIONAL_PASSPORT]: {
          primary: KYC_PROVIDERS.ZEEH,
          fallback: [KYC_PROVIDERS.PREMBLY],
          config: {
            endpoint: '/passport',
            requiresImage: true,
            timeout: 45000,
            retryAttempts: 2,
          },
        },
        [DocumentType.VOTERS_CARD]: {
          primary: KYC_PROVIDERS.ZEEH,
          fallback: [KYC_PROVIDERS.PREMBLY],
          config: {
            endpoint: '/voters-card',
            requiresImage: true,
            timeout: 45000,
            retryAttempts: 2,
          },
        },
        [DocumentType.TIN]: {
          primary: KYC_PROVIDERS.ZEEH,
          fallback: [],
          config: {
            endpoint: '/tin',
            requiresImage: false,
            timeout: 30000,
            retryAttempts: 2,
          },
        },
      },
      validationRules: {
        [DocumentType.DRIVERS_LICENSE]: {
          format: /^[A-Z0-9]{8,15}$/,
          required: true,
          requiresImage: true,
          description: 'Ghana drivers license number (8-15 alphanumeric characters)',
        },
        [DocumentType.INTERNATIONAL_PASSPORT]: {
          format: /^[A-Z][0-9]{7}$/,
          required: true,
          requiresImage: true,
          description: 'Ghana passport number (1 letter followed by 7 digits)',
        },
        [DocumentType.VOTERS_CARD]: {
          format: /^[0-9]{10}$/,
          required: true,
          requiresImage: true,
          description: 'Ghana voter ID (10 digits)',
        },
        [DocumentType.TIN]: {
          format: /^[A-Z]{3}[0-9]{9}[A-Z]$/,
          required: true,
          requiresImage: false,
          description: 'Ghana TIN (3 letters, 9 digits, 1 letter)',
        },
      },
      kycRequirements: {
        minimumIdentityDocuments: 1,
        minimumAddressDocuments: 0,
        minimumBusinessDocuments: 0,
        facialVerificationRequired: true,
        documentExpiryMonths: 24,
      },
    };

    this.configs.set('233', ghanaConfig);

    // Kenya Configuration
    const kenyaConfig: CountryKycConfig = {
      countryCode: '254',
      countryName: 'Kenya',
      supportedDocuments: [
        DocumentType.NATIONAL_ID,
        DocumentType.DRIVERS_LICENSE,
        DocumentType.INTERNATIONAL_PASSPORT,
        DocumentType.TIN,
        DocumentType.FACIAL_IMAGE,
        DocumentType.SELFIE,
      ],
      providers: {
        [DocumentType.NATIONAL_ID]: {
          primary: KYC_PROVIDERS.ZEEH,
          fallback: [KYC_PROVIDERS.PREMBLY],
          config: {
            endpoint: '/national-id',
            requiresImage: true,
            timeout: 30000,
            retryAttempts: 2,
          },
        },
        [DocumentType.DRIVERS_LICENSE]: {
          primary: KYC_PROVIDERS.ZEEH,
          fallback: [KYC_PROVIDERS.PREMBLY],
          config: {
            endpoint: '/drivers-license',
            requiresImage: true,
            timeout: 30000,
            retryAttempts: 2,
          },
        },
        [DocumentType.INTERNATIONAL_PASSPORT]: {
          primary: KYC_PROVIDERS.ZEEH,
          fallback: [KYC_PROVIDERS.PREMBLY],
          config: {
            endpoint: '/passport',
            requiresImage: true,
            timeout: 45000,
            retryAttempts: 2,
          },
        },
        [DocumentType.TIN]: {
          primary: KYC_PROVIDERS.ZEEH,
          fallback: [],
          config: {
            endpoint: '/kra-pin',
            requiresImage: false,
            timeout: 30000,
            retryAttempts: 2,
          },
        },
      },
      validationRules: {
        [DocumentType.NATIONAL_ID]: {
          format: /^[0-9]{8}$/,
          required: true,
          requiresImage: true,
          description: 'Kenya National ID (8 digits)',
        },
        [DocumentType.DRIVERS_LICENSE]: {
          format: /^[A-Z0-9]{8,12}$/,
          required: true,
          requiresImage: true,
          description: 'Kenya drivers license number (8-12 alphanumeric characters)',
        },
        [DocumentType.INTERNATIONAL_PASSPORT]: {
          format: /^[A-Z][0-9]{7}$/,
          required: true,
          requiresImage: true,
          description: 'Kenya passport number (1 letter followed by 7 digits)',
        },
        [DocumentType.TIN]: {
          format: /^[A-Z][0-9]{9}[A-Z]$/,
          required: true,
          requiresImage: false,
          description: 'Kenya KRA PIN (1 letter, 9 digits, 1 letter)',
        },
      },
      kycRequirements: {
        minimumIdentityDocuments: 1,
        minimumAddressDocuments: 0,
        minimumBusinessDocuments: 0,
        facialVerificationRequired: true,
        documentExpiryMonths: 24,
      },
    };

    this.configs.set('254', kenyaConfig);
  }

  getConfig(countryCode: string): CountryKycConfig {
    const config = this.configs.get(countryCode);
    if (!config) {
      throw new Error(
        `KYC configuration not found for country code: ${countryCode}`,
      );
    }
    return config;
  }

  getSupportedCountries(): string[] {
    return Array.from(this.configs.keys());
  }

  isDocumentSupported(
    countryCode: string,
    documentType: DocumentType,
  ): boolean {
    const config = this.getConfig(countryCode);
    return config.supportedDocuments.includes(documentType);
  }

  getValidationRule(
    countryCode: string,
    documentType: DocumentType,
  ): DocumentValidationRule {
    const config = this.getConfig(countryCode);
    const rule = config.validationRules[documentType];
    if (!rule) {
      throw new Error(
        `Validation rule not found for document type ${documentType} in country ${countryCode}`,
      );
    }
    return rule;
  }

  getProviderConfig(
    countryCode: string,
    documentType: DocumentType,
  ): ProviderConfig {
    const config = this.getConfig(countryCode);
    const providerConfig = config.providers[documentType];
    if (!providerConfig) {
      throw new Error(
        `Provider configuration not found for document type ${documentType} in country ${countryCode}`,
      );
    }
    return providerConfig;
  }

  validateDocumentNumber(
    countryCode: string,
    documentType: DocumentType,
    documentNumber: string,
  ): boolean {
    const rule = this.getValidationRule(countryCode, documentType);

    if (rule.length && documentNumber.length !== rule.length) {
      return false;
    }

    if (rule.minLength && documentNumber.length < rule.minLength) {
      return false;
    }

    if (rule.maxLength && documentNumber.length > rule.maxLength) {
      return false;
    }

    return rule.format.test(documentNumber);
  }

  getKycRequirements(countryCode: string) {
    const config = this.getConfig(countryCode);
    return config.kycRequirements;
  }

  addCountryConfig(config: CountryKycConfig): void {
    this.configs.set(config.countryCode, config);
  }

  updateCountryConfig(
    countryCode: string,
    updates: Partial<CountryKycConfig>,
  ): void {
    const existingConfig = this.getConfig(countryCode);
    const updatedConfig = { ...existingConfig, ...updates };
    this.configs.set(countryCode, updatedConfig);
  }
}
