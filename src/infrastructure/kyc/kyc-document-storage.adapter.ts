import { Injectable, Inject, Logger } from '@nestjs/common';
import {
  DocumentStoragePort,
  DocumentUploadRequest,
  DocumentUploadResult,
  DocumentDeleteRequest,
  DocumentDeleteResult,
  DocumentTransformOptions,
  DocumentAccessOptions,
  SecureDocumentUrl,
} from '../../core/ports/kyc/document-storage.port';
import { DocumentType } from '../../core/entities/kyc-verification.entity';
import { CloudinaryStorageService } from '../storage/cloudinary-storage.service';
import { STORAGE_SERVICE } from '../storage/storage.module';

@Injectable()
export class KycDocumentStorageAdapter implements DocumentStoragePort {
  private readonly logger = new Logger(KycDocumentStorageAdapter.name);

  constructor(
    @Inject(STORAGE_SERVICE)
    private readonly storageService: CloudinaryStorageService,
  ) {}

  async uploadDocument(request: DocumentUploadRequest): Promise<DocumentUploadResult> {
    try {
      const folder = this.getDocumentFolder(request.documentType, request.userId);
      
      const uploadResult = await this.storageService.uploadFile({
        file: request.file,
        fileName: request.fileName,
        mimeType: request.mimeType,
        folder,
        userId: request.userId,
        metadata: {
          document_type: request.documentType,
          ...request.metadata,
        },
        tags: [
          'kyc',
          request.documentType.toLowerCase(),
        ],
      });

      return {
        success: uploadResult.success,
        documentUrl: uploadResult.fileUrl,
        publicId: uploadResult.publicId,
        secureUrl: uploadResult.secureUrl,
        format: uploadResult.format,
        size: uploadResult.size,
        width: uploadResult.width,
        height: uploadResult.height,
        metadata: uploadResult.metadata,
        errorMessage: uploadResult.errorMessage,
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error('Failed to upload KYC document', {
        documentType: request.documentType,
        userId: request.userId,
        error: errorMessage,
      });

      return {
        success: false,
        documentUrl: '',
        publicId: '',
        secureUrl: '',
        format: '',
        size: 0,
        errorMessage,
      };
    }
  }

  async uploadMultipleDocuments(requests: DocumentUploadRequest[]): Promise<DocumentUploadResult[]> {
    const results = await Promise.allSettled(
      requests.map(request => this.uploadDocument(request))
    );

    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        return {
          success: false,
          documentUrl: '',
          publicId: '',
          secureUrl: '',
          format: '',
          size: 0,
          errorMessage: result.reason instanceof Error ? result.reason.message : 'Upload failed',
        };
      }
    });
  }

  async getDocumentUrl(publicId: string, options?: DocumentAccessOptions): Promise<string> {
    return this.storageService.getFileUrl(publicId, {
      secure: options?.secure,
      expiresIn: options?.expiresIn,
      transformation: options?.transformation,
    });
  }

  async getSecureDocumentUrl(publicId: string, options?: DocumentAccessOptions): Promise<SecureDocumentUrl> {
    return this.storageService.getSecureFileUrl(publicId, {
      secure: options?.secure,
      expiresIn: options?.expiresIn,
      transformation: options?.transformation,
    });
  }

  async downloadDocument(publicId: string): Promise<Buffer> {
    return this.storageService.downloadFile(publicId);
  }

  async transformDocument(publicId: string, options: DocumentTransformOptions): Promise<string> {
    return this.storageService.transformFile(publicId, options);
  }

  async generateThumbnail(publicId: string, width = 200, height = 200): Promise<string> {
    return this.storageService.generateThumbnail(publicId, width, height);
  }

  async deleteDocument(request: DocumentDeleteRequest): Promise<DocumentDeleteResult> {
    const result = await this.storageService.deleteFile({
      publicId: request.publicId,
      fileUrl: request.documentUrl,
    });

    return {
      success: result.success,
      publicId: result.publicId,
      errorMessage: result.errorMessage,
    };
  }

  async deleteMultipleDocuments(requests: DocumentDeleteRequest[]): Promise<DocumentDeleteResult[]> {
    const fileRequests = requests.map(req => ({
      publicId: req.publicId,
      fileUrl: req.documentUrl,
    }));

    const results = await this.storageService.deleteMultipleFiles(fileRequests);

    return results.map(result => ({
      success: result.success,
      publicId: result.publicId,
      errorMessage: result.errorMessage,
    }));
  }

  async getDocumentMetadata(publicId: string): Promise<Record<string, any>> {
    return this.storageService.getFileMetadata(publicId);
  }

  async updateDocumentMetadata(publicId: string, metadata: Record<string, any>): Promise<boolean> {
    return this.storageService.updateFileMetadata(publicId, metadata);
  }

  async validateDocument(publicId: string): Promise<boolean> {
    return this.storageService.validateFile(publicId);
  }

  async getDocumentInfo(publicId: string): Promise<{
    publicId: string;
    format: string;
    size: number;
    width?: number;
    height?: number;
    createdAt: Date;
    metadata?: Record<string, any>;
  }> {
    return this.storageService.getFileInfo(publicId);
  }

  async generateSignedUploadUrl(options: {
    documentType: DocumentType;
    userId: string;
    expiresIn?: number;
    maxFileSize?: number;
    allowedFormats?: string[];
  }): Promise<{
    uploadUrl: string;
    uploadParams: Record<string, any>;
    expiresAt: Date;
  }> {
    const folder = this.getDocumentFolder(options.documentType, options.userId);

    return this.storageService.generateSignedUploadUrl({
      folder,
      userId: options.userId,
      expiresIn: options.expiresIn,
      maxFileSize: options.maxFileSize,
      allowedFormats: options.allowedFormats,
      tags: [
        'kyc',
        options.documentType.toLowerCase(),
      ],
    });
  }

  async deleteExpiredDocuments(): Promise<number> {
    return this.storageService.deleteExpiredFiles();
  }

  async deleteUserDocuments(userId: string): Promise<number> {
    return this.storageService.deleteUserFiles(userId);
  }

  private getDocumentFolder(documentType: DocumentType, userId: string): string {
    return `kyc/${documentType.toLowerCase()}/${userId}`;
  }
}
