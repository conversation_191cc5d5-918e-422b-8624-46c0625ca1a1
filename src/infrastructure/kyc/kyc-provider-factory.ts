import { Injectable, Logger } from '@nestjs/common';
import { KycProviderPort } from '../../core/ports/kyc/kyc-provider.port';
import { PremblyKycProvider } from './providers/prembly-kyc.provider';
import { <PERSON>eehKycProvider } from './providers/zeeh-kyc.provider';
import { KYC_PROVIDERS, KycProviderId, isValidProviderId, getProviderDisplayName } from '../../core/constants/kyc-providers.constants';

export interface ProviderCredentials {
  apiKey: string;
  secretKey?: string;
  baseUrl?: string;
  appId?: string;
  [key: string]: any;
}

export interface ProviderConfiguration {
  credentials: ProviderCredentials;
  timeout?: number;
  retryAttempts?: number;
  retryDelay?: number;
  enableLogging?: boolean;
  webhookSecret?: string;
}

@Injectable()
export class KycProviderFactory {
  private readonly logger = new Logger(KycProviderFactory.name);
  private readonly providers: Map<KycProviderId, KycProviderPort> = new Map();
  private readonly configurations: Map<KycProviderId, ProviderConfiguration> = new Map();

  constructor() {
    this.initializeConfigurations();
  }

  private initializeConfigurations(): void {
    // Prembly Configuration
    const premblyConfig: ProviderConfiguration = {
      credentials: {
        apiKey: process.env.PREMBLY_API_KEY || '',
        appId: process.env.PREMBLY_APP_ID || '',
        baseUrl: process.env.PREMBLY_BASE_URL || 'https://api.prembly.com/identitypass/verification',
      },
      timeout: parseInt(process.env.PREMBLY_TIMEOUT || '30000'),
      retryAttempts: parseInt(process.env.PREMBLY_RETRY_ATTEMPTS || '2'),
      retryDelay: parseInt(process.env.PREMBLY_RETRY_DELAY || '1000'),
      enableLogging: process.env.PREMBLY_ENABLE_LOGGING === 'true',
      webhookSecret: process.env.PREMBLY_WEBHOOK_SECRET || '',
    };

    // Zeeh Configuration
    const zeehConfig: ProviderConfiguration = {
      credentials: {
        apiKey: process.env.ZEEH_API_KEY || '',
        secretKey: process.env.ZEEH_SECRET_KEY || '',
        baseUrl: process.env.ZEEH_BASE_URL || 'https://api.usezeeh.com/v1',
      },
      timeout: parseInt(process.env.ZEEH_TIMEOUT || '30000'),
      retryAttempts: parseInt(process.env.ZEEH_RETRY_ATTEMPTS || '2'),
      retryDelay: parseInt(process.env.ZEEH_RETRY_DELAY || '1000'),
      enableLogging: process.env.ZEEH_ENABLE_LOGGING === 'true',
      webhookSecret: process.env.ZEEH_WEBHOOK_SECRET || '',
    };

    this.configurations.set(KYC_PROVIDERS.PREMBLY, premblyConfig);
    this.configurations.set(KYC_PROVIDERS.ZEEH, zeehConfig);
  }

  create(providerId: KycProviderId): KycProviderPort {
    // Check if provider instance already exists
    if (this.providers.has(providerId)) {
      return this.providers.get(providerId)!;
    }

    const configuration = this.configurations.get(providerId);
    if (!configuration) {
      throw new Error(`Configuration not found for provider: ${getProviderDisplayName(providerId)}`);
    }

    // Validate provider ID
    if (!isValidProviderId(providerId)) {
      throw new Error(`Invalid KYC provider ID: ${providerId}`);
    }

    let provider: KycProviderPort;

    switch (providerId) {
      case KYC_PROVIDERS.PREMBLY:
        provider = new PremblyKycProvider(configuration);
        break;
      case KYC_PROVIDERS.ZEEH:
        provider = new ZeehKycProvider(configuration);
        break;
      default:
        throw new Error(`Unknown KYC provider: ${getProviderDisplayName(providerId)}`);
    }

    // Cache the provider instance
    this.providers.set(providerId, provider);
    this.logger.log(`Created KYC provider instance: ${getProviderDisplayName(providerId)}`);

    return provider;
  }

  getAvailableProviders(): KycProviderId[] {
    return Array.from(this.configurations.keys());
  }

  isProviderConfigured(providerId: KycProviderId): boolean {
    const config = this.configurations.get(providerId);
    if (!config) {
      return false;
    }

    // Check if required credentials are present
    switch (providerId) {
      case KYC_PROVIDERS.PREMBLY:
        return !!(config.credentials.apiKey && config.credentials.appId);
      case KYC_PROVIDERS.ZEEH:
        return !!(config.credentials.apiKey && config.credentials.secretKey);
      default:
        return false;
    }
  }

  getProviderConfiguration(providerId: KycProviderId): ProviderConfiguration {
    const config = this.configurations.get(providerId);
    if (!config) {
      throw new Error(`Configuration not found for provider: ${providerId}`);
    }
    return config;
  }

  updateProviderConfiguration(providerId: KycProviderId, updates: Partial<ProviderConfiguration>): void {
    const existingConfig = this.getProviderConfiguration(providerId);
    const updatedConfig = {
      ...existingConfig,
      ...updates,
      credentials: {
        ...existingConfig.credentials,
        ...updates.credentials,
      },
    };

    this.configurations.set(providerId, updatedConfig);

    // Remove cached provider instance to force recreation with new config
    if (this.providers.has(providerId)) {
      this.providers.delete(providerId);
      this.logger.log(`Removed cached provider instance due to configuration update: ${getProviderDisplayName(providerId)}`);
    }
  }

  validateConfiguration(providerId: KycProviderId): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    try {
      const config = this.getProviderConfiguration(providerId);

      if (!config.credentials.apiKey) {
        errors.push('API key is required');
      }

      switch (providerId) {
        case KYC_PROVIDERS.PREMBLY:
          if (!config.credentials.appId) {
            errors.push('App ID is required for Prembly');
          }
          break;
        case KYC_PROVIDERS.ZEEH:
          if (!config.credentials.secretKey) {
            errors.push('Secret key is required for Zeeh');
          }
          break;
      }

      if (config.timeout && config.timeout < 1000) {
        errors.push('Timeout must be at least 1000ms');
      }

      if (config.retryAttempts && (config.retryAttempts < 0 || config.retryAttempts > 5)) {
        errors.push('Retry attempts must be between 0 and 5');
      }

    } catch (error) {
      errors.push(`Configuration not found for provider: ${providerId}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  async testProviderConnection(providerId: KycProviderId): Promise<{ success: boolean; error?: string; responseTime?: number }> {
    try {
      const provider = this.create(providerId);
      const startTime = Date.now();
      const healthStatus = await provider.checkHealth();
      const responseTime = Date.now() - startTime;

      return {
        success: healthStatus.isHealthy,
        error: healthStatus.errorMessage,
        responseTime,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  clearProviderCache(providerId?: KycProviderId): void {
    if (providerId) {
      this.providers.delete(providerId);
      this.logger.log(`Cleared cache for provider: ${getProviderDisplayName(providerId)}`);
    } else {
      this.providers.clear();
      this.logger.log('Cleared all provider cache');
    }
  }
}
