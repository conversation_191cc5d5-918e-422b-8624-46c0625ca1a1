import { Injectable, Logger } from '@nestjs/common';
import { KycProviderPort, VerificationRequest, VerificationResult, ProviderHealthStatus } from '../../core/ports/kyc/kyc-provider.port';
import { DocumentType } from '../../core/entities/kyc-verification.entity';
import { KycProviderFactory } from './kyc-provider-factory';
import { CountryKycConfigService, ProviderConfig } from './country-kyc-config.service';
import { KycProviderId, getProviderDisplayName } from '../../core/constants/kyc-providers.constants';

export interface ProviderAttempt {
  providerId: KycProviderId;
  success: boolean;
  result?: VerificationResult;
  error?: string;
  responseTime: number;
  attemptedAt: Date;
}

export interface VerificationAttemptLog {
  requestId: string;
  countryCode: string;
  documentType: DocumentType;
  attempts: ProviderAttempt[];
  finalResult?: VerificationResult;
  totalResponseTime: number;
  completedAt: Date;
}

@Injectable()
export class KycProviderManager {
  private readonly logger = new Logger(KycProviderManager.name);
  private readonly healthCache: Map<KycProviderId, { status: ProviderHealthStatus; cachedAt: Date }> = new Map();
  private readonly healthCacheTimeout = 5 * 60 * 1000; // 5 minutes

  constructor(
    private readonly providerFactory: KycProviderFactory,
    private readonly countryConfigService: CountryKycConfigService,
  ) {}

  async verifyDocument(
    countryCode: string,
    documentType: DocumentType,
    request: VerificationRequest,
  ): Promise<{ result: VerificationResult; log: VerificationAttemptLog }> {
    const requestId = this.generateRequestId();
    const startTime = Date.now();
    const attempts: ProviderAttempt[] = [];

    this.logger.log(`Starting verification for ${documentType} in country ${countryCode}`, { requestId });

    try {
      const providerConfig = this.countryConfigService.getProviderConfig(countryCode, documentType);
      const providersToTry = [providerConfig.primary, ...providerConfig.fallback];

      let finalResult: VerificationResult | undefined;

      for (const providerId of providersToTry) {
        const attemptStartTime = Date.now();
        
        try {
          this.logger.log(`Attempting verification with provider: ${getProviderDisplayName(providerId)}`, { requestId });

          // Check provider health first
          const isHealthy = await this.isProviderHealthy(providerId);
          if (!isHealthy) {
            const error = `Provider ${getProviderDisplayName(providerId)} is not healthy, skipping`;
            this.logger.warn(error, { requestId });
            attempts.push({
              providerId,
              success: false,
              error,
              responseTime: Date.now() - attemptStartTime,
              attemptedAt: new Date(),
            });
            continue;
          }

          // Get provider instance and perform verification
          const provider = this.providerFactory.create(providerId);
          const result = await this.performVerification(provider, request, providerConfig.config);

          const responseTime = Date.now() - attemptStartTime;
          
          attempts.push({
            providerId,
            success: result.success,
            result,
            responseTime,
            attemptedAt: new Date(),
          });

          if (result.success && result.verified) {
            this.logger.log(`Verification successful with provider: ${getProviderDisplayName(providerId)}`, { requestId });
            finalResult = result;
            break;
          } else if (result.success && !result.verified) {
            this.logger.log(`Verification completed but document not verified with provider: ${getProviderDisplayName(providerId)}`, { requestId });
            finalResult = result;
            break; // Don't try other providers if verification was successful but document is invalid
          } else {
            this.logger.warn(`Verification failed with provider: ${getProviderDisplayName(providerId)}`, {
              requestId,
              error: result.failureReason
            });
          }

        } catch (error) {
          const responseTime = Date.now() - attemptStartTime;
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          
          this.logger.error(`Provider ${getProviderDisplayName(providerId)} threw an error`, {
            requestId,
            error: errorMessage
          });

          attempts.push({
            providerId,
            success: false,
            error: errorMessage,
            responseTime,
            attemptedAt: new Date(),
          });

          // Mark provider as unhealthy
          await this.markProviderUnhealthy(providerId, errorMessage);
        }
      }

      if (!finalResult) {
        finalResult = {
          success: false,
          verified: false,
          providerReference: requestId,
          providerResponse: {},
          failureReason: 'All providers failed or are unavailable',
        };
      }

      const totalResponseTime = Date.now() - startTime;
      const log: VerificationAttemptLog = {
        requestId,
        countryCode,
        documentType,
        attempts,
        finalResult,
        totalResponseTime,
        completedAt: new Date(),
      };

      this.logger.log(`Verification completed`, { 
        requestId, 
        success: finalResult.success,
        verified: finalResult.verified,
        totalResponseTime,
        attemptsCount: attempts.length 
      });

      return { result: finalResult, log };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Verification process failed`, { requestId, error: errorMessage });

      const failureResult: VerificationResult = {
        success: false,
        verified: false,
        providerReference: requestId,
        providerResponse: {},
        failureReason: errorMessage,
      };

      const log: VerificationAttemptLog = {
        requestId,
        countryCode,
        documentType,
        attempts,
        finalResult: failureResult,
        totalResponseTime: Date.now() - startTime,
        completedAt: new Date(),
      };

      return { result: failureResult, log };
    }
  }

  private async performVerification(
    provider: KycProviderPort,
    request: VerificationRequest,
    config: any,
  ): Promise<VerificationResult> {
    // Apply provider-specific configuration
    const timeout = config.timeout || 30000;
    
    return Promise.race([
      provider.verify(request),
      new Promise<VerificationResult>((_, reject) =>
        setTimeout(() => reject(new Error('Provider timeout')), timeout)
      ),
    ]);
  }

  private async isProviderHealthy(providerId: KycProviderId): Promise<boolean> {
    const cached = this.healthCache.get(providerId);
    const now = new Date();

    // Use cached health status if it's recent
    if (cached && (now.getTime() - cached.cachedAt.getTime()) < this.healthCacheTimeout) {
      return cached.status.isHealthy;
    }

    try {
      const provider = this.providerFactory.create(providerId);
      const healthStatus = await provider.checkHealth();
      
      this.healthCache.set(providerId, {
        status: healthStatus,
        cachedAt: now,
      });

      return healthStatus.isHealthy;
    } catch (error) {
      this.logger.error(`Health check failed for provider ${getProviderDisplayName(providerId)}`, {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      
      // Cache the unhealthy status
      this.healthCache.set(providerId, {
        status: {
          isHealthy: false,
          lastChecked: now,
          errorMessage: error instanceof Error ? error.message : 'Unknown error',
        },
        cachedAt: now,
      });

      return false;
    }
  }

  private async markProviderUnhealthy(providerId: KycProviderId, error: string): Promise<void> {
    this.healthCache.set(providerId, {
      status: {
        isHealthy: false,
        lastChecked: new Date(),
        errorMessage: error,
      },
      cachedAt: new Date(),
    });
  }

  private generateRequestId(): string {
    return `kyc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  async getProviderHealth(providerId: KycProviderId): Promise<ProviderHealthStatus> {
    return this.isProviderHealthy(providerId).then(isHealthy => {
      const cached = this.healthCache.get(providerId);
      return cached?.status || {
        isHealthy,
        lastChecked: new Date(),
      };
    });
  }

  async getAllProvidersHealth(): Promise<Record<KycProviderId, ProviderHealthStatus>> {
    const providers = this.providerFactory.getAvailableProviders();
    const healthStatuses: Record<KycProviderId, ProviderHealthStatus> = {} as Record<KycProviderId, ProviderHealthStatus>;

    await Promise.all(
      providers.map(async (providerId) => {
        healthStatuses[providerId] = await this.getProviderHealth(providerId);
      })
    );

    return healthStatuses;
  }

  clearHealthCache(providerId?: KycProviderId): void {
    if (providerId) {
      this.healthCache.delete(providerId);
    } else {
      this.healthCache.clear();
    }
  }
}
