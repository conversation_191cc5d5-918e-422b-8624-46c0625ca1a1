import { ZeehKycProvider } from '../zeeh-kyc.provider';
import { DocumentType } from '../../../../core/entities/kyc-verification.entity';
import {
  BvnVerificationRequest,
  NinVerificationRequest,
  PassportVerificationRequest,
  VotersCardVerificationRequest,
  CacVerificationRequest,
  TinVerificationRequest,
  DriversLicenseVerificationRequest,
  NationalIdVerificationRequest
} from '../../../../core/ports/kyc/kyc-provider.port';
import { ProviderConfiguration } from '../../kyc-provider-factory';

// Mock axios
jest.mock('axios');
const mockedAxios = jest.mocked(require('axios'));

describe('ZeehKycProvider', () => {
  let provider: ZeehKycProvider;
  let mockConfig: ProviderConfiguration;

  beforeEach(() => {
    mockConfig = {
      credentials: {
        apiKey: 'test-api-key',
        secretKey: 'test-secret-key',
        baseUrl: 'https://api.usezeeh.com/v1',
      },
      timeout: 30000,
      retryAttempts: 2,
      enableLogging: false,
    };

    // Mock axios.create
    const mockAxiosInstance = {
      post: jest.fn(),
      get: jest.fn(),
      interceptors: {
        request: { use: jest.fn() },
        response: { use: jest.fn() },
      },
    };
    mockedAxios.create.mockReturnValue(mockAxiosInstance);

    provider = new ZeehKycProvider(mockConfig);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Provider Configuration', () => {
    it('should have correct provider ID', () => {
      expect(provider.providerId).toBe('zeeh');
    });

    it('should support correct document types', () => {
      expect(provider.supportedDocuments).toContain(DocumentType.BVN);
      expect(provider.supportedDocuments).toContain(DocumentType.NIN);
      expect(provider.supportedDocuments).toContain(DocumentType.NATIONAL_ID);
      expect(provider.supportedDocuments).toContain(DocumentType.DRIVERS_LICENSE);
      expect(provider.supportedDocuments).toContain(DocumentType.INTERNATIONAL_PASSPORT);
      expect(provider.supportedDocuments).toContain(DocumentType.VOTERS_CARD);
      expect(provider.supportedDocuments).toContain(DocumentType.CAC);
      expect(provider.supportedDocuments).toContain(DocumentType.TIN);
      expect(provider.supportedDocuments).toContain(DocumentType.FACIAL_IMAGE);
    });

    it('should support multiple countries', () => {
      expect(provider.supportedCountries).toContain('234'); // Nigeria
      expect(provider.supportedCountries).toContain('233'); // Ghana
      expect(provider.supportedCountries).toContain('254'); // Kenya
    });

    it('should validate document support by country', () => {
      // Nigeria supports BVN
      expect(provider.isDocumentSupportedInCountry('234', DocumentType.BVN)).toBe(true);
      // Ghana does not support BVN
      expect(provider.isDocumentSupportedInCountry('233', DocumentType.BVN)).toBe(false);
      // Kenya supports National ID
      expect(provider.isDocumentSupportedInCountry('254', DocumentType.NATIONAL_ID)).toBe(true);
      // Nigeria does not support National ID
      expect(provider.isDocumentSupportedInCountry('234', DocumentType.NATIONAL_ID)).toBe(false);
    });
  });

  describe('BVN Verification', () => {
    it('should make correct API call for BVN verification', async () => {
      const mockResponse = {
        data: {
          success: true,
          message: 'BVN verification successful',
          data: {
            bvn: '**********1',
            first_name: 'John',
            last_name: 'Doe',
            date_of_birth: '1990-01-01',
            phone_number: '08123456789',
            gender: 'Male',
          },
        },
      };

      const mockAxiosInstance = mockedAxios.create();
      mockAxiosInstance.post.mockResolvedValue(mockResponse);

      const request: BvnVerificationRequest = {
        documentType: DocumentType.BVN,
        documentNumber: '**********1',
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: '1990-01-01',
        phoneNumber: '08123456789',
      };

      const result = await provider.verifyBvn(request);

      expect(mockAxiosInstance.post).toHaveBeenCalledWith('/nigeria_kyc/lookup_bvn', {
        bvn: '**********1',
        first_name: 'John',
        last_name: 'Doe',
        date_of_birth: '1990-01-01',
        phone_number: '08123456789',
      });

      expect(result.success).toBe(true);
      expect(result.verified).toBe(true);
      expect(result.extractedData).toEqual({
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: '1990-01-01',
        phoneNumber: '08123456789',
        gender: 'Male',
        bvn: '**********1',
      });
    });

    it('should handle BVN verification failure', async () => {
      const mockResponse = {
        data: {
          success: false,
          message: 'BVN not found',
          error: {
            code: 'BVN_NOT_FOUND',
            message: 'The provided BVN was not found',
          },
        },
      };

      const mockAxiosInstance = mockedAxios.create();
      mockAxiosInstance.post.mockResolvedValue(mockResponse);

      const request: BvnVerificationRequest = {
        documentType: DocumentType.BVN,
        documentNumber: '**********1',
        firstName: 'John',
        lastName: 'Doe',
      };

      const result = await provider.verifyBvn(request);

      expect(result.success).toBe(false);
      expect(result.verified).toBe(false);
      expect(result.failureReason).toBe('The provided BVN was not found');
    });
  });

  describe('NIN Verification', () => {
    it('should make correct API call for NIN verification', async () => {
      const mockResponse = {
        data: {
          success: true,
          message: 'NIN verification successful',
          data: {
            nin: '**********1',
            first_name: 'Jane',
            last_name: 'Smith',
            middle_name: 'Mary',
            date_of_birth: '1985-05-15',
            gender: 'Female',
          },
        },
      };

      const mockAxiosInstance = mockedAxios.create();
      mockAxiosInstance.post.mockResolvedValue(mockResponse);

      const request: NinVerificationRequest = {
        documentType: DocumentType.NIN,
        documentNumber: '**********1',
        firstName: 'Jane',
        lastName: 'Smith',
        dateOfBirth: '1985-05-15',
      };

      const result = await provider.verifyNin(request);

      expect(mockAxiosInstance.post).toHaveBeenCalledWith('/nigeria_kyc/lookup_nin', {
        nin: '**********1',
        first_name: 'Jane',
        last_name: 'Smith',
        date_of_birth: '1985-05-15',
      });

      expect(result.success).toBe(true);
      expect(result.verified).toBe(true);
      expect(result.extractedData).toEqual({
        firstName: 'Jane',
        lastName: 'Smith',
        middleName: 'Mary',
        dateOfBirth: '1985-05-15',
        gender: 'Female',
        nin: '**********1',
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle network errors', async () => {
      const mockAxiosInstance = mockedAxios.create();
      mockAxiosInstance.post.mockRejectedValue(new Error('Network error'));

      const request: BvnVerificationRequest = {
        documentType: DocumentType.BVN,
        documentNumber: '**********1',
      };

      const result = await provider.verifyBvn(request);

      expect(result.success).toBe(false);
      expect(result.verified).toBe(false);
      expect(result.failureReason).toBe('Network error');
    });

    it('should handle API errors with response', async () => {
      const mockAxiosInstance = mockedAxios.create();
      const apiError = {
        response: {
          status: 400,
          data: {
            success: false,
            message: 'Invalid BVN format',
            error: {
              code: 'INVALID_FORMAT',
              message: 'BVN must be 11 digits',
            },
          },
        },
      };
      mockAxiosInstance.post.mockRejectedValue(apiError);

      const request: BvnVerificationRequest = {
        documentType: DocumentType.BVN,
        documentNumber: '123',
      };

      const result = await provider.verifyBvn(request);

      expect(result.success).toBe(false);
      expect(result.verified).toBe(false);
      expect(result.failureReason).toBe('BVN must be 11 digits');
    });
  });

  describe('Health Check', () => {
    it('should return healthy status on successful connection', async () => {
      const mockAxiosInstance = mockedAxios.create();
      mockAxiosInstance.get.mockResolvedValue({ status: 200 });

      const health = await provider.checkHealth();

      expect(health.isHealthy).toBe(true);
      expect(health.lastChecked).toBeInstanceOf(Date);
      expect(health.responseTime).toBeGreaterThan(0);
    });

    it('should return unhealthy status on connection failure', async () => {
      const mockAxiosInstance = mockedAxios.create();
      mockAxiosInstance.get.mockRejectedValue(new Error('Connection failed'));

      const health = await provider.checkHealth();

      expect(health.isHealthy).toBe(false);
      expect(health.errorMessage).toBe('Connection failed');
    });
  });

  describe('International Passport Verification', () => {
    it('should make correct API call for passport verification', async () => {
      const mockResponse = {
        data: {
          success: true,
          message: 'Passport verification successful',
          data: {
            passport_number: 'A12345678',
            first_name: 'Alice',
            last_name: 'Johnson',
            date_of_birth: '1988-03-20',
            gender: 'Female',
          },
        },
      };

      const mockAxiosInstance = mockedAxios.create();
      mockAxiosInstance.post.mockResolvedValue(mockResponse);

      const request: PassportVerificationRequest = {
        documentType: DocumentType.INTERNATIONAL_PASSPORT,
        documentNumber: 'A12345678',
        firstName: 'Alice',
        lastName: 'Johnson',
        dateOfBirth: '1988-03-20',
      };

      const result = await provider.verifyInternationalPassport(request);

      expect(mockAxiosInstance.post).toHaveBeenCalledWith('/nigeria_kyc/lookup_passport', {
        passportNumber: 'A12345678',
        dob: '1988-03-20',
        firstName: 'Alice',
        lastName: 'Johnson',
      });

      expect(result.success).toBe(true);
      expect(result.verified).toBe(true);
      expect(result.extractedData).toEqual({
        firstName: 'Alice',
        lastName: 'Johnson',
        dateOfBirth: '1988-03-20',
        gender: 'Female',
        passportNumber: 'A12345678',
      });
    });
  });

  describe('Voters Card Verification', () => {
    it('should make correct API call for voters card verification', async () => {
      const mockResponse = {
        data: {
          success: true,
          message: 'Voters card verification successful',
          data: {
            vin: '**********123456789',
            first_name: 'Bob',
            last_name: 'Wilson',
            gender: 'Male',
          },
        },
      };

      const mockAxiosInstance = mockedAxios.create();
      mockAxiosInstance.post.mockResolvedValue(mockResponse);

      const request: VotersCardVerificationRequest = {
        documentType: DocumentType.VOTERS_CARD,
        documentNumber: '**********123456789',
        firstName: 'Bob',
        lastName: 'Wilson',
      };

      const result = await provider.verifyVotersCard(request);

      expect(mockAxiosInstance.post).toHaveBeenCalledWith('/nigeria_kyc/lookup_voters_id', {
        vin: '**********123456789',
      });

      expect(result.success).toBe(true);
      expect(result.verified).toBe(true);
      expect(result.extractedData).toEqual({
        firstName: 'Bob',
        lastName: 'Wilson',
        gender: 'Male',
        vin: '**********123456789',
      });
    });
  });

  describe('CAC Verification', () => {
    it('should make correct API call for CAC verification', async () => {
      const mockResponse = {
        data: {
          success: true,
          message: 'CAC verification successful',
          data: {
            rc_number: 'RC123456',
            company_name: 'Test Company Ltd',
            company_type: 'Private Limited Company',
            registration_date: '2020-01-15',
            company_status: 'Active',
          },
        },
      };

      const mockAxiosInstance = mockedAxios.create();
      mockAxiosInstance.post.mockResolvedValue(mockResponse);

      const request: CacVerificationRequest = {
        documentType: DocumentType.CAC,
        documentNumber: 'RC123456',
        companyName: 'Test Company Ltd',
      };

      const result = await provider.verifyCac(request);

      expect(mockAxiosInstance.post).toHaveBeenCalledWith('/nigeria_kyc/lookup_cac', {
        rc_number: 'RC123456',
        company_name: 'Test Company Ltd',
      });

      expect(result.success).toBe(true);
      expect(result.verified).toBe(true);
      expect(result.extractedData).toEqual({
        rcNumber: 'RC123456',
        companyName: 'Test Company Ltd',
        companyType: 'Private Limited Company',
        registrationDate: '2020-01-15',
        companyStatus: 'Active',
      });
    });
  });

  describe('TIN Verification', () => {
    it('should make correct API call for TIN verification', async () => {
      const mockResponse = {
        data: {
          success: true,
          message: 'TIN verification successful',
          data: {
            tin: '12345678-0001',
            taxpayer_name: 'John Doe Enterprises',
            taxpayer_type: 'Individual',
          },
        },
      };

      const mockAxiosInstance = mockedAxios.create();
      mockAxiosInstance.post.mockResolvedValue(mockResponse);

      const request: TinVerificationRequest = {
        documentType: DocumentType.TIN,
        documentNumber: '12345678-0001',
      };

      const result = await provider.verifyTin(request);

      expect(mockAxiosInstance.post).toHaveBeenCalledWith('/nigeria_kyc/lookup_tin', {
        tin: '12345678-0001',
      });

      expect(result.success).toBe(true);
      expect(result.verified).toBe(true);
      expect(result.extractedData).toEqual({
        tin: '12345678-0001',
        taxpayerName: 'John Doe Enterprises',
        taxpayerType: 'Individual',
      });
    });
  });

  describe('Drivers License Verification', () => {
    it('should make correct API call for drivers license verification', async () => {
      const mockResponse = {
        data: {
          success: true,
          message: 'Drivers license verification successful',
          data: {
            license_number: 'ABC123456789',
            first_name: 'Charlie',
            last_name: 'Brown',
            date_of_birth: '1992-07-10',
            issue_date: '2020-01-01',
            expiry_date: '2025-01-01',
            state_of_issue: 'Lagos',
          },
        },
      };

      const mockAxiosInstance = mockedAxios.create();
      mockAxiosInstance.post.mockResolvedValue(mockResponse);

      const request: DriversLicenseVerificationRequest = {
        documentType: DocumentType.DRIVERS_LICENSE,
        documentNumber: 'ABC123456789',
        firstName: 'Charlie',
        lastName: 'Brown',
        dateOfBirth: '1992-07-10',
      };

      const result = await provider.verifyDriversLicense(request);

      expect(mockAxiosInstance.post).toHaveBeenCalledWith('/nigeria_kyc/drivers_license', {
        license_number: 'ABC123456789',
        first_name: 'Charlie',
        last_name: 'Brown',
        date_of_birth: '1992-07-10',
        document_image: undefined,
      });

      expect(result.success).toBe(true);
      expect(result.verified).toBe(true);
      expect(result.extractedData).toEqual({
        firstName: 'Charlie',
        lastName: 'Brown',
        dateOfBirth: '1992-07-10',
        licenseNumber: 'ABC123456789',
        issueDate: '2020-01-01',
        expiryDate: '2025-01-01',
        stateOfIssue: 'Lagos',
      });
    });
  });

  describe('Multi-Country Support', () => {
    describe('Ghana Verification', () => {
      it('should verify Ghana TIN', async () => {
        const mockResponse = {
          data: {
            success: true,
            message: 'Ghana TIN verification successful',
            data: {
              tin_number: 'GHA123456789A',
              taxpayer_name: 'Ghana Test Company',
              taxpayer_type: 'Corporate',
            },
          },
        };

        const mockAxiosInstance = mockedAxios.create();
        mockAxiosInstance.post.mockResolvedValue(mockResponse);

        const request: TinVerificationRequest = {
          documentType: DocumentType.TIN,
          documentNumber: 'GHA123456789A',
        };

        const result = await provider.verify(request, '233'); // Ghana

        expect(mockAxiosInstance.post).toHaveBeenCalledWith('/ghana_kyc/tin', {
          tin_number: 'GHA123456789A',
        });

        expect(result.success).toBe(true);
        expect(result.verified).toBe(true);
        expect(result.extractedData).toEqual({
          tinNumber: 'GHA123456789A',
          taxpayerName: 'Ghana Test Company',
          taxpayerType: 'Corporate',
        });
      });

      it('should verify Ghana Voters Card', async () => {
        const mockResponse = {
          data: {
            success: true,
            message: 'Ghana voters card verification successful',
            data: {
              voter_id: '**********',
              first_name: 'Kwame',
              last_name: 'Asante',
              constituency: 'Accra Central',
              polling_station: 'Station 001',
            },
          },
        };

        const mockAxiosInstance = mockedAxios.create();
        mockAxiosInstance.post.mockResolvedValue(mockResponse);

        const request: VotersCardVerificationRequest = {
          documentType: DocumentType.VOTERS_CARD,
          documentNumber: '**********',
          firstName: 'Kwame',
          lastName: 'Asante',
        };

        const result = await provider.verify(request, '233'); // Ghana

        expect(mockAxiosInstance.post).toHaveBeenCalledWith('/ghana_kyc/voters_id', {
          voter_id: '**********',
          first_name: 'Kwame',
          last_name: 'Asante',
        });

        expect(result.success).toBe(true);
        expect(result.verified).toBe(true);
        expect(result.extractedData).toEqual({
          firstName: 'Kwame',
          lastName: 'Asante',
          voterId: '**********',
          constituency: 'Accra Central',
          pollingStation: 'Station 001',
        });
      });
    });

    describe('Kenya Verification', () => {
      it('should verify Kenya National ID', async () => {
        const mockResponse = {
          data: {
            success: true,
            message: 'Kenya National ID verification successful',
            data: {
              national_id: '12345678',
              first_name: 'John',
              last_name: 'Mwangi',
              date_of_birth: '1985-03-15',
              place_of_birth: 'Nairobi',
              place_of_issue: 'Nairobi',
            },
          },
        };

        const mockAxiosInstance = mockedAxios.create();
        mockAxiosInstance.post.mockResolvedValue(mockResponse);

        const request: NationalIdVerificationRequest = {
          documentType: DocumentType.NATIONAL_ID,
          documentNumber: '12345678',
          firstName: 'John',
          lastName: 'Mwangi',
          dateOfBirth: '1985-03-15',
        };

        const result = await provider.verify(request, '254'); // Kenya

        expect(mockAxiosInstance.post).toHaveBeenCalledWith('/kenya_kyc/national_id', {
          national_id: '12345678',
          first_name: 'John',
          last_name: 'Mwangi',
          date_of_birth: '1985-03-15',
        });

        expect(result.success).toBe(true);
        expect(result.verified).toBe(true);
        expect(result.extractedData).toEqual({
          firstName: 'John',
          lastName: 'Mwangi',
          dateOfBirth: '1985-03-15',
          nationalId: '12345678',
          placeOfBirth: 'Nairobi',
          placeOfIssue: 'Nairobi',
        });
      });

      it('should verify Kenya KRA PIN (TIN)', async () => {
        const mockResponse = {
          data: {
            success: true,
            message: 'Kenya KRA PIN verification successful',
            data: {
              kra_pin: 'A123456789B',
              taxpayer_name: 'John Mwangi',
              taxpayer_type: 'Individual',
            },
          },
        };

        const mockAxiosInstance = mockedAxios.create();
        mockAxiosInstance.post.mockResolvedValue(mockResponse);

        const request: TinVerificationRequest = {
          documentType: DocumentType.TIN,
          documentNumber: 'A123456789B',
        };

        const result = await provider.verify(request, '254'); // Kenya

        expect(mockAxiosInstance.post).toHaveBeenCalledWith('/kenya_kyc/kra_pin', {
          kra_pin: 'A123456789B',
        });

        expect(result.success).toBe(true);
        expect(result.verified).toBe(true);
        expect(result.extractedData).toEqual({
          kraPin: 'A123456789B',
          taxpayerName: 'John Mwangi',
          taxpayerType: 'Individual',
        });
      });
    });

    describe('Country Validation', () => {
      it('should reject unsupported country', async () => {
        const request: BvnVerificationRequest = {
          documentType: DocumentType.BVN,
          documentNumber: '**********1',
        };

        await expect(provider.verify(request, '999')).rejects.toThrow(
          'Country 999 not supported by Zeeh'
        );
      });

      it('should reject unsupported document type for country', async () => {
        const request: BvnVerificationRequest = {
          documentType: DocumentType.BVN,
          documentNumber: '**********1',
        };

        await expect(provider.verify(request, '233')).rejects.toThrow(
          'Document type BVN not supported in country 233'
        );
      });
    });

    describe('Backward Compatibility', () => {
      it('should maintain backward compatibility for Nigeria-specific methods', async () => {
        const mockResponse = {
          data: {
            success: true,
            message: 'BVN verification successful',
            data: {
              bvn: '**********1',
              first_name: 'John',
              last_name: 'Doe',
            },
          },
        };

        const mockAxiosInstance = mockedAxios.create();
        mockAxiosInstance.post.mockResolvedValue(mockResponse);

        const request: BvnVerificationRequest = {
          documentType: DocumentType.BVN,
          documentNumber: '**********1',
          firstName: 'John',
          lastName: 'Doe',
        };

        // Test legacy method (should default to Nigeria)
        const result = await provider.verifyBvn(request);

        expect(mockAxiosInstance.post).toHaveBeenCalledWith('/nigeria_kyc/lookup_bvn', {
          bvn: '**********1',
          first_name: 'John',
          last_name: 'Doe',
          date_of_birth: undefined,
          phone_number: undefined,
        });

        expect(result.success).toBe(true);
        expect(result.verified).toBe(true);
      });
    });
  });
});
