import { Logger } from '@nestjs/common';
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import {
  KycProviderPort,
  VerificationRequest,
  VerificationResult,
  BvnVerificationRequest,
  NinVerificationRequest,
  DriversLicenseVerificationRequest,
  PassportVerificationRequest,
  VotersCardVerificationRequest,
  CacVerificationRequest,
  TinVerificationRequest,
  ProviderHealthStatus,
} from '../../../core/ports/kyc/kyc-provider.port';
import { DocumentType } from '../../../core/entities/kyc-verification.entity';
import { ProviderConfiguration } from '../kyc-provider-factory';
import { KYC_PROVIDERS } from '../../../core/constants/kyc-providers.constants';

interface PremblyApiResponse {
  status: boolean;
  detail: string;
  response_code: string;
  verification: {
    status: boolean;
    reference: string;
    [key: string]: any;
  };
}

export class PremblyKycProvider implements KycProviderPort {
  readonly providerId = KYC_PROVIDERS.PREMBLY;
  readonly supportedDocuments = [
    DocumentType.BVN,
    DocumentType.NIN,
    DocumentType.DRIVERS_LICENSE,
    DocumentType.INTERNATIONAL_PASSPORT,
    DocumentType.VOTERS_CARD,
    DocumentType.CAC,
    DocumentType.TIN,
  ];
  readonly supportedCountries = ['234']; // Nigeria

  private readonly logger = new Logger(PremblyKycProvider.name);
  private readonly httpClient: AxiosInstance;

  constructor(private readonly config: ProviderConfiguration) {
    this.httpClient = axios.create({
      baseURL: config.credentials.baseUrl,
      timeout: config.timeout || 30000,
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': config.credentials.apiKey,
        'app-id': config.credentials.appId,
      },
    });

    // Add request/response interceptors for logging
    if (config.enableLogging) {
      this.setupLogging();
    }
  }

  private setupLogging(): void {
    this.httpClient.interceptors.request.use(
      (config) => {
        this.logger.debug(`Prembly API Request: ${config.method?.toUpperCase()} ${config.url}`, {
          headers: { ...config.headers, 'x-api-key': '[REDACTED]' },
          data: config.data,
        });
        return config;
      },
      (error) => {
        this.logger.error('Prembly API Request Error', error);
        return Promise.reject(error);
      }
    );

    this.httpClient.interceptors.response.use(
      (response) => {
        this.logger.debug(`Prembly API Response: ${response.status}`, {
          data: response.data,
        });
        return response;
      },
      (error) => {
        this.logger.error('Prembly API Response Error', {
          status: error.response?.status,
          data: error.response?.data,
          message: error.message,
        });
        return Promise.reject(error);
      }
    );
  }

  async checkHealth(): Promise<ProviderHealthStatus> {
    try {
      const startTime = Date.now();
      // Prembly doesn't have a dedicated health endpoint, so we'll use a lightweight verification
      // This is just a connectivity test
      const response = await this.httpClient.get('/', { timeout: 5000 });
      const responseTime = Date.now() - startTime;

      return {
        isHealthy: true,
        responseTime,
        lastChecked: new Date(),
      };
    } catch (error) {
      return {
        isHealthy: false,
        lastChecked: new Date(),
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async verifyBvn(request: BvnVerificationRequest): Promise<VerificationResult> {
    try {
      const payload = {
        number: request.documentNumber,
        first_name: request.firstName,
        last_name: request.lastName,
        date_of_birth: request.dateOfBirth,
        phone: request.phoneNumber,
      };

      const response: AxiosResponse<PremblyApiResponse> = await this.httpClient.post('/bvn', payload);
      return this.parsePremblyResponse(response.data, request.documentType);
    } catch (error) {
      return this.handleError(error, request.documentType);
    }
  }

  async verifyNin(request: NinVerificationRequest): Promise<VerificationResult> {
    try {
      const payload = {
        number: request.documentNumber,
        first_name: request.firstName,
        last_name: request.lastName,
        date_of_birth: request.dateOfBirth,
      };

      const response: AxiosResponse<PremblyApiResponse> = await this.httpClient.post('/nin', payload);
      return this.parsePremblyResponse(response.data, request.documentType);
    } catch (error) {
      return this.handleError(error, request.documentType);
    }
  }

  async verifyDriversLicense(request: DriversLicenseVerificationRequest): Promise<VerificationResult> {
    try {
      const payload = {
        number: request.documentNumber,
        first_name: request.firstName,
        last_name: request.lastName,
        date_of_birth: request.dateOfBirth,
        image: request.documentImageUrl,
      };

      const response: AxiosResponse<PremblyApiResponse> = await this.httpClient.post('/drivers_license', payload);
      return this.parsePremblyResponse(response.data, request.documentType);
    } catch (error) {
      return this.handleError(error, request.documentType);
    }
  }

  async verifyInternationalPassport(request: PassportVerificationRequest): Promise<VerificationResult> {
    try {
      const payload = {
        number: request.documentNumber,
        first_name: request.firstName,
        last_name: request.lastName,
        date_of_birth: request.dateOfBirth,
        image: request.documentImageUrl,
      };

      const response: AxiosResponse<PremblyApiResponse> = await this.httpClient.post('/passport', payload);
      return this.parsePremblyResponse(response.data, request.documentType);
    } catch (error) {
      return this.handleError(error, request.documentType);
    }
  }

  async verifyVotersCard(request: VotersCardVerificationRequest): Promise<VerificationResult> {
    try {
      const payload = {
        number: request.documentNumber,
        first_name: request.firstName,
        last_name: request.lastName,
        image: request.documentImageUrl,
      };

      const response: AxiosResponse<PremblyApiResponse> = await this.httpClient.post('/voters_card', payload);
      return this.parsePremblyResponse(response.data, request.documentType);
    } catch (error) {
      return this.handleError(error, request.documentType);
    }
  }

  async verifyCac(request: CacVerificationRequest): Promise<VerificationResult> {
    try {
      const payload = {
        rc_number: request.documentNumber,
        company_name: request.companyName,
      };

      const response: AxiosResponse<PremblyApiResponse> = await this.httpClient.post('/cac', payload);
      return this.parsePremblyResponse(response.data, request.documentType);
    } catch (error) {
      return this.handleError(error, request.documentType);
    }
  }

  async verifyTin(request: TinVerificationRequest): Promise<VerificationResult> {
    try {
      const payload = {
        tin: request.documentNumber,
        company_name: request.companyName,
        first_name: request.firstName,
        last_name: request.lastName,
      };

      const response: AxiosResponse<PremblyApiResponse> = await this.httpClient.post('/tin', payload);
      return this.parsePremblyResponse(response.data, request.documentType);
    } catch (error) {
      return this.handleError(error, request.documentType);
    }
  }

  // Not supported by Prembly
  async verifyUtilityBill(): Promise<VerificationResult> {
    throw new Error('Utility bill verification not supported by Prembly');
  }

  async verifyBankStatement(): Promise<VerificationResult> {
    throw new Error('Bank statement verification not supported by Prembly');
  }

  async verifyFacial(): Promise<VerificationResult> {
    throw new Error('Facial verification not supported by Prembly');
  }

  async verify(request: VerificationRequest): Promise<VerificationResult> {
    switch (request.documentType) {
      case DocumentType.BVN:
        return this.verifyBvn(request as BvnVerificationRequest);
      case DocumentType.NIN:
        return this.verifyNin(request as NinVerificationRequest);
      case DocumentType.DRIVERS_LICENSE:
        return this.verifyDriversLicense(request as DriversLicenseVerificationRequest);
      case DocumentType.INTERNATIONAL_PASSPORT:
        return this.verifyInternationalPassport(request as PassportVerificationRequest);
      case DocumentType.VOTERS_CARD:
        return this.verifyVotersCard(request as VotersCardVerificationRequest);
      case DocumentType.CAC:
        return this.verifyCac(request as CacVerificationRequest);
      case DocumentType.TIN:
        return this.verifyTin(request as TinVerificationRequest);
      default:
        throw new Error(`Document type ${request.documentType} not supported by Prembly`);
    }
  }

  private parsePremblyResponse(response: PremblyApiResponse, documentType: DocumentType): VerificationResult {
    const isVerified = response.status && response.verification?.status;
    
    return {
      success: response.status,
      verified: isVerified,
      confidence: isVerified ? 0.95 : 0.0, // Prembly doesn't provide confidence scores
      providerReference: response.verification?.reference || '',
      providerResponse: response,
      extractedData: this.extractDataFromResponse(response, documentType),
      failureReason: !response.status ? response.detail : undefined,
      requiresManualReview: false,
    };
  }

  private extractDataFromResponse(response: PremblyApiResponse, documentType: DocumentType): Record<string, any> | undefined {
    if (!response.verification || !response.status) {
      return undefined;
    }

    const verification = response.verification;
    const extracted: Record<string, any> = {};

    // Common fields
    if (verification.first_name) extracted.firstName = verification.first_name;
    if (verification.last_name) extracted.lastName = verification.last_name;
    if (verification.date_of_birth) extracted.dateOfBirth = verification.date_of_birth;
    if (verification.phone) extracted.phoneNumber = verification.phone;
    if (verification.gender) extracted.gender = verification.gender;

    // Document-specific fields
    switch (documentType) {
      case DocumentType.DRIVERS_LICENSE:
        if (verification.license_number) extracted.licenseNumber = verification.license_number;
        if (verification.issue_date) extracted.issueDate = verification.issue_date;
        if (verification.expiry_date) extracted.expiryDate = verification.expiry_date;
        break;
      case DocumentType.CAC:
        if (verification.company_name) extracted.companyName = verification.company_name;
        if (verification.registration_date) extracted.registrationDate = verification.registration_date;
        if (verification.company_type) extracted.companyType = verification.company_type;
        break;
    }

    return Object.keys(extracted).length > 0 ? extracted : undefined;
  }

  private handleError(error: any, documentType: DocumentType): VerificationResult {
    const errorMessage = error.response?.data?.detail || error.message || 'Unknown error';
    
    return {
      success: false,
      verified: false,
      providerReference: '',
      providerResponse: error.response?.data || {},
      failureReason: errorMessage,
      requiresManualReview: error.response?.status === 500, // Server errors might need manual review
    };
  }

  verifyWebhookSignature(payload: string, signature: string): boolean {
    // Implement Prembly webhook signature verification if they support it
    return true; // Placeholder
  }

  parseWebhookPayload(payload: any): { providerReference: string; status: 'verified' | 'failed' | 'requires_review'; result: VerificationResult } {
    // Implement Prembly webhook payload parsing if they support it
    throw new Error('Webhook parsing not implemented for Prembly');
  }
}
