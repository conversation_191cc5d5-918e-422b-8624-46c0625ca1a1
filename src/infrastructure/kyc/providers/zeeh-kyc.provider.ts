import { Logger } from '@nestjs/common';
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import {
  KycProviderPort,
  VerificationRequest,
  VerificationResult,
  BvnVerificationRequest,
  NinVerificationRequest,
  DriversLicenseVerificationRequest,
  PassportVerificationRequest,
  VotersCardVerificationRequest,
  CacVerificationRequest,
  TinVerificationRequest,
  NationalIdVerificationRequest,
  FacialVerificationRequest,
  ProviderHealthStatus,
} from '../../../core/ports/kyc/kyc-provider.port';
import { DocumentType } from '../../../core/entities/kyc-verification.entity';
import { ProviderConfiguration } from '../kyc-provider-factory';
import { KYC_PROVIDERS } from '../../../core/constants/kyc-providers.constants';

interface ZeehApiResponse {
  success: boolean;
  message: string;
  data?: {
    verification_id?: string;
    status?: 'verified' | 'failed' | 'pending' | 'success';
    confidence_score?: number;
    details?: {
      [key: string]: any;
    };
    // BVN specific fields
    bvn?: string;
    first_name?: string;
    last_name?: string;
    middle_name?: string;
    date_of_birth?: string;
    phone_number?: string;
    email?: string;
    gender?: string;
    // NIN specific fields
    nin?: string;
    // Drivers License specific fields
    license_number?: string;
    issue_date?: string;
    expiry_date?: string;
    state_of_issue?: string;
    // CAC specific fields
    rc_number?: string;
    company_name?: string;
    company_type?: string;
    registration_date?: string;
    company_status?: string;
    // TIN specific fields
    tin?: string;
    taxpayer_name?: string;
    taxpayer_type?: string;
    // Passport specific fields
    passport_number?: string;
    // Voters Card specific fields
    vin?: string;
    // Common fields
    [key: string]: any;
  };
  error?: {
    code: string;
    message: string;
  };
}

export class ZeehKycProvider implements KycProviderPort {
  readonly providerId = KYC_PROVIDERS.ZEEH;
  readonly supportedCountries = ['234', '233', '254']; // Nigeria, Ghana, Kenya

  private readonly logger = new Logger(ZeehKycProvider.name);
  private readonly httpClient: AxiosInstance;

  // Dynamic supported documents based on all countries
  get supportedDocuments(): DocumentType[] {
    return Array.from(new Set([
      ...this.getCountrySupportedDocuments('234'),
      ...this.getCountrySupportedDocuments('233'),
      ...this.getCountrySupportedDocuments('254'),
    ]));
  }

  constructor(private readonly config: ProviderConfiguration) {
    this.httpClient = axios.create({
      baseURL: config.credentials.baseUrl || 'https://api.usezeeh.com/v1',
      timeout: config.timeout || 30000,
      headers: {
        'Content-Type': 'application/json',
        // 'Authorization': `Bearer ${config.credentials.apiKey}`,
        'Secret_Key': config.credentials.secretKey, // Zeeh uses x-api-key for secret
      },
    });

    // Add request/response interceptors for logging
    if (config.enableLogging) {
      this.setupLogging();
    }
  }

  private getCountrySupportedDocuments(countryCode: string): DocumentType[] {
    switch (countryCode) {
      case '234': // Nigeria
        return [
          DocumentType.BVN,
          DocumentType.NIN,
          DocumentType.DRIVERS_LICENSE,
          DocumentType.INTERNATIONAL_PASSPORT,
          DocumentType.VOTERS_CARD,
          DocumentType.CAC,
          DocumentType.TIN,
          DocumentType.FACIAL_IMAGE,
          DocumentType.SELFIE,
        ];

      case '233': // Ghana
        return [
          DocumentType.DRIVERS_LICENSE,
          DocumentType.INTERNATIONAL_PASSPORT,
          DocumentType.VOTERS_CARD,
          DocumentType.TIN,
          DocumentType.FACIAL_IMAGE,
          DocumentType.SELFIE,
        ];

      case '254': // Kenya
        return [
          DocumentType.NATIONAL_ID,
          DocumentType.DRIVERS_LICENSE,
          DocumentType.INTERNATIONAL_PASSPORT,
          DocumentType.TIN,
          DocumentType.FACIAL_IMAGE,
          DocumentType.SELFIE,
        ];

      default:
        return [];
    }
  }

  private getCountryEndpoints(countryCode: string): Record<DocumentType, string> {
    switch (countryCode) {
      case '234': // Nigeria
        return {
          [DocumentType.BVN]: '/nigeria_kyc/lookup_bvn',
          [DocumentType.NIN]: '/nigeria_kyc/lookup_nin',
          [DocumentType.DRIVERS_LICENSE]: '/nigeria_kyc/drivers_license',
          [DocumentType.INTERNATIONAL_PASSPORT]: '/nigeria_kyc/lookup_passport',
          [DocumentType.VOTERS_CARD]: '/nigeria_kyc/lookup_voters_id',
          [DocumentType.CAC]: '/nigeria_kyc/lookup_cac',
          [DocumentType.TIN]: '/nigeria_kyc/lookup_tin',
          [DocumentType.FACIAL_IMAGE]: '/nigeria_kyc/facial_verification',
          [DocumentType.SELFIE]: '/nigeria_kyc/selfie_verification',
        } as Record<DocumentType, string>;

      case '233': // Ghana
        return {
          [DocumentType.DRIVERS_LICENSE]: '/ghana_kyc/drivers_license',
          [DocumentType.INTERNATIONAL_PASSPORT]: '/ghana_kyc/passport',
          [DocumentType.VOTERS_CARD]: '/ghana_kyc/voters_id',
          [DocumentType.TIN]: '/ghana_kyc/tin',
          [DocumentType.FACIAL_IMAGE]: '/ghana_kyc/facial_verification',
          [DocumentType.SELFIE]: '/ghana_kyc/selfie_verification',
        } as Record<DocumentType, string>;

      case '254': // Kenya
        return {
          [DocumentType.NATIONAL_ID]: '/kenya_kyc/national_id',
          [DocumentType.DRIVERS_LICENSE]: '/kenya_kyc/drivers_license',
          [DocumentType.INTERNATIONAL_PASSPORT]: '/kenya_kyc/passport',
          [DocumentType.TIN]: '/kenya_kyc/kra_pin',
          [DocumentType.FACIAL_IMAGE]: '/kenya_kyc/facial_verification',
          [DocumentType.SELFIE]: '/kenya_kyc/selfie_verification',
        } as Record<DocumentType, string>;

      default:
        throw new Error(`Country ${countryCode} not supported by Zeeh`);
    }
  }

  isDocumentSupportedInCountry(countryCode: string, documentType: DocumentType): boolean {
    return this.getCountrySupportedDocuments(countryCode).includes(documentType);
  }

  private buildVerificationPayload(
    request: VerificationRequest,
    countryCode: string
  ): Record<string, any> {
    switch (countryCode) {
      case '234': // Nigeria
        return this.buildNigeriaPayload(request);

      case '233': // Ghana
        return this.buildGhanaPayload(request);

      case '254': // Kenya
        return this.buildKenyaPayload(request);

      default:
        throw new Error(`Country ${countryCode} not supported`);
    }
  }

  private buildNigeriaPayload(request: VerificationRequest): Record<string, any> {
    switch (request.documentType) {
      case DocumentType.BVN:
        const bvnReq = request as BvnVerificationRequest;
        return {
          bvn: bvnReq.documentNumber,
          first_name: bvnReq.firstName,
          last_name: bvnReq.lastName,
          date_of_birth: bvnReq.dateOfBirth,
          phone_number: bvnReq.phoneNumber,
        };

      case DocumentType.NIN:
        const ninReq = request as NinVerificationRequest;
        return {
          nin: ninReq.documentNumber,
          first_name: ninReq.firstName,
          last_name: ninReq.lastName,
          date_of_birth: ninReq.dateOfBirth,
        };

      case DocumentType.DRIVERS_LICENSE:
        const dlReq = request as DriversLicenseVerificationRequest;
        return {
          license_number: dlReq.documentNumber,
          first_name: dlReq.firstName,
          last_name: dlReq.lastName,
          date_of_birth: dlReq.dateOfBirth,
          document_image: dlReq.documentImageUrl,
        };

      case DocumentType.INTERNATIONAL_PASSPORT:
        const passportReq = request as PassportVerificationRequest;
        return {
          passportNumber: passportReq.documentNumber,
          dob: passportReq.dateOfBirth,
          firstName: passportReq.firstName,
          lastName: passportReq.lastName,
        };

      case DocumentType.VOTERS_CARD:
        const votersReq = request as VotersCardVerificationRequest;
        return {
          vin: votersReq.documentNumber,
        };

      case DocumentType.CAC:
        const cacReq = request as CacVerificationRequest;
        return {
          rc_number: cacReq.documentNumber,
          company_name: cacReq.companyName,
        };

      case DocumentType.TIN:
        const tinReq = request as TinVerificationRequest;
        return {
          tin: tinReq.documentNumber,
        };

      case DocumentType.FACIAL_IMAGE:
      case DocumentType.SELFIE:
        const facialReq = request as FacialVerificationRequest;
        return {
          image: facialReq.imageData,
          reference_image: facialReq.referenceImageData,
        };

      default:
        throw new Error(`Document type ${request.documentType} not supported for Nigeria`);
    }
  }

  private buildGhanaPayload(request: VerificationRequest): Record<string, any> {
    switch (request.documentType) {
      case DocumentType.DRIVERS_LICENSE:
        const dlReq = request as DriversLicenseVerificationRequest;
        return {
          license_number: dlReq.documentNumber,
          first_name: dlReq.firstName,
          last_name: dlReq.lastName,
          date_of_birth: dlReq.dateOfBirth,
        };

      case DocumentType.INTERNATIONAL_PASSPORT:
        const passportReq = request as PassportVerificationRequest;
        return {
          passport_number: passportReq.documentNumber,
          first_name: passportReq.firstName,
          last_name: passportReq.lastName,
          date_of_birth: passportReq.dateOfBirth,
        };

      case DocumentType.VOTERS_CARD:
        const votersReq = request as VotersCardVerificationRequest;
        return {
          voter_id: votersReq.documentNumber,
          first_name: votersReq.firstName,
          last_name: votersReq.lastName,
        };

      case DocumentType.TIN:
        const tinReq = request as TinVerificationRequest;
        return {
          tin_number: tinReq.documentNumber,
        };

      case DocumentType.FACIAL_IMAGE:
      case DocumentType.SELFIE:
        const facialReq = request as FacialVerificationRequest;
        return {
          image: facialReq.imageData,
          reference_image: facialReq.referenceImageData,
        };

      default:
        throw new Error(`Document type ${request.documentType} not supported for Ghana`);
    }
  }

  private buildKenyaPayload(request: VerificationRequest): Record<string, any> {
    switch (request.documentType) {
      case DocumentType.NATIONAL_ID:
        const nationalIdReq = request as NationalIdVerificationRequest;
        return {
          national_id: nationalIdReq.documentNumber,
          first_name: nationalIdReq.firstName,
          last_name: nationalIdReq.lastName,
          date_of_birth: nationalIdReq.dateOfBirth,
        };

      case DocumentType.DRIVERS_LICENSE:
        const dlReq = request as DriversLicenseVerificationRequest;
        return {
          license_number: dlReq.documentNumber,
          first_name: dlReq.firstName,
          last_name: dlReq.lastName,
          date_of_birth: dlReq.dateOfBirth,
        };

      case DocumentType.INTERNATIONAL_PASSPORT:
        const passportReq = request as PassportVerificationRequest;
        return {
          passport_number: passportReq.documentNumber,
          first_name: passportReq.firstName,
          last_name: passportReq.lastName,
          date_of_birth: passportReq.dateOfBirth,
        };

      case DocumentType.TIN:
        const tinReq = request as TinVerificationRequest;
        return {
          kra_pin: tinReq.documentNumber,
        };

      case DocumentType.FACIAL_IMAGE:
      case DocumentType.SELFIE:
        const facialReq = request as FacialVerificationRequest;
        return {
          image: facialReq.imageData,
          reference_image: facialReq.referenceImageData,
        };

      default:
        throw new Error(`Document type ${request.documentType} not supported for Kenya`);
    }
  }

  private setupLogging(): void {
    this.httpClient.interceptors.request.use(
      (config) => {
        this.logger.debug(`Zeeh API Request: ${config.method?.toUpperCase()} ${config.url}`, {
          headers: {
            ...config.headers,
            'Authorization': '[REDACTED]',
            'x-api-key': '[REDACTED]'
          },
          data: config.data,
        });
        return config;
      },
      (error) => {
        this.logger.error('Zeeh API Request Error', error);
        return Promise.reject(error);
      }
    );

    this.httpClient.interceptors.response.use(
      (response) => {
        this.logger.debug(`Zeeh API Response: ${response.status}`, {
          data: response.data,
        });
        return response;
      },
      (error) => {
        this.logger.error('Zeeh API Response Error', {
          status: error.response?.status,
          data: error.response?.data,
          message: error.message,
        });
        return Promise.reject(error);
      }
    );
  }

  async checkHealth(): Promise<ProviderHealthStatus> {
    try {
      const startTime = Date.now();
      // Zeeh doesn't have a dedicated health endpoint, so we'll make a simple request
      // This is just a connectivity test to their base URL
      const response = await this.httpClient.get('/', { timeout: 5000 });
      const responseTime = Date.now() - startTime;

      return {
        isHealthy: response.status === 200 || response.status === 404, // 404 is OK for base URL
        responseTime,
        lastChecked: new Date(),
      };
    } catch (error) {
      return {
        isHealthy: false,
        lastChecked: new Date(),
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }





  async verify(request: VerificationRequest, countryCode: string = '234'): Promise<VerificationResult> {
    // Validate country support
    if (!this.supportedCountries.includes(countryCode)) {
      throw new Error(`Country ${countryCode} not supported by Zeeh`);
    }

    // Validate document support for this country
    if (!this.isDocumentSupportedInCountry(countryCode, request.documentType)) {
      throw new Error(`Document type ${request.documentType} not supported in country ${countryCode}`);
    }

    try {
      // Get country-specific endpoint
      const endpoints = this.getCountryEndpoints(countryCode);
      const endpoint = endpoints[request.documentType];

      if (!endpoint) {
        throw new Error(`No endpoint configured for ${request.documentType} in country ${countryCode}`);
      }

      // Build country-specific payload
      const payload = this.buildVerificationPayload(request, countryCode);

      this.logger.log(`Making verification request to Zeeh`, {
        endpoint,
        countryCode,
        documentType: request.documentType,
      });

      // Make API call
      const response: AxiosResponse<ZeehApiResponse> = await this.httpClient.post(endpoint, payload);

      // Parse response (might also be country-specific)
      return this.parseZeehResponse(response.data, request.documentType, countryCode);

    } catch (error) {
      return this.handleError(error, request.documentType);
    }
  }

  // Legacy methods for backward compatibility (Nigeria only)
  async verifyBvn(request: BvnVerificationRequest): Promise<VerificationResult> {
    return this.verify(request, '234');
  }

  async verifyNin(request: NinVerificationRequest): Promise<VerificationResult> {
    return this.verify(request, '234');
  }

  async verifyDriversLicense(request: DriversLicenseVerificationRequest): Promise<VerificationResult> {
    return this.verify(request, '234');
  }

  async verifyInternationalPassport(request: PassportVerificationRequest): Promise<VerificationResult> {
    return this.verify(request, '234');
  }

  async verifyVotersCard(request: VotersCardVerificationRequest): Promise<VerificationResult> {
    return this.verify(request, '234');
  }

  async verifyCac(request: CacVerificationRequest): Promise<VerificationResult> {
    return this.verify(request, '234');
  }

  async verifyTin(request: TinVerificationRequest): Promise<VerificationResult> {
    return this.verify(request, '234');
  }

  async verifyFacial(request: FacialVerificationRequest): Promise<VerificationResult> {
    return this.verify(request, '234');
  }

  private parseZeehResponse(response: ZeehApiResponse, documentType: DocumentType, countryCode: string = '234'): VerificationResult {
    // Zeeh considers success=true as verification success
    const isVerified = response.success && (
      response.data?.status === 'verified' ||
      response.data?.status === 'success' ||
      response.success === true
    );

    return {
      success: response.success,
      verified: isVerified,
      confidence: response.data?.confidence_score || (isVerified ? 0.9 : 0.0),
      providerReference: response.data?.verification_id || `zeeh_${countryCode}_${Date.now()}`,
      providerResponse: response,
      extractedData: this.extractDataFromResponse(response, documentType, countryCode),
      failureReason: !response.success ? (response.error?.message || response.message) : undefined,
      requiresManualReview: response.data?.status === 'pending',
    };
  }

  private extractDataFromResponse(response: ZeehApiResponse, documentType: DocumentType, countryCode: string = '234'): Record<string, any> | undefined {
    if (!response.data || !response.success) {
      return undefined;
    }

    const data = response.data;
    const extracted: Record<string, any> = {};

    // Common fields from main data object
    if (data.first_name) extracted.firstName = data.first_name;
    if (data.last_name) extracted.lastName = data.last_name;
    if (data.middle_name) extracted.middleName = data.middle_name;
    if (data.date_of_birth) extracted.dateOfBirth = data.date_of_birth;
    if (data.phone_number) extracted.phoneNumber = data.phone_number;
    if (data.email) extracted.email = data.email;
    if (data.gender) extracted.gender = data.gender;

    // Country and document-specific extraction
    switch (countryCode) {
      case '234': // Nigeria
        return this.extractNigeriaData(data, documentType, extracted);

      case '233': // Ghana
        return this.extractGhanaData(data, documentType, extracted);

      case '254': // Kenya
        return this.extractKenyaData(data, documentType, extracted);

      default:
        return this.extractDefaultData(data, documentType, extracted);
    }
  }

  private extractNigeriaData(data: any, documentType: DocumentType, extracted: Record<string, any>): Record<string, any> | undefined {
    // Document-specific fields for Nigeria
    switch (documentType) {
      case DocumentType.BVN:
        if (data.bvn) extracted.bvn = data.bvn;
        break;
      case DocumentType.NIN:
        if (data.nin) extracted.nin = data.nin;
        break;
      case DocumentType.DRIVERS_LICENSE:
        if (data.license_number) extracted.licenseNumber = data.license_number;
        if (data.issue_date) extracted.issueDate = data.issue_date;
        if (data.expiry_date) extracted.expiryDate = data.expiry_date;
        if (data.state_of_issue) extracted.stateOfIssue = data.state_of_issue;
        break;
      case DocumentType.INTERNATIONAL_PASSPORT:
        if (data.passport_number) extracted.passportNumber = data.passport_number;
        if (data.issue_date) extracted.issueDate = data.issue_date;
        if (data.expiry_date) extracted.expiryDate = data.expiry_date;
        break;
      case DocumentType.CAC:
        if (data.rc_number) extracted.rcNumber = data.rc_number;
        if (data.company_name) extracted.companyName = data.company_name;
        if (data.company_type) extracted.companyType = data.company_type;
        if (data.registration_date) extracted.registrationDate = data.registration_date;
        if (data.company_status) extracted.companyStatus = data.company_status;
        break;
      case DocumentType.TIN:
        if (data.tin) extracted.tin = data.tin;
        if (data.taxpayer_name) extracted.taxpayerName = data.taxpayer_name;
        if (data.taxpayer_type) extracted.taxpayerType = data.taxpayer_type;
        break;
      case DocumentType.VOTERS_CARD:
        if (data.vin) extracted.vin = data.vin;
        break;
      case DocumentType.FACIAL_IMAGE:
      case DocumentType.SELFIE:
        if (data.match_score) extracted.matchScore = data.match_score;
        if (data.liveness_score) extracted.livenessScore = data.liveness_score;
        break;
    }

    return Object.keys(extracted).length > 0 ? extracted : undefined;
  }

  private extractGhanaData(data: any, documentType: DocumentType, extracted: Record<string, any>): Record<string, any> | undefined {
    // Document-specific fields for Ghana
    switch (documentType) {
      case DocumentType.DRIVERS_LICENSE:
        if (data.license_number) extracted.licenseNumber = data.license_number;
        if (data.issue_date) extracted.issueDate = data.issue_date;
        if (data.expiry_date) extracted.expiryDate = data.expiry_date;
        if (data.issuing_authority) extracted.issuingAuthority = data.issuing_authority;
        break;
      case DocumentType.INTERNATIONAL_PASSPORT:
        if (data.passport_number) extracted.passportNumber = data.passport_number;
        if (data.issue_date) extracted.issueDate = data.issue_date;
        if (data.expiry_date) extracted.expiryDate = data.expiry_date;
        break;
      case DocumentType.VOTERS_CARD:
        if (data.voter_id) extracted.voterId = data.voter_id;
        if (data.constituency) extracted.constituency = data.constituency;
        if (data.polling_station) extracted.pollingStation = data.polling_station;
        break;
      case DocumentType.TIN:
        if (data.tin_number) extracted.tinNumber = data.tin_number;
        if (data.taxpayer_name) extracted.taxpayerName = data.taxpayer_name;
        if (data.taxpayer_type) extracted.taxpayerType = data.taxpayer_type;
        break;
      case DocumentType.FACIAL_IMAGE:
      case DocumentType.SELFIE:
        if (data.match_score) extracted.matchScore = data.match_score;
        if (data.liveness_score) extracted.livenessScore = data.liveness_score;
        break;
    }

    return Object.keys(extracted).length > 0 ? extracted : undefined;
  }

  private extractKenyaData(data: any, documentType: DocumentType, extracted: Record<string, any>): Record<string, any> | undefined {
    // Document-specific fields for Kenya
    switch (documentType) {
      case DocumentType.NATIONAL_ID:
        if (data.national_id) extracted.nationalId = data.national_id;
        if (data.id_number) extracted.idNumber = data.id_number;
        if (data.place_of_birth) extracted.placeOfBirth = data.place_of_birth;
        if (data.place_of_issue) extracted.placeOfIssue = data.place_of_issue;
        break;
      case DocumentType.DRIVERS_LICENSE:
        if (data.license_number) extracted.licenseNumber = data.license_number;
        if (data.issue_date) extracted.issueDate = data.issue_date;
        if (data.expiry_date) extracted.expiryDate = data.expiry_date;
        if (data.license_class) extracted.licenseClass = data.license_class;
        break;
      case DocumentType.INTERNATIONAL_PASSPORT:
        if (data.passport_number) extracted.passportNumber = data.passport_number;
        if (data.issue_date) extracted.issueDate = data.issue_date;
        if (data.expiry_date) extracted.expiryDate = data.expiry_date;
        break;
      case DocumentType.TIN:
        if (data.kra_pin) extracted.kraPin = data.kra_pin;
        if (data.taxpayer_name) extracted.taxpayerName = data.taxpayer_name;
        if (data.taxpayer_type) extracted.taxpayerType = data.taxpayer_type;
        break;
      case DocumentType.FACIAL_IMAGE:
      case DocumentType.SELFIE:
        if (data.match_score) extracted.matchScore = data.match_score;
        if (data.liveness_score) extracted.livenessScore = data.liveness_score;
        break;
    }

    return Object.keys(extracted).length > 0 ? extracted : undefined;
  }

  private extractDefaultData(data: any, documentType: DocumentType, extracted: Record<string, any>): Record<string, any> | undefined {
    // Default extraction for unsupported countries
    switch (documentType) {
      case DocumentType.FACIAL_IMAGE:
      case DocumentType.SELFIE:
        if (data.match_score) extracted.matchScore = data.match_score;
        if (data.liveness_score) extracted.livenessScore = data.liveness_score;
        break;
    }

    // Also check details object if it exists
    if (data.details) {
      const details = data.details;
      if (details.first_name && !extracted.firstName) extracted.firstName = details.first_name;
      if (details.last_name && !extracted.lastName) extracted.lastName = details.last_name;
      if (details.date_of_birth && !extracted.dateOfBirth) extracted.dateOfBirth = details.date_of_birth;
      if (details.phone_number && !extracted.phoneNumber) extracted.phoneNumber = details.phone_number;
      if (details.gender && !extracted.gender) extracted.gender = details.gender;
    }

    return Object.keys(extracted).length > 0 ? extracted : undefined;
  }

  private handleError(error: any, documentType: DocumentType): VerificationResult {
    const errorMessage = error.response?.data?.error?.message || 
                        error.response?.data?.message || 
                        error.message || 
                        'Unknown error';
    
    return {
      success: false,
      verified: false,
      providerReference: '',
      providerResponse: error.response?.data || {},
      failureReason: errorMessage,
      requiresManualReview: error.response?.status === 500, // Server errors might need manual review
    };
  }

  verifyWebhookSignature(payload: string, signature: string): boolean {
    // Implement Zeeh webhook signature verification if they support it
    return true; // Placeholder
  }

  parseWebhookPayload(payload: any): { providerReference: string; status: 'verified' | 'failed' | 'requires_review'; result: VerificationResult } {
    // Implement Zeeh webhook payload parsing if they support it
    throw new Error('Webhook parsing not implemented for Zeeh');
  }
}
