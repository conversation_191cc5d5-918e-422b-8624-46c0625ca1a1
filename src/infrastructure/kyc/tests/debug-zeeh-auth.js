const https = require('https');
const fs = require('fs');
const path = require('path');

// Simple dotenv implementation
function loadEnv() {
  try {
    const envPath = path.join(__dirname, '.env');
    const envContent = fs.readFileSync(envPath, 'utf8');
    const lines = envContent.split('\n');
    
    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed && !trimmed.startsWith('#')) {
        const [key, ...valueParts] = trimmed.split('=');
        if (key && valueParts.length > 0) {
          process.env[key.trim()] = valueParts.join('=').trim();
        }
      }
    }
  } catch (error) {
    console.log('⚠️  Could not load .env file:', error.message);
  }
}

loadEnv();

function makeHttpsRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          resolve({
            status: res.statusCode,
            data: parsedData,
            headers: res.headers
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            data: responseData,
            headers: res.headers
          });
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function debugZeehAuth() {
  console.log('🔧 Debugging Zeeh Authentication...\n');

  // Show API key info (masked for security)
  console.log('🔑 API Key Information:');
  const apiKey = process.env.ZEEH_API_KEY || '';
  const secretKey = process.env.ZEEH_SECRET_KEY || '';
  
  console.log(`   API Key Length: ${apiKey.length}`);
  console.log(`   API Key Preview: ${apiKey.substring(0, 8)}...${apiKey.substring(apiKey.length - 4)}`);
  console.log(`   Secret Key Length: ${secretKey.length}`);
  console.log(`   Secret Key Preview: ${secretKey.substring(0, 8)}...${secretKey.substring(secretKey.length - 4)}`);

  const baseUrl = process.env.ZEEH_BASE_URL || 'https://api.usezeeh.com';
  const url = new URL(baseUrl);

  // Test different authentication methods
  const authMethods = [
    {
      name: 'Current Method (Bearer + x-api-key)',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
        'x-api-key': secretKey,
      }
    },
    {
      name: 'Alternative 1 (Only Bearer)',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      }
    },
    {
      name: 'Alternative 2 (Only x-api-key)',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': apiKey,
      }
    },
    {
      name: 'Alternative 3 (API-Key header)',
      headers: {
        'Content-Type': 'application/json',
        'API-Key': apiKey,
        'Secret-Key': secretKey,
      }
    },
    {
      name: 'Alternative 4 (Basic Auth)',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Basic ${Buffer.from(`${apiKey}:${secretKey}`).toString('base64')}`,
      }
    }
  ];

  for (const method of authMethods) {
    console.log(`\n🧪 Testing: ${method.name}`);
    
    try {
      const options = {
        hostname: url.hostname,
        port: url.port || 443,
        path: '/v1/nigeria_kyc/lookup_bvn',
        method: 'POST',
        timeout: 10000,
        headers: method.headers,
      };

      const testPayload = {
        bvn: '22222222222',
        first_name: 'John',
        last_name: 'Doe',
      };

      const response = await makeHttpsRequest(options, testPayload);
      
      console.log(`   Status: ${response.status}`);
      if (response.status === 200) {
        console.log(`   ✅ SUCCESS! This authentication method works!`);
        console.log(`   Response:`, JSON.stringify(response.data, null, 2));
        break;
      } else if (response.status === 401) {
        console.log(`   ❌ Still getting 401 - Invalid credentials`);
        if (response.data.message) {
          console.log(`   Message: ${response.data.message}`);
        }
      } else if (response.status === 400) {
        console.log(`   ⚠️  Got 400 - This might mean auth is working but data is invalid`);
        console.log(`   Message: ${response.data.message || 'Bad request'}`);
      } else {
        console.log(`   ⚠️  Got ${response.status} - ${response.data.message || 'Unknown response'}`);
      }
      
    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
    }
  }

  console.log('\n📋 Troubleshooting Checklist:');
  console.log('   1. ✅ Check if API keys are from the correct environment (sandbox vs production)');
  console.log('   2. ✅ Verify API keys are copied correctly (no extra spaces/characters)');
  console.log('   3. ✅ Confirm your Zeeh account is active and has API access');
  console.log('   4. ✅ Check if there are any IP restrictions on your API keys');
  console.log('   5. ✅ Verify the base URL is correct for your environment');
  
  console.log('\n💡 Next Steps:');
  console.log('   - Contact Zeeh support if all authentication methods fail');
  console.log('   - Check Zeeh documentation for the latest authentication format');
  console.log('   - Verify your account status and API permissions');
}

debugZeehAuth().catch(console.error);
