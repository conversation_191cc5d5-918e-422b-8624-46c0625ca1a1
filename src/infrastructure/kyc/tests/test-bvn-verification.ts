import { NestFactory } from '@nestjs/core';
import { AppModule } from '../../../app.module';
import { VerifyBvnUseCase } from '../../../core/use-cases/kyc/verify-bvn.use-case';
import { KycProviderManager } from '../kyc-provider-manager';
import { ZeehKycProvider } from '../providers/zeeh-kyc.provider';
import { KycProviderFactory } from '../kyc-provider-factory';
import { DocumentType } from '../../../core/entities/kyc-verification.entity';
import { BvnVerificationRequest } from '../../../core/ports/kyc/kyc-provider.port';

async function testBvnVerification() {
  console.log('🚀 Starting BVN Verification Test...\n');

  try {
    // Create NestJS application context
    const app = await NestFactory.createApplicationContext(AppModule);
    
    console.log('✅ Application context created successfully');

    // Test 1: Check if Zeeh provider is properly configured
    console.log('\n📋 Test 1: Checking Zeeh Provider Configuration...');
    
    const providerFactory = app.get(KycProviderFactory);
    
    // Check if Zeeh is configured
    const isZeehConfigured = providerFactory.isProviderConfigured('zeeh');
    console.log(`   Zeeh configured: ${isZeehConfigured ? '✅' : '❌'}`);
    
    if (!isZeehConfigured) {
      console.log('❌ Zeeh is not properly configured. Please check your environment variables.');
      await app.close();
      return;
    }

    // Test 2: Create Zeeh provider instance
    console.log('\n📋 Test 2: Creating Zeeh Provider Instance...');
    
    try {
      const zeehProvider = providerFactory.create('zeeh') as ZeehKycProvider;
      console.log(`   Provider ID: ${zeehProvider.providerId}`);
      console.log(`   Supported Countries: ${zeehProvider.supportedCountries.join(', ')}`);
      console.log(`   Supports BVN: ${zeehProvider.supportedDocuments.includes(DocumentType.BVN) ? '✅' : '❌'}`);
      console.log('✅ Zeeh provider instance created successfully');
    } catch (error) {
      console.log(`❌ Failed to create Zeeh provider: ${error.message}`);
      await app.close();
      return;
    }

    // Test 3: Check Zeeh API connectivity
    console.log('\n📋 Test 3: Testing Zeeh API Connectivity...');
    
    try {
      const zeehProvider = providerFactory.create('zeeh') as ZeehKycProvider;
      const healthStatus = await zeehProvider.checkHealth();
      
      console.log(`   Health Status: ${healthStatus.isHealthy ? '✅ Healthy' : '❌ Unhealthy'}`);
      console.log(`   Response Time: ${healthStatus.responseTime}ms`);
      console.log(`   Last Checked: ${healthStatus.lastChecked}`);
      
      if (healthStatus.errorMessage) {
        console.log(`   Error: ${healthStatus.errorMessage}`);
      }
      
      if (!healthStatus.isHealthy) {
        console.log('⚠️  Zeeh API is not healthy, but continuing with test...');
      }
    } catch (error) {
      console.log(`❌ Health check failed: ${error.message}`);
    }

    // Test 4: Test BVN verification with test data
    console.log('\n📋 Test 4: Testing BVN Verification...');
    
    try {
      const verifyBvnUseCase = app.get(VerifyBvnUseCase);
      
      // Test BVN data (using a test BVN - replace with actual test data if available)
      const testBvnData = {
        userId: 'test-user-' + Date.now(),
        bvn: '22222222222', // Test BVN - replace with valid test BVN
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: '1990-01-01',
        phoneNumber: '08123456789',
        countryCode: '234', // Nigeria
      };

      console.log(`   Testing with BVN: ${testBvnData.bvn}`);
      console.log(`   User: ${testBvnData.firstName} ${testBvnData.lastName}`);
      console.log(`   DOB: ${testBvnData.dateOfBirth}`);
      console.log(`   Phone: ${testBvnData.phoneNumber}`);

      const startTime = Date.now();
      const result = await verifyBvnUseCase.execute(testBvnData);
      const endTime = Date.now();

      console.log(`\n   📊 Verification Results:`);
      console.log(`   ✅ Verification completed in ${endTime - startTime}ms`);
      console.log(`   📋 Verification ID: ${result.verification.id}`);
      console.log(`   🎯 Status: ${result.verification.status}`);
      console.log(`   ✅ Is Verified: ${result.isVerified ? '✅ Yes' : '❌ No'}`);
      console.log(`   🏢 Provider Used: ${result.providerUsed}`);
      console.log(`   ⏱️  Response Time: ${result.responseTime}ms`);

      if (result.extractedData) {
        console.log(`\n   📄 Extracted Data:`);
        Object.entries(result.extractedData).forEach(([key, value]) => {
          console.log(`      ${key}: ${value}`);
        });
      }

      if (!result.isVerified) {
        console.log(`   ❌ Verification failed. This might be expected with test data.`);
        console.log(`   📝 Failure reason: ${result.verification.failureReason || 'Unknown'}`);
      }

    } catch (error) {
      console.log(`❌ BVN verification test failed: ${error.message}`);
      console.log(`   Stack trace: ${error.stack}`);
    }

    // Test 5: Test direct provider call
    console.log('\n📋 Test 5: Testing Direct Provider Call...');
    
    try {
      const zeehProvider = providerFactory.create('zeeh') as ZeehKycProvider;
      
      const directRequest: BvnVerificationRequest = {
        documentType: DocumentType.BVN,
        documentNumber: '22222222222', // Test BVN
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: '1990-01-01',
        phoneNumber: '08123456789',
      };

      console.log(`   Making direct call to Zeeh API...`);
      const startTime = Date.now();
      const directResult = await zeehProvider.verifyBvn(directRequest);
      const endTime = Date.now();

      console.log(`\n   📊 Direct API Results:`);
      console.log(`   ✅ API call completed in ${endTime - startTime}ms`);
      console.log(`   🎯 Success: ${directResult.success ? '✅ Yes' : '❌ No'}`);
      console.log(`   ✅ Verified: ${directResult.verified ? '✅ Yes' : '❌ No'}`);
      console.log(`   📊 Confidence: ${directResult.confidence}`);
      console.log(`   🔗 Provider Reference: ${directResult.providerReference}`);

      if (directResult.extractedData) {
        console.log(`\n   📄 Extracted Data:`);
        Object.entries(directResult.extractedData).forEach(([key, value]) => {
          console.log(`      ${key}: ${value}`);
        });
      }

      if (directResult.failureReason) {
        console.log(`   ❌ Failure Reason: ${directResult.failureReason}`);
      }

      if (directResult.providerResponse) {
        console.log(`\n   📡 Raw Provider Response:`);
        console.log(JSON.stringify(directResult.providerResponse, null, 2));
      }

    } catch (error) {
      console.log(`❌ Direct provider call failed: ${error.message}`);
      console.log(`   This might indicate an API configuration issue.`);
    }

    // Test 6: Test provider manager
    console.log('\n📋 Test 6: Testing Provider Manager...');
    
    try {
      const providerManager = app.get(KycProviderManager);
      
      const managerRequest: BvnVerificationRequest = {
        documentType: DocumentType.BVN,
        documentNumber: '22222222222',
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: '1990-01-01',
        phoneNumber: '08123456789',
      };

      console.log(`   Testing provider manager with failover...`);
      const startTime = Date.now();
      const { result: managerResult, log } = await providerManager.verifyDocument(
        '234', // Nigeria
        DocumentType.BVN,
        managerRequest
      );
      const endTime = Date.now();

      console.log(`\n   📊 Provider Manager Results:`);
      console.log(`   ✅ Manager call completed in ${endTime - startTime}ms`);
      console.log(`   🎯 Success: ${managerResult.success ? '✅ Yes' : '❌ No'}`);
      console.log(`   ✅ Verified: ${managerResult.verified ? '✅ Yes' : '❌ No'}`);
      console.log(`   📊 Attempts Made: ${log.attempts.length}`);
      
      log.attempts.forEach((attempt, index) => {
        console.log(`      Attempt ${index + 1}: ${attempt.providerId} - ${attempt.success ? '✅ Success' : '❌ Failed'} (${attempt.responseTime}ms)`);
        if (attempt.error) {
          console.log(`         Error: ${attempt.error}`);
        }
      });

    } catch (error) {
      console.log(`❌ Provider manager test failed: ${error.message}`);
    }

    console.log('\n🎉 BVN Verification Test Completed!');
    console.log('\n📝 Summary:');
    console.log('   - If you see API errors, this is normal with test data');
    console.log('   - The important thing is that the configuration is working');
    console.log('   - Check that Zeeh provider is configured and can make API calls');
    console.log('   - For production testing, use valid BVN data from Zeeh documentation');

    await app.close();

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

// Run the test
testBvnVerification().catch(console.error);
