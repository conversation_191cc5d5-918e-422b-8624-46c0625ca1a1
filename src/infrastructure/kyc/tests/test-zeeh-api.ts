import axios from 'axios';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function testZeehAPI() {
  console.log('🔍 Testing Zeeh API Configuration...\n');

  // Check environment variables
  console.log('📋 Environment Variables:');
  console.log(`   ZEEH_API_KEY: ${process.env.ZEEH_API_KEY ? '✅ Set' : '❌ Missing'}`);
  console.log(`   ZEEH_SECRET_KEY: ${process.env.ZEEH_SECRET_KEY ? '✅ Set' : '❌ Missing'}`);
  console.log(`   ZEEH_BASE_URL: ${process.env.ZEEH_BASE_URL || 'https://api.usezeeh.com/v1'}`);

  if (!process.env.ZEEH_API_KEY || !process.env.ZEEH_SECRET_KEY) {
    console.log('❌ Missing required Zeeh configuration');
    return;
  }

  // Create HTTP client
  const httpClient = axios.create({
    baseURL: process.env.ZEEH_BASE_URL || 'https://api.usezeeh.com/v1',
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${process.env.ZEEH_API_KEY}`,
      'x-api-key': process.env.ZEEH_SECRET_KEY,
    },
  });

  // Test 1: Basic connectivity
  console.log('\n🌐 Test 1: Basic API Connectivity...');
  try {
    const response = await httpClient.get('/', { timeout: 5000 });
    console.log(`   Status: ${response.status}`);
    console.log(`   ✅ API is reachable`);
  } catch (error) {
    if (error.response) {
      console.log(`   Status: ${error.response.status}`);
      console.log(`   ✅ API is reachable (${error.response.status} is expected for base URL)`);
    } else {
      console.log(`   ❌ API connectivity failed: ${error.message}`);
      return;
    }
  }

  // Test 2: Test BVN endpoint with test data
  console.log('\n🔍 Test 2: Testing BVN Verification Endpoint...');
  try {
    const testPayload = {
      bvn: '22222222222', // Test BVN
      first_name: 'John',
      last_name: 'Doe',
      date_of_birth: '1990-01-01',
      phone_number: '08123456789',
    };

    console.log(`   Payload:`, testPayload);
    console.log(`   Endpoint: /nigeria_kyc/lookup_bvn`);

    const startTime = Date.now();
    const response = await httpClient.post('/nigeria_kyc/lookup_bvn', testPayload);
    const endTime = Date.now();

    console.log(`\n   📊 Response:`);
    console.log(`   Status: ${response.status}`);
    console.log(`   Response Time: ${endTime - startTime}ms`);
    console.log(`   Data:`, JSON.stringify(response.data, null, 2));

    if (response.data.success) {
      console.log(`   ✅ API call successful`);
    } else {
      console.log(`   ⚠️  API call completed but verification failed (expected with test data)`);
    }

  } catch (error) {
    console.log(`   ❌ BVN verification failed:`);
    if (error.response) {
      console.log(`   Status: ${error.response.status}`);
      console.log(`   Error Data:`, JSON.stringify(error.response.data, null, 2));
      
      // Common error interpretations
      if (error.response.status === 401) {
        console.log(`   🔑 This indicates authentication issues - check your API keys`);
      } else if (error.response.status === 400) {
        console.log(`   📝 This indicates bad request - might be expected with test data`);
      } else if (error.response.status === 403) {
        console.log(`   🚫 This indicates forbidden - check your API permissions`);
      } else if (error.response.status === 429) {
        console.log(`   ⏰ This indicates rate limiting - too many requests`);
      }
    } else {
      console.log(`   Network Error: ${error.message}`);
    }
  }

  // Test 3: Test different endpoints
  console.log('\n🔍 Test 3: Testing Other Endpoints...');
  
  const endpoints = [
    { name: 'NIN Lookup', path: '/nigeria_kyc/lookup_nin', payload: { nin: '12345678901', first_name: 'John', last_name: 'Doe' } },
    { name: 'CAC Lookup', path: '/nigeria_kyc/lookup_cac', payload: { rc_number: 'RC123456', company_name: 'Test Company' } },
    { name: 'TIN Lookup', path: '/nigeria_kyc/lookup_tin', payload: { tin: '12345678-0001' } },
  ];

  for (const endpoint of endpoints) {
    try {
      console.log(`\n   Testing ${endpoint.name}...`);
      const response = await httpClient.post(endpoint.path, endpoint.payload);
      console.log(`   ✅ ${endpoint.name}: ${response.status} - ${response.data.success ? 'Success' : 'Failed'}`);
    } catch (error) {
      if (error.response) {
        console.log(`   ⚠️  ${endpoint.name}: ${error.response.status} - ${error.response.data?.message || 'Error'}`);
      } else {
        console.log(`   ❌ ${endpoint.name}: Network error`);
      }
    }
  }

  console.log('\n🎉 Zeeh API Test Completed!');
  console.log('\n📝 Summary:');
  console.log('   - 401 errors indicate authentication issues');
  console.log('   - 400 errors with test data are normal');
  console.log('   - 200 responses indicate successful API communication');
  console.log('   - Check the response data to see if verification succeeded');
}

// Run the test
testZeehAPI().catch(console.error);
