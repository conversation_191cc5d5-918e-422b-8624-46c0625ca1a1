const https = require('https');
const fs = require('fs');
const path = require('path');

// Simple dotenv implementation
function loadEnv() {
  try {
    const envPath = path.join(__dirname, '.env');
    const envContent = fs.readFileSync(envPath, 'utf8');
    const lines = envContent.split('\n');
    
    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed && !trimmed.startsWith('#')) {
        const [key, ...valueParts] = trimmed.split('=');
        if (key && valueParts.length > 0) {
          process.env[key.trim()] = valueParts.join('=').trim();
        }
      }
    }
  } catch (error) {
    console.log('⚠️  Could not load .env file:', error.message);
  }
}

// Load environment variables
loadEnv();

function makeHttpsRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          resolve({
            status: res.statusCode,
            data: parsedData,
            headers: res.headers
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            data: responseData,
            headers: res.headers
          });
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function testZeehAPI() {
  console.log('🔍 Testing Zeeh API Configuration...\n');

  // Check environment variables
  console.log('📋 Environment Variables:');
  console.log(`   ZEEH_API_KEY: ${process.env.ZEEH_API_KEY ? '✅ Set' : '❌ Missing'}`);
  console.log(`   ZEEH_SECRET_KEY: ${process.env.ZEEH_SECRET_KEY ? '✅ Set' : '❌ Missing'}`);
  console.log(`   ZEEH_BASE_URL: ${process.env.ZEEH_BASE_URL || 'https://api.usezeeh.com/v1'}`);

  if (!process.env.ZEEH_API_KEY || !process.env.ZEEH_SECRET_KEY) {
    console.log('❌ Missing required Zeeh configuration');
    return;
  }

  const baseUrl = process.env.ZEEH_BASE_URL || 'https://api.usezeeh.com';
  const url = new URL(baseUrl);

  // Test 1: Basic connectivity
  console.log('\n🌐 Test 1: Basic API Connectivity...');
  try {
    const options = {
      hostname: url.hostname,
      port: url.port || 443,
      path: '/v1/',
      method: 'GET',
      timeout: 5000,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.ZEEH_API_KEY}`,
        'x-api-key': process.env.ZEEH_SECRET_KEY,
      },
    };

    const response = await makeHttpsRequest(options);
    console.log(`   Status: ${response.status}`);
    console.log(`   ✅ API is reachable (${response.status} is expected for base URL)`);
  } catch (error) {
    console.log(`   ❌ API connectivity failed: ${error.message}`);
    return;
  }

  // Test 2: Test BVN endpoint with test data
  console.log('\n🔍 Test 2: Testing BVN Verification Endpoint...');
  try {
    const testPayload = {
      bvn: '22222222222', // Test BVN
      first_name: 'John',
      last_name: 'Doe',
      date_of_birth: '1990-01-01',
      phone_number: '08123456789',
    };

    console.log(`   Payload:`, testPayload);
    console.log(`   Endpoint: /v1/nigeria_kyc/lookup_bvn`);

    const options = {
      hostname: url.hostname,
      port: url.port || 443,
      path: '/v1/nigeria_kyc/lookup_bvn',
      method: 'POST',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.ZEEH_API_KEY}`,
        'x-api-key': process.env.ZEEH_SECRET_KEY,
      },
    };

    const startTime = Date.now();
    const response = await makeHttpsRequest(options, testPayload);
    const endTime = Date.now();

    console.log(`\n   📊 Response:`);
    console.log(`   Status: ${response.status}`);
    console.log(`   Response Time: ${endTime - startTime}ms`);
    console.log(`   Data:`, JSON.stringify(response.data, null, 2));

    if (response.status === 200 && response.data.success) {
      console.log(`   ✅ API call successful`);
      if (response.data.data) {
        console.log(`   📄 Extracted Data:`);
        Object.entries(response.data.data).forEach(([key, value]) => {
          console.log(`      ${key}: ${value}`);
        });
      }
    } else {
      console.log(`   ⚠️  API call completed but verification failed (expected with test data)`);
      if (response.data.message) {
        console.log(`   📝 Message: ${response.data.message}`);
      }
    }

  } catch (error) {
    console.log(`   ❌ BVN verification failed: ${error.message}`);
  }

  // Test 3: Test different endpoints
  console.log('\n🔍 Test 3: Testing Other Endpoints...');
  
  const endpoints = [
    { name: 'NIN Lookup', path: '/v1/nigeria_kyc/lookup_nin', payload: { nin: '12345678901', first_name: 'John', last_name: 'Doe' } },
    { name: 'CAC Lookup', path: '/v1/nigeria_kyc/lookup_cac', payload: { rc_number: 'RC123456', company_name: 'Test Company' } },
    { name: 'TIN Lookup', path: '/v1/nigeria_kyc/lookup_tin', payload: { tin: '12345678-0001' } },
  ];

  for (const endpoint of endpoints) {
    try {
      console.log(`\n   Testing ${endpoint.name}...`);
      
      const options = {
        hostname: url.hostname,
        port: url.port || 443,
        path: endpoint.path,
        method: 'POST',
        timeout: 30000,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.ZEEH_API_KEY}`,
          'x-api-key': process.env.ZEEH_SECRET_KEY,
        },
      };

      const response = await makeHttpsRequest(options, endpoint.payload);
      console.log(`   ${response.status === 200 ? '✅' : '⚠️'} ${endpoint.name}: ${response.status} - ${response.data.success ? 'Success' : 'Failed'}`);
      if (response.data.message) {
        console.log(`      Message: ${response.data.message}`);
      }
    } catch (error) {
      console.log(`   ❌ ${endpoint.name}: ${error.message}`);
    }
  }

  console.log('\n🎉 Zeeh API Test Completed!');
  console.log('\n📝 Summary:');
  console.log('   - 401 errors indicate authentication issues');
  console.log('   - 400 errors with test data are normal');
  console.log('   - 200 responses indicate successful API communication');
  console.log('   - Check the response data to see if verification succeeded');
  console.log('\n💡 Next Steps:');
  console.log('   - If you see 401 errors, check your ZEEH_API_KEY and ZEEH_SECRET_KEY');
  console.log('   - If you see 200 responses, your configuration is working!');
  console.log('   - For real verification, use valid BVN data from Zeeh documentation');
}

// Run the test
testZeehAPI().catch(console.error);
