const https = require('https');
const fs = require('fs');
const path = require('path');

// Simple dotenv implementation
function loadEnv() {
  try {
    const envPath = path.join(__dirname, '.env');
    const envContent = fs.readFileSync(envPath, 'utf8');
    const lines = envContent.split('\n');
    
    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed && !trimmed.startsWith('#')) {
        const [key, ...valueParts] = trimmed.split('=');
        if (key && valueParts.length > 0) {
          process.env[key.trim()] = valueParts.join('=').trim();
        }
      }
    }
  } catch (error) {
    console.log('⚠️  Could not load .env file:', error.message);
  }
}

loadEnv();

function makeHttpsRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          resolve({
            status: res.statusCode,
            data: parsedData,
            headers: res.headers
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            data: responseData,
            headers: res.headers
          });
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function testZeehEnvironments() {
  console.log('🌍 Testing Different Zeeh Environments...\n');

  const apiKey = process.env.ZEEH_API_KEY || '';
  const secretKey = process.env.ZEEH_SECRET_KEY || '';

  // Test different base URLs
  const environments = [
    {
      name: 'Production API',
      baseUrl: 'https://api.usezeeh.com',
      description: 'Main production environment'
    },
    {
      name: 'Sandbox API',
      baseUrl: 'https://sandbox.usezeeh.com',
      description: 'Sandbox/testing environment'
    },
    {
      name: 'Alternative Production',
      baseUrl: 'https://api.zeeh.africa',
      description: 'Alternative production URL'
    },
    {
      name: 'Alternative Sandbox',
      baseUrl: 'https://sandbox.zeeh.africa',
      description: 'Alternative sandbox URL'
    }
  ];

  for (const env of environments) {
    console.log(`\n🧪 Testing: ${env.name}`);
    console.log(`   URL: ${env.baseUrl}`);
    console.log(`   Description: ${env.description}`);
    
    try {
      const url = new URL(env.baseUrl);
      
      const options = {
        hostname: url.hostname,
        port: url.port || 443,
        path: '/v1/nigeria_kyc/lookup_bvn',
        method: 'POST',
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`,
          'x-api-key': secretKey,
        },
      };

      const testPayload = {
        bvn: '22222222222',
        first_name: 'John',
        last_name: 'Doe',
      };

      const response = await makeHttpsRequest(options, testPayload);
      
      console.log(`   Status: ${response.status}`);
      
      if (response.status === 200) {
        console.log(`   ✅ SUCCESS! This environment works!`);
        console.log(`   🎯 Use this base URL: ${env.baseUrl}`);
        break;
      } else if (response.status === 401) {
        console.log(`   ❌ 401 - Invalid credentials for this environment`);
      } else if (response.status === 400) {
        console.log(`   ⚠️  400 - Auth might be working, but data is invalid`);
        console.log(`   🎯 This could be the correct environment!`);
      } else if (response.status === 404) {
        console.log(`   ❌ 404 - Endpoint not found (wrong environment)`);
      } else {
        console.log(`   ⚠️  ${response.status} - ${response.data.message || 'Unknown response'}`);
      }
      
      if (response.data.message) {
        console.log(`   Message: ${response.data.message}`);
      }
      
    } catch (error) {
      if (error.code === 'ENOTFOUND') {
        console.log(`   ❌ Environment not reachable (DNS not found)`);
      } else {
        console.log(`   ❌ Error: ${error.message}`);
      }
    }
  }

  console.log('\n📊 Key Analysis:');
  console.log(`   API Key starts with: ${apiKey.substring(0, 3)}`);
  console.log(`   Secret Key starts with: ${secretKey.substring(0, 3)}`);
  
  if (apiKey.startsWith('pk_test_')) {
    console.log('   🧪 Detected: TEST/SANDBOX keys');
    console.log('   💡 Recommendation: Use sandbox environment');
  } else if (apiKey.startsWith('pk_live_')) {
    console.log('   🚀 Detected: LIVE/PRODUCTION keys');
    console.log('   💡 Recommendation: Use production environment');
  } else if (apiKey.startsWith('pk_')) {
    console.log('   🔍 Detected: Standard Zeeh keys');
    console.log('   💡 Recommendation: Check Zeeh dashboard for environment');
  }

  console.log('\n📋 Summary:');
  console.log('   - If all environments return 401, the API keys might be invalid');
  console.log('   - If one environment returns 400, that might be the correct one');
  console.log('   - Check your Zeeh dashboard for the correct environment and keys');
  console.log('   - Ensure your account has API access enabled');
}

testZeehEnvironments().catch(console.error);
