const https = require('https');
const fs = require('fs');
const path = require('path');

// Simple dotenv implementation
function loadEnv() {
  try {
    const envPath = path.join(__dirname, '.env');
    const envContent = fs.readFileSync(envPath, 'utf8');
    const lines = envContent.split('\n');
    
    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed && !trimmed.startsWith('#')) {
        const [key, ...valueParts] = trimmed.split('=');
        if (key && valueParts.length > 0) {
          process.env[key.trim()] = valueParts.join('=').trim();
        }
      }
    }
  } catch (error) {
    console.log('⚠️  Could not load .env file:', error.message);
  }
}

loadEnv();

function makeHttpsRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          resolve({
            status: res.statusCode,
            data: parsedData,
            headers: res.headers
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            data: responseData,
            headers: res.headers
          });
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function testNewHeaders() {
  console.log('🔧 Testing New Zeeh Header Format...\n');

  const apiKey = process.env.ZEEH_API_KEY || '';
  const secretKey = process.env.ZEEH_SECRET_KEY || '';
  const baseUrl = process.env.ZEEH_BASE_URL || 'https://api.usezeeh.com';
  const url = new URL(baseUrl);

  console.log('📋 Testing with your updated header format:');
  console.log('   - Removed Authorization header');
  console.log('   - Using Secret_Key instead of x-api-key');

  // Test with your new header format
  console.log('\n🧪 Test 1: Your New Header Format');
  try {
    const options = {
      hostname: url.hostname,
      port: url.port || 443,
      path: '/v1/nigeria_kyc/lookup_bvn',
      method: 'POST',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
        'Secret_Key': secretKey, // Your new format
      },
    };

    const testPayload = {
      bvn: '22222222222',
      first_name: 'John',
      last_name: 'Doe',
      date_of_birth: '1990-01-01',
      phone_number: '08123456789',
    };

    console.log('   Headers:', JSON.stringify(options.headers, null, 2));
    
    const response = await makeHttpsRequest(options, testPayload);
    
    console.log(`   Status: ${response.status}`);
    console.log(`   Response:`, JSON.stringify(response.data, null, 2));
    
    if (response.status === 200) {
      console.log(`   ✅ SUCCESS! Your header format works!`);
    } else if (response.status === 401) {
      console.log(`   ❌ Still getting 401 with new format`);
    } else if (response.status === 400) {
      console.log(`   ⚠️  Got 400 - Auth might be working but data is invalid`);
    }
    
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`);
  }

  // Test other possible header combinations
  const headerVariations = [
    {
      name: 'Secret_Key + API_Key',
      headers: {
        'Content-Type': 'application/json',
        'Secret_Key': secretKey,
        'API_Key': apiKey,
      }
    },
    {
      name: 'Only API_Key',
      headers: {
        'Content-Type': 'application/json',
        'API_Key': apiKey,
      }
    },
    {
      name: 'Secret-Key (with dash)',
      headers: {
        'Content-Type': 'application/json',
        'Secret-Key': secretKey,
      }
    },
    {
      name: 'Authorization with Secret_Key',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
        'Secret_Key': secretKey,
      }
    }
  ];

  for (const variation of headerVariations) {
    console.log(`\n🧪 Test: ${variation.name}`);
    
    try {
      const options = {
        hostname: url.hostname,
        port: url.port || 443,
        path: '/v1/nigeria_kyc/lookup_bvn',
        method: 'POST',
        timeout: 10000,
        headers: variation.headers,
      };

      const testPayload = {
        bvn: '22222222222',
        first_name: 'John',
        last_name: 'Doe',
      };

      const response = await makeHttpsRequest(options, testPayload);
      
      console.log(`   Status: ${response.status}`);
      
      if (response.status === 200) {
        console.log(`   ✅ SUCCESS! This header combination works!`);
        console.log(`   🎯 Use these headers:`, JSON.stringify(variation.headers, null, 2));
        break;
      } else if (response.status === 401) {
        console.log(`   ❌ 401 - Still invalid credentials`);
      } else if (response.status === 400) {
        console.log(`   ⚠️  400 - Auth might be working, data might be invalid`);
        console.log(`   🎯 This could be the correct format!`);
      } else {
        console.log(`   ⚠️  ${response.status} - ${response.data.message || 'Unknown'}`);
      }
      
    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
    }
  }

  console.log('\n📋 Summary:');
  console.log('   - Your changes show you\'re experimenting with the header format');
  console.log('   - If none of these work, the issue might be with the API keys themselves');
  console.log('   - Check Zeeh documentation for the exact header format they expect');
  console.log('   - Contact Zeeh support to verify your API keys are valid and active');
}

testNewHeaders().catch(console.error);
