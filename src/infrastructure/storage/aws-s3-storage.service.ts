import { Injectable, Logger } from '@nestjs/common';
import { S3Client, PutObjectCommand, DeleteObjectCommand, GetObjectCommand, HeadObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import {
  StorageServiceInterface,
  FileUploadRequest,
  FileUploadResult,
  FileDeleteRequest,
  FileDeleteResult,
  FileTransformOptions,
  FileAccessOptions,
  SecureFileUrl,
} from './interfaces/storage.interface';
import * as path from 'path';
import * as crypto from 'crypto';

@Injectable()
export class AwsS3StorageService implements StorageServiceInterface {
  private readonly logger = new Logger(AwsS3StorageService.name);
  private readonly s3Client: S3Client;
  private readonly bucketName: string;
  private readonly region: string;

  constructor() {
    this.bucketName = process.env.AWS_S3_BUCKET_NAME || '';
    this.region = process.env.AWS_REGION || 'us-east-1';

    this.s3Client = new S3Client({
      region: this.region,
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
      },
    });

    this.validateConfiguration();
  }

  private validateConfiguration(): void {
    if (!process.env.AWS_S3_BUCKET_NAME || 
        !process.env.AWS_ACCESS_KEY_ID || 
        !process.env.AWS_SECRET_ACCESS_KEY) {
      throw new Error('AWS S3 configuration is incomplete. Please set AWS_S3_BUCKET_NAME, AWS_ACCESS_KEY_ID, and AWS_SECRET_ACCESS_KEY');
    }
  }

  async uploadFile(request: FileUploadRequest): Promise<FileUploadResult> {
    try {
      this.logger.log(`Uploading file to S3`, {
        fileName: request.fileName,
        folder: request.folder,
        userId: request.userId,
      });

      const key = this.generateS3Key(request.fileName, request.folder, request.userId);
      const fileBuffer = Buffer.isBuffer(request.file) 
        ? request.file 
        : Buffer.from(request.file, 'base64');

      const uploadCommand = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: key,
        Body: fileBuffer,
        ContentType: request.mimeType,
        Metadata: {
          'original-filename': request.fileName,
          'user-id': request.userId || 'anonymous',
          'uploaded-at': new Date().toISOString(),
          ...this.flattenMetadata(request.metadata || {}),
        },
        TagSet: this.formatTags([
          'banqroll',
          ...(request.tags || []),
          ...(request.userId ? [`user-${request.userId}`] : []),
        ]),
      });

      await this.s3Client.send(uploadCommand);

      const fileUrl = `https://${this.bucketName}.s3.${this.region}.amazonaws.com/${key}`;
      const secureUrl = await this.getSignedUrl(key, 3600); // 1 hour default

      this.logger.log(`File uploaded successfully to S3`, {
        key,
        fileUrl,
        size: fileBuffer.length,
      });

      return {
        success: true,
        fileUrl,
        publicId: key,
        secureUrl,
        format: path.extname(request.fileName).slice(1),
        size: fileBuffer.length,
        metadata: request.metadata,
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown upload error';
      this.logger.error(`File upload to S3 failed`, {
        fileName: request.fileName,
        error: errorMessage,
      });

      return {
        success: false,
        fileUrl: '',
        publicId: '',
        secureUrl: '',
        format: '',
        size: 0,
        errorMessage,
      };
    }
  }

  async uploadMultipleFiles(requests: FileUploadRequest[]): Promise<FileUploadResult[]> {
    const results = await Promise.allSettled(
      requests.map(request => this.uploadFile(request))
    );

    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        this.logger.error(`Failed to upload file ${index} to S3`, {
          error: result.reason,
        });
        return {
          success: false,
          fileUrl: '',
          publicId: '',
          secureUrl: '',
          format: '',
          size: 0,
          errorMessage: result.reason instanceof Error ? result.reason.message : 'Upload failed',
        };
      }
    });
  }

  async getFileUrl(publicId: string, options?: FileAccessOptions): Promise<string> {
    if (options?.secure !== false) {
      return this.getSignedUrl(publicId, options?.expiresIn || 3600);
    }
    return `https://${this.bucketName}.s3.${this.region}.amazonaws.com/${publicId}`;
  }

  async getSecureFileUrl(publicId: string, options?: FileAccessOptions): Promise<SecureFileUrl> {
    const expiresIn = options?.expiresIn || 3600; // Default 1 hour
    const url = await this.getSignedUrl(publicId, expiresIn);
    
    return {
      url,
      expiresAt: new Date(Date.now() + expiresIn * 1000),
      isSecure: true,
    };
  }

  async downloadFile(publicId: string): Promise<Buffer> {
    try {
      const command = new GetObjectCommand({
        Bucket: this.bucketName,
        Key: publicId,
      });

      const response = await this.s3Client.send(command);
      
      if (!response.Body) {
        throw new Error('No file content received');
      }

      const chunks: Uint8Array[] = [];
      const stream = response.Body as any;
      
      for await (const chunk of stream) {
        chunks.push(chunk);
      }

      return Buffer.concat(chunks);
    } catch (error) {
      this.logger.error(`Failed to download file from S3`, {
        publicId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  async transformFile(publicId: string, options: FileTransformOptions): Promise<string> {
    // S3 doesn't have built-in image transformation
    // You would need to integrate with AWS Lambda + Sharp or use CloudFront with Lambda@Edge
    this.logger.warn('Image transformation not supported with S3 storage. Consider using CloudFront with Lambda@Edge or a separate image processing service.');
    return this.getFileUrl(publicId);
  }

  async generateThumbnail(publicId: string, width = 200, height = 200): Promise<string> {
    // S3 doesn't have built-in thumbnail generation
    this.logger.warn('Thumbnail generation not supported with S3 storage. Consider using AWS Lambda with Sharp or a separate image processing service.');
    return this.getFileUrl(publicId);
  }

  async deleteFile(request: FileDeleteRequest): Promise<FileDeleteResult> {
    try {
      this.logger.log(`Deleting file from S3`, {
        publicId: request.publicId,
      });

      const command = new DeleteObjectCommand({
        Bucket: this.bucketName,
        Key: request.publicId,
      });

      await this.s3Client.send(command);

      this.logger.log(`File deleted successfully from S3`, {
        publicId: request.publicId,
      });

      return {
        success: true,
        publicId: request.publicId,
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown deletion error';
      this.logger.error(`File deletion from S3 failed`, {
        publicId: request.publicId,
        error: errorMessage,
      });

      return {
        success: false,
        publicId: request.publicId,
        errorMessage,
      };
    }
  }

  async deleteMultipleFiles(requests: FileDeleteRequest[]): Promise<FileDeleteResult[]> {
    const results = await Promise.allSettled(
      requests.map(request => this.deleteFile(request))
    );

    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        return {
          success: false,
          publicId: requests[index].publicId,
          errorMessage: result.reason instanceof Error ? result.reason.message : 'Deletion failed',
        };
      }
    });
  }

  async getFileMetadata(publicId: string): Promise<Record<string, any>> {
    try {
      const command = new HeadObjectCommand({
        Bucket: this.bucketName,
        Key: publicId,
      });

      const response = await this.s3Client.send(command);
      return response.Metadata || {};
    } catch (error) {
      this.logger.error(`Failed to get file metadata from S3`, {
        publicId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  async updateFileMetadata(publicId: string, metadata: Record<string, any>): Promise<boolean> {
    try {
      // S3 doesn't support updating metadata without copying the object
      // This would require a copy operation which is more complex
      this.logger.warn('Metadata update not implemented for S3. Would require object copy operation.');
      return false;
    } catch (error) {
      this.logger.error(`Failed to update file metadata in S3`, {
        publicId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      return false;
    }
  }

  async validateFile(publicId: string): Promise<boolean> {
    try {
      const command = new HeadObjectCommand({
        Bucket: this.bucketName,
        Key: publicId,
      });

      await this.s3Client.send(command);
      return true;
    } catch (error) {
      return false;
    }
  }

  async getFileInfo(publicId: string): Promise<{
    publicId: string;
    format: string;
    size: number;
    width?: number;
    height?: number;
    createdAt: Date;
    metadata?: Record<string, any>;
  }> {
    try {
      const command = new HeadObjectCommand({
        Bucket: this.bucketName,
        Key: publicId,
      });

      const response = await this.s3Client.send(command);

      return {
        publicId,
        format: path.extname(publicId).slice(1),
        size: response.ContentLength || 0,
        createdAt: response.LastModified || new Date(),
        metadata: response.Metadata,
      };
    } catch (error) {
      this.logger.error(`Failed to get file info from S3`, {
        publicId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  async generateSignedUploadUrl(options: {
    folder?: string;
    userId?: string;
    expiresIn?: number;
    maxFileSize?: number;
    allowedFormats?: string[];
    tags?: string[];
  }): Promise<{
    uploadUrl: string;
    uploadParams: Record<string, any>;
    expiresAt: Date;
  }> {
    const expiresIn = options.expiresIn || 3600; // Default 1 hour
    const key = this.generateS3Key(`upload_${Date.now()}`, options.folder, options.userId);

    const command = new PutObjectCommand({
      Bucket: this.bucketName,
      Key: key,
      Metadata: {
        'user-id': options.userId || 'anonymous',
        'uploaded-at': new Date().toISOString(),
      },
      TagSet: this.formatTags([
        'banqroll',
        ...(options.tags || []),
        ...(options.userId ? [`user-${options.userId}`] : []),
      ]),
    });

    const uploadUrl = await getSignedUrl(this.s3Client, command, {
      expiresIn,
    });

    return {
      uploadUrl,
      uploadParams: {
        key,
        bucket: this.bucketName,
      },
      expiresAt: new Date(Date.now() + expiresIn * 1000),
    };
  }

  async deleteExpiredFiles(): Promise<number> {
    // This would require implementing a cleanup job with S3 lifecycle policies
    this.logger.warn('Expired file cleanup not implemented for S3. Consider using S3 lifecycle policies.');
    return 0;
  }

  async deleteUserFiles(userId: string): Promise<number> {
    // This would require listing and deleting objects with user prefix
    this.logger.warn('User file cleanup not implemented for S3. Would require listing and batch deletion.');
    return 0;
  }

  private async getSignedUrl(key: string, expiresIn: number): Promise<string> {
    const command = new GetObjectCommand({
      Bucket: this.bucketName,
      Key: key,
    });

    return getSignedUrl(this.s3Client, command, { expiresIn });
  }

  private generateS3Key(fileName: string, folder?: string, userId?: string): string {
    const timestamp = Date.now();
    const randomSuffix = crypto.randomBytes(8).toString('hex');
    const cleanFileName = fileName.replace(/[^a-zA-Z0-9.-]/g, '_');
    
    const parts = [];
    if (folder) parts.push(folder);
    if (userId) parts.push(`user_${userId}`);
    parts.push(`${timestamp}_${randomSuffix}_${cleanFileName}`);
    
    return parts.join('/');
  }

  private formatTags(tags: string[]): Array<{ Key: string; Value: string }> {
    return tags.map((tag, index) => ({
      Key: `tag${index}`,
      Value: tag,
    }));
  }

  private flattenMetadata(metadata: Record<string, any>): Record<string, string> {
    const flattened: Record<string, string> = {};
    for (const [key, value] of Object.entries(metadata)) {
      flattened[key] = typeof value === 'string' ? value : JSON.stringify(value);
    }
    return flattened;
  }
}
