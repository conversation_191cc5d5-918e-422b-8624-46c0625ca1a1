import { Injectable, Logger } from '@nestjs/common';
import {
  v2 as cloudinary,
  UploadApiResponse,
  UploadApiErrorResponse,
} from 'cloudinary';
import {
  StorageServiceInterface,
  FileUploadRequest,
  FileUploadResult,
  FileDeleteRequest,
  FileDeleteResult,
  FileTransformOptions,
  FileAccessOptions,
  SecureFileUrl,
} from './interfaces/storage.interface';

@Injectable()
export class CloudinaryStorageService implements StorageServiceInterface {
  private readonly logger = new Logger(CloudinaryStorageService.name);

  constructor() {
    // Configure Cloudinary
    cloudinary.config({
      cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
      api_key: process.env.CLOUDINARY_API_KEY,
      api_secret: process.env.CLOUDINARY_API_SECRET,
      secure: true,
    });

    this.validateConfiguration();
  }

  private validateConfiguration(): void {
    if (
      !process.env.CLOUDINARY_CLOUD_NAME ||
      !process.env.CLOUDINARY_API_KEY ||
      !process.env.CLOUDINARY_API_SECRET
    ) {
      throw new Error(
        'Cloudinary configuration is incomplete. Please set CLOUDINARY_CLOUD_NAME, CLOUDINARY_API_KEY, and CLOUDINARY_API_SECRET',
      );
    }
  }

  async uploadFile(request: FileUploadRequest): Promise<FileUploadResult> {
    try {
      this.logger.log(`Uploading file`, {
        fileName: request.fileName,
        folder: request.folder,
        userId: request.userId,
      });

      const folder = request.folder || 'uploads';
      const publicId = this.generatePublicId(request.fileName, request.userId);

      const uploadOptions = {
        public_id: publicId,
        folder: folder,
        resource_type: 'auto' as const,
        access_mode: 'authenticated' as const, // Secure access by default
        type: 'authenticated' as const,
        tags: [
          'banqroll',
          ...(request.tags || []),
          ...(request.userId ? [`user_${request.userId}`] : []),
        ],
        context: {
          uploaded_at: new Date().toISOString(),
          user_id: request.userId,
          original_filename: request.fileName,
          ...request.metadata,
        },
      };

      let uploadResult: UploadApiResponse;

      if (Buffer.isBuffer(request.file)) {
        // Upload from buffer
        uploadResult = await new Promise((resolve, reject) => {
          cloudinary.uploader
            .upload_stream(
              uploadOptions,
              (
                error: UploadApiErrorResponse | undefined,
                result: UploadApiResponse | undefined,
              ) => {
                if (error) reject(error);
                else if (result) resolve(result);
                else reject(new Error('Upload failed with no result'));
              },
            )
            .end(request.file);
        });
      } else {
        // Upload from base64 string or URL
        uploadResult = await cloudinary.uploader.upload(
          request.file,
          uploadOptions,
        );
      }

      this.logger.log(`File uploaded successfully`, {
        publicId: uploadResult.public_id,
        secureUrl: uploadResult.secure_url,
        size: uploadResult.bytes,
      });

      return {
        success: true,
        fileUrl: uploadResult.url,
        publicId: uploadResult.public_id,
        secureUrl: uploadResult.secure_url,
        format: uploadResult.format,
        size: uploadResult.bytes,
        width: uploadResult.width,
        height: uploadResult.height,
        metadata: uploadResult.context,
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown upload error';
      this.logger.error(`File upload failed`, {
        fileName: request.fileName,
        error: errorMessage,
      });

      return {
        success: false,
        fileUrl: '',
        publicId: '',
        secureUrl: '',
        format: '',
        size: 0,
        errorMessage,
      };
    }
  }

  async uploadMultipleFiles(
    requests: FileUploadRequest[],
  ): Promise<FileUploadResult[]> {
    const results = await Promise.allSettled(
      requests.map((request) => this.uploadFile(request)),
    );

    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        this.logger.error(`Failed to upload file ${index}`, {
          error: result.reason,
        });
        return {
          success: false,
          fileUrl: '',
          publicId: '',
          secureUrl: '',
          format: '',
          size: 0,
          errorMessage:
            result.reason instanceof Error
              ? result.reason.message
              : 'Upload failed',
        };
      }
    });
  }

  async getFileUrl(
    publicId: string,
    options?: FileAccessOptions,
  ): Promise<string> {
    try {
      const transformationOptions: any = {};

      if (options?.transformation) {
        const t = options.transformation;
        if (t.width) transformationOptions.width = t.width;
        if (t.height) transformationOptions.height = t.height;
        if (t.quality) transformationOptions.quality = t.quality;
        if (t.format) transformationOptions.format = t.format;
        if (t.crop) transformationOptions.crop = t.crop;
        if (t.gravity) transformationOptions.gravity = t.gravity;
      }

      const url = cloudinary.url(publicId, {
        secure: options?.secure !== false,
        type: 'authenticated',
        sign_url: true,
        auth_token: options?.expiresIn
          ? {
              duration: options.expiresIn,
            }
          : undefined,
        transformation:
          Object.keys(transformationOptions).length > 0
            ? transformationOptions
            : undefined,
      });

      return url;
    } catch (error) {
      this.logger.error(`Failed to generate file URL`, {
        publicId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  async getSecureFileUrl(
    publicId: string,
    options?: FileAccessOptions,
  ): Promise<SecureFileUrl> {
    const expiresIn = options?.expiresIn || 3600; // Default 1 hour
    const url = await this.getFileUrl(publicId, {
      ...options,
      secure: true,
      expiresIn,
    });

    return {
      url,
      expiresAt: new Date(Date.now() + expiresIn * 1000),
      isSecure: true,
    };
  }

  async downloadFile(publicId: string): Promise<Buffer> {
    try {
      const url = await this.getFileUrl(publicId, { secure: true });

      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to download file: ${response.statusText}`);
      }

      const arrayBuffer = await response.arrayBuffer();
      return Buffer.from(arrayBuffer);
    } catch (error) {
      this.logger.error(`Failed to download file`, {
        publicId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  async transformFile(
    publicId: string,
    options: FileTransformOptions,
  ): Promise<string> {
    return this.getFileUrl(publicId, { transformation: options });
  }

  async generateThumbnail(
    publicId: string,
    width = 200,
    height = 200,
  ): Promise<string> {
    return this.transformFile(publicId, {
      width,
      height,
      crop: 'fill',
      quality: 80,
      format: 'jpg',
    });
  }

  async deleteFile(request: FileDeleteRequest): Promise<FileDeleteResult> {
    try {
      this.logger.log(`Deleting file`, {
        publicId: request.publicId,
      });

      const result = await cloudinary.uploader.destroy(request.publicId, {
        type: 'authenticated',
        invalidate: true,
      });

      const success = result.result === 'ok';

      this.logger.log(`File deletion ${success ? 'successful' : 'failed'}`, {
        publicId: request.publicId,
        result: result.result,
      });

      return {
        success,
        publicId: request.publicId,
        errorMessage: success ? undefined : `Deletion failed: ${result.result}`,
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown deletion error';
      this.logger.error(`File deletion failed`, {
        publicId: request.publicId,
        error: errorMessage,
      });

      return {
        success: false,
        publicId: request.publicId,
        errorMessage,
      };
    }
  }

  async deleteMultipleFiles(
    requests: FileDeleteRequest[],
  ): Promise<FileDeleteResult[]> {
    const results = await Promise.allSettled(
      requests.map((request) => this.deleteFile(request)),
    );

    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        return {
          success: false,
          publicId: requests[index].publicId,
          errorMessage:
            result.reason instanceof Error
              ? result.reason.message
              : 'Deletion failed',
        };
      }
    });
  }

  async getFileMetadata(publicId: string): Promise<Record<string, any>> {
    try {
      const result = await cloudinary.api.resource(publicId, {
        type: 'authenticated',
      });

      return result.context || {};
    } catch (error) {
      this.logger.error(`Failed to get file metadata`, {
        publicId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  async updateFileMetadata(
    publicId: string,
    metadata: Record<string, any>,
  ): Promise<boolean> {
    try {
      await cloudinary.uploader.add_context(metadata, [publicId]);
      return true;
    } catch (error) {
      this.logger.error(`Failed to update file metadata`, {
        publicId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      return false;
    }
  }

  async validateFile(publicId: string): Promise<boolean> {
    try {
      await cloudinary.api.resource(publicId, { type: 'authenticated' });
      return true;
    } catch (error) {
      return false;
    }
  }

  async getFileInfo(publicId: string): Promise<{
    publicId: string;
    format: string;
    size: number;
    width?: number;
    height?: number;
    createdAt: Date;
    metadata?: Record<string, any>;
  }> {
    try {
      const result = await cloudinary.api.resource(publicId, {
        type: 'authenticated',
      });

      return {
        publicId: result.public_id,
        format: result.format,
        size: result.bytes,
        width: result.width,
        height: result.height,
        createdAt: new Date(result.created_at),
        metadata: result.context,
      };
    } catch (error) {
      this.logger.error(`Failed to get file info`, {
        publicId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  async generateSignedUploadUrl(options: {
    folder?: string;
    userId?: string;
    expiresIn?: number;
    maxFileSize?: number;
    allowedFormats?: string[];
    tags?: string[];
  }): Promise<{
    uploadUrl: string;
    uploadParams: Record<string, any>;
    expiresAt: Date;
  }> {
    const timestamp = Math.round(Date.now() / 1000);
    const expiresIn = options.expiresIn || 3600; // Default 1 hour
    const folder = options.folder || 'uploads';

    const uploadParams = {
      timestamp,
      folder,
      access_mode: 'authenticated',
      type: 'authenticated',
      tags: [
        'banqroll',
        ...(options.tags || []),
        ...(options.userId ? [`user_${options.userId}`] : []),
      ].join(','),
      context: [
        `user_id=${options.userId || 'anonymous'}`,
        `uploaded_at=${new Date().toISOString()}`,
      ].join('|'),
    };

    if (options.maxFileSize) {
      uploadParams['bytes_step'] = options.maxFileSize;
    }

    if (options.allowedFormats) {
      uploadParams['allowed_formats'] = options.allowedFormats.join(',');
    }

    const signature = cloudinary.utils.api_sign_request(
      uploadParams,
      process.env.CLOUDINARY_API_SECRET!,
    );

    return {
      uploadUrl: `https://api.cloudinary.com/v1_1/${process.env.CLOUDINARY_CLOUD_NAME}/auto/upload`,
      uploadParams: {
        ...uploadParams,
        signature,
        api_key: process.env.CLOUDINARY_API_KEY,
      },
      expiresAt: new Date(Date.now() + expiresIn * 1000),
    };
  }

  async deleteExpiredFiles(): Promise<number> {
    // This would require implementing a cleanup job
    // For now, return 0 as placeholder
    return 0;
  }

  async deleteUserFiles(userId: string): Promise<number> {
    try {
      const result = await cloudinary.api.delete_resources_by_tag(
        `user_${userId}`,
        {
          type: 'authenticated',
        },
      );

      return Object.keys(result.deleted).length;
    } catch (error) {
      this.logger.error(`Failed to delete user files`, {
        userId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      return 0;
    }
  }

  private generatePublicId(fileName: string, userId?: string): string {
    const timestamp = Date.now();
    const cleanFileName = fileName.replace(/[^a-zA-Z0-9.-]/g, '_');
    const userPrefix = userId ? `user_${userId}_` : '';
    return `${userPrefix}${timestamp}_${cleanFileName}`;
  }
}
