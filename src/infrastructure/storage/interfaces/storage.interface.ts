export interface FileUploadRequest {
  file: Buffer | string; // File buffer or base64 string
  fileName: string;
  mimeType: string;
  folder?: string; // Optional folder path
  userId?: string; // Optional user ID for organization
  metadata?: Record<string, any>;
  tags?: string[]; // Optional tags for categorization
}

export interface FileUploadResult {
  success: boolean;
  fileUrl: string;
  publicId: string;
  secureUrl: string;
  format: string;
  size: number;
  width?: number;
  height?: number;
  metadata?: Record<string, any>;
  errorMessage?: string;
}

export interface FileDeleteRequest {
  publicId: string;
  fileUrl?: string;
}

export interface FileDeleteResult {
  success: boolean;
  publicId: string;
  errorMessage?: string;
}

export interface FileTransformOptions {
  width?: number;
  height?: number;
  quality?: number;
  format?: 'jpg' | 'png' | 'pdf' | 'webp';
  crop?: 'fill' | 'fit' | 'scale' | 'crop';
  gravity?: 'auto' | 'center' | 'face' | 'faces';
}

export interface FileAccessOptions {
  expiresIn?: number; // seconds
  secure?: boolean;
  transformation?: FileTransformOptions;
}

export interface SecureFileUrl {
  url: string;
  expiresAt: Date;
  isSecure: boolean;
}

export interface StorageServiceInterface {
  uploadFile(request: FileUploadRequest): Promise<FileUploadResult>;
  uploadMultipleFiles(requests: FileUploadRequest[]): Promise<FileUploadResult[]>;
  getFileUrl(publicId: string, options?: FileAccessOptions): Promise<string>;
  getSecureFileUrl(publicId: string, options?: FileAccessOptions): Promise<SecureFileUrl>;
  downloadFile(publicId: string): Promise<Buffer>;
  transformFile(publicId: string, options: FileTransformOptions): Promise<string>;
  generateThumbnail(publicId: string, width?: number, height?: number): Promise<string>;
  deleteFile(request: FileDeleteRequest): Promise<FileDeleteResult>;
  deleteMultipleFiles(requests: FileDeleteRequest[]): Promise<FileDeleteResult[]>;
  getFileMetadata(publicId: string): Promise<Record<string, any>>;
  updateFileMetadata(publicId: string, metadata: Record<string, any>): Promise<boolean>;
  validateFile(publicId: string): Promise<boolean>;
  getFileInfo(publicId: string): Promise<{
    publicId: string;
    format: string;
    size: number;
    width?: number;
    height?: number;
    createdAt: Date;
    metadata?: Record<string, any>;
  }>;
  generateSignedUploadUrl(options: {
    folder?: string;
    userId?: string;
    expiresIn?: number;
    maxFileSize?: number;
    allowedFormats?: string[];
    tags?: string[];
  }): Promise<{
    uploadUrl: string;
    uploadParams: Record<string, any>;
    expiresAt: Date;
  }>;
  deleteExpiredFiles(): Promise<number>;
  deleteUserFiles(userId: string): Promise<number>;
}
