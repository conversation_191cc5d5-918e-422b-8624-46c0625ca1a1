import { Injectable, Logger } from '@nestjs/common';
import {
  StorageServiceInterface,
  FileUploadRequest,
  FileUploadResult,
  FileDeleteRequest,
  FileDeleteResult,
  FileTransformOptions,
  FileAccessOptions,
  SecureFileUrl,
} from './interfaces/storage.interface';
import * as fs from 'fs/promises';
import * as path from 'path';
import * as crypto from 'crypto';
import { existsSync } from 'fs';

@Injectable()
export class LocalStorageService implements StorageServiceInterface {
  private readonly logger = new Logger(LocalStorageService.name);
  private readonly baseStoragePath: string;
  private readonly baseUrl: string;

  constructor() {
    this.baseStoragePath = process.env.LOCAL_STORAGE_PATH || './storage';
    this.baseUrl = process.env.LOCAL_STORAGE_BASE_URL || 'http://localhost:3000/files';
    
    this.initializeStorage();
  }

  private async initializeStorage(): Promise<void> {
    try {
      if (!existsSync(this.baseStoragePath)) {
        await fs.mkdir(this.baseStoragePath, { recursive: true });
        this.logger.log(`Created storage directory: ${this.baseStoragePath}`);
      }
    } catch (error) {
      this.logger.error(`Failed to initialize storage directory`, {
        path: this.baseStoragePath,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  async uploadFile(request: FileUploadRequest): Promise<FileUploadResult> {
    try {
      this.logger.log(`Uploading file to local storage`, {
        fileName: request.fileName,
        folder: request.folder,
        userId: request.userId,
      });

      const relativePath = this.generateFilePath(request.fileName, request.folder, request.userId);
      const fullPath = path.join(this.baseStoragePath, relativePath);
      const directory = path.dirname(fullPath);

      // Ensure directory exists
      await fs.mkdir(directory, { recursive: true });

      // Write file
      const fileBuffer = Buffer.isBuffer(request.file) 
        ? request.file 
        : Buffer.from(request.file, 'base64');

      await fs.writeFile(fullPath, fileBuffer);

      // Create metadata file
      const metadataPath = `${fullPath}.meta.json`;
      const metadata = {
        originalFileName: request.fileName,
        mimeType: request.mimeType,
        userId: request.userId,
        folder: request.folder,
        tags: request.tags || [],
        uploadedAt: new Date().toISOString(),
        size: fileBuffer.length,
        ...request.metadata,
      };

      await fs.writeFile(metadataPath, JSON.stringify(metadata, null, 2));

      const fileUrl = `${this.baseUrl}/${relativePath}`;
      const secureUrl = this.generateSecureUrl(relativePath, 3600); // 1 hour default

      this.logger.log(`File uploaded successfully to local storage`, {
        relativePath,
        fileUrl,
        size: fileBuffer.length,
      });

      return {
        success: true,
        fileUrl,
        publicId: relativePath,
        secureUrl,
        format: path.extname(request.fileName).slice(1),
        size: fileBuffer.length,
        metadata,
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown upload error';
      this.logger.error(`File upload to local storage failed`, {
        fileName: request.fileName,
        error: errorMessage,
      });

      return {
        success: false,
        fileUrl: '',
        publicId: '',
        secureUrl: '',
        format: '',
        size: 0,
        errorMessage,
      };
    }
  }

  async uploadMultipleFiles(requests: FileUploadRequest[]): Promise<FileUploadResult[]> {
    const results = await Promise.allSettled(
      requests.map(request => this.uploadFile(request))
    );

    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        this.logger.error(`Failed to upload file ${index} to local storage`, {
          error: result.reason,
        });
        return {
          success: false,
          fileUrl: '',
          publicId: '',
          secureUrl: '',
          format: '',
          size: 0,
          errorMessage: result.reason instanceof Error ? result.reason.message : 'Upload failed',
        };
      }
    });
  }

  async getFileUrl(publicId: string, options?: FileAccessOptions): Promise<string> {
    if (options?.secure !== false) {
      return this.generateSecureUrl(publicId, options?.expiresIn || 3600);
    }
    return `${this.baseUrl}/${publicId}`;
  }

  async getSecureFileUrl(publicId: string, options?: FileAccessOptions): Promise<SecureFileUrl> {
    const expiresIn = options?.expiresIn || 3600; // Default 1 hour
    const url = this.generateSecureUrl(publicId, expiresIn);
    
    return {
      url,
      expiresAt: new Date(Date.now() + expiresIn * 1000),
      isSecure: true,
    };
  }

  async downloadFile(publicId: string): Promise<Buffer> {
    try {
      const fullPath = path.join(this.baseStoragePath, publicId);
      return await fs.readFile(fullPath);
    } catch (error) {
      this.logger.error(`Failed to download file from local storage`, {
        publicId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  async transformFile(publicId: string, options: FileTransformOptions): Promise<string> {
    // Local storage doesn't have built-in image transformation
    // You would need to integrate with Sharp or similar library
    this.logger.warn('Image transformation not supported with local storage. Consider integrating Sharp for image processing.');
    return this.getFileUrl(publicId);
  }

  async generateThumbnail(publicId: string, width = 200, height = 200): Promise<string> {
    // Local storage doesn't have built-in thumbnail generation
    this.logger.warn('Thumbnail generation not supported with local storage. Consider integrating Sharp for image processing.');
    return this.getFileUrl(publicId);
  }

  async deleteFile(request: FileDeleteRequest): Promise<FileDeleteResult> {
    try {
      this.logger.log(`Deleting file from local storage`, {
        publicId: request.publicId,
      });

      const fullPath = path.join(this.baseStoragePath, request.publicId);
      const metadataPath = `${fullPath}.meta.json`;

      // Delete main file
      await fs.unlink(fullPath);

      // Delete metadata file if it exists
      try {
        await fs.unlink(metadataPath);
      } catch (error) {
        // Metadata file might not exist, ignore error
      }

      this.logger.log(`File deleted successfully from local storage`, {
        publicId: request.publicId,
      });

      return {
        success: true,
        publicId: request.publicId,
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown deletion error';
      this.logger.error(`File deletion from local storage failed`, {
        publicId: request.publicId,
        error: errorMessage,
      });

      return {
        success: false,
        publicId: request.publicId,
        errorMessage,
      };
    }
  }

  async deleteMultipleFiles(requests: FileDeleteRequest[]): Promise<FileDeleteResult[]> {
    const results = await Promise.allSettled(
      requests.map(request => this.deleteFile(request))
    );

    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        return {
          success: false,
          publicId: requests[index].publicId,
          errorMessage: result.reason instanceof Error ? result.reason.message : 'Deletion failed',
        };
      }
    });
  }

  async getFileMetadata(publicId: string): Promise<Record<string, any>> {
    try {
      const fullPath = path.join(this.baseStoragePath, publicId);
      const metadataPath = `${fullPath}.meta.json`;
      
      const metadataContent = await fs.readFile(metadataPath, 'utf-8');
      return JSON.parse(metadataContent);
    } catch (error) {
      this.logger.error(`Failed to get file metadata from local storage`, {
        publicId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      return {};
    }
  }

  async updateFileMetadata(publicId: string, metadata: Record<string, any>): Promise<boolean> {
    try {
      const fullPath = path.join(this.baseStoragePath, publicId);
      const metadataPath = `${fullPath}.meta.json`;
      
      const existingMetadata = await this.getFileMetadata(publicId);
      const updatedMetadata = { ...existingMetadata, ...metadata };
      
      await fs.writeFile(metadataPath, JSON.stringify(updatedMetadata, null, 2));
      return true;
    } catch (error) {
      this.logger.error(`Failed to update file metadata in local storage`, {
        publicId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      return false;
    }
  }

  async validateFile(publicId: string): Promise<boolean> {
    try {
      const fullPath = path.join(this.baseStoragePath, publicId);
      await fs.access(fullPath);
      return true;
    } catch (error) {
      return false;
    }
  }

  async getFileInfo(publicId: string): Promise<{
    publicId: string;
    format: string;
    size: number;
    width?: number;
    height?: number;
    createdAt: Date;
    metadata?: Record<string, any>;
  }> {
    try {
      const fullPath = path.join(this.baseStoragePath, publicId);
      const stats = await fs.stat(fullPath);
      const metadata = await this.getFileMetadata(publicId);

      return {
        publicId,
        format: path.extname(publicId).slice(1),
        size: stats.size,
        createdAt: stats.birthtime,
        metadata,
      };
    } catch (error) {
      this.logger.error(`Failed to get file info from local storage`, {
        publicId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  async generateSignedUploadUrl(options: {
    folder?: string;
    userId?: string;
    expiresIn?: number;
    maxFileSize?: number;
    allowedFormats?: string[];
    tags?: string[];
  }): Promise<{
    uploadUrl: string;
    uploadParams: Record<string, any>;
    expiresAt: Date;
  }> {
    const expiresIn = options.expiresIn || 3600; // Default 1 hour
    const uploadToken = crypto.randomBytes(32).toString('hex');
    
    // In a real implementation, you'd store this token temporarily
    // and validate it when the upload happens
    
    return {
      uploadUrl: `${this.baseUrl}/upload`,
      uploadParams: {
        token: uploadToken,
        folder: options.folder,
        userId: options.userId,
        maxFileSize: options.maxFileSize,
        allowedFormats: options.allowedFormats,
        tags: options.tags,
      },
      expiresAt: new Date(Date.now() + expiresIn * 1000),
    };
  }

  async deleteExpiredFiles(): Promise<number> {
    // This would require implementing a cleanup job
    this.logger.warn('Expired file cleanup not implemented for local storage.');
    return 0;
  }

  async deleteUserFiles(userId: string): Promise<number> {
    try {
      // This is a simplified implementation
      // In production, you'd want to be more careful about recursive deletion
      let deletedCount = 0;
      
      const userFolders = [
        path.join(this.baseStoragePath, 'user-profiles', `user_${userId}`),
        path.join(this.baseStoragePath, 'kyc', '*', `user_${userId}`),
        // Add more user-specific folders as needed
      ];

      for (const folderPattern of userFolders) {
        try {
          // This is a simplified approach - in production you'd use glob patterns
          if (existsSync(folderPattern)) {
            await fs.rm(folderPattern, { recursive: true });
            deletedCount++;
          }
        } catch (error) {
          this.logger.warn(`Failed to delete user folder: ${folderPattern}`, {
            error: error instanceof Error ? error.message : 'Unknown error',
          });
        }
      }

      return deletedCount;
    } catch (error) {
      this.logger.error(`Failed to delete user files from local storage`, {
        userId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      return 0;
    }
  }

  private generateFilePath(fileName: string, folder?: string, userId?: string): string {
    const timestamp = Date.now();
    const randomSuffix = crypto.randomBytes(8).toString('hex');
    const cleanFileName = fileName.replace(/[^a-zA-Z0-9.-]/g, '_');
    
    const parts = [];
    if (folder) parts.push(folder);
    if (userId) parts.push(`user_${userId}`);
    parts.push(`${timestamp}_${randomSuffix}_${cleanFileName}`);
    
    return parts.join('/');
  }

  private generateSecureUrl(relativePath: string, expiresIn: number): string {
    const expiresAt = Math.floor(Date.now() / 1000) + expiresIn;
    const signature = crypto
      .createHmac('sha256', process.env.LOCAL_STORAGE_SECRET || 'default-secret')
      .update(`${relativePath}:${expiresAt}`)
      .digest('hex');
    
    return `${this.baseUrl}/${relativePath}?expires=${expiresAt}&signature=${signature}`;
  }
}
