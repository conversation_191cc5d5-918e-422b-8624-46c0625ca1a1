import { Injectable, Logger } from '@nestjs/common';

export enum StorageProvider {
  CLOUDINARY = 'cloudinary',
  AWS_S3 = 'aws',
  LOCAL = 'local',
}

export interface StorageConfig {
  provider: StorageProvider;
  cloudinary?: {
    cloudName: string;
    apiKey: string;
    apiSecret: string;
  };
  aws?: {
    region: string;
    bucketName: string;
    accessKeyId: string;
    secretAccessKey: string;
  };
  local?: {
    storagePath: string;
    baseUrl: string;
    secret: string;
  };
}

@Injectable()
export class StorageConfigService {
  private readonly logger = new Logger(StorageConfigService.name);

  getStorageConfig(): StorageConfig {
    const provider = this.getStorageProvider();
    
    const config: StorageConfig = {
      provider,
    };

    switch (provider) {
      case StorageProvider.CLOUDINARY:
        config.cloudinary = this.getCloudinaryConfig();
        break;
      case StorageProvider.AWS_S3:
        config.aws = this.getAwsConfig();
        break;
      case StorageProvider.LOCAL:
        config.local = this.getLocalConfig();
        break;
    }

    this.validateConfig(config);
    return config;
  }

  private getStorageProvider(): StorageProvider {
    const provider = process.env.STORAGE_PROVIDER?.toLowerCase() || 'local';
    
    switch (provider) {
      case 'cloudinary':
        return StorageProvider.CLOUDINARY;
      case 'aws':
      case 's3':
        return StorageProvider.AWS_S3;
      case 'local':
      default:
        return StorageProvider.LOCAL;
    }
  }

  private getCloudinaryConfig() {
    return {
      cloudName: process.env.CLOUDINARY_CLOUD_NAME || '',
      apiKey: process.env.CLOUDINARY_API_KEY || '',
      apiSecret: process.env.CLOUDINARY_API_SECRET || '',
    };
  }

  private getAwsConfig() {
    return {
      region: process.env.AWS_REGION || 'us-east-1',
      bucketName: process.env.AWS_S3_BUCKET_NAME || '',
      accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
    };
  }

  private getLocalConfig() {
    return {
      storagePath: process.env.LOCAL_STORAGE_PATH || './storage',
      baseUrl: process.env.LOCAL_STORAGE_BASE_URL || 'http://localhost:3000/files',
      secret: process.env.LOCAL_STORAGE_SECRET || 'default-secret',
    };
  }

  private validateConfig(config: StorageConfig): void {
    switch (config.provider) {
      case StorageProvider.CLOUDINARY:
        if (!config.cloudinary?.cloudName || !config.cloudinary?.apiKey || !config.cloudinary?.apiSecret) {
          throw new Error('Cloudinary configuration is incomplete. Please set CLOUDINARY_CLOUD_NAME, CLOUDINARY_API_KEY, and CLOUDINARY_API_SECRET');
        }
        break;
      
      case StorageProvider.AWS_S3:
        if (!config.aws?.bucketName || !config.aws?.accessKeyId || !config.aws?.secretAccessKey) {
          throw new Error('AWS S3 configuration is incomplete. Please set AWS_S3_BUCKET_NAME, AWS_ACCESS_KEY_ID, and AWS_SECRET_ACCESS_KEY');
        }
        break;
      
      case StorageProvider.LOCAL:
        if (!config.local?.storagePath) {
          throw new Error('Local storage configuration is incomplete. Please set LOCAL_STORAGE_PATH');
        }
        break;
    }

    this.logger.log(`Storage provider configured: ${config.provider}`);
  }

  getProviderCapabilities(provider: StorageProvider): {
    supportsTransformation: boolean;
    supportsThumbnails: boolean;
    supportsMetadataUpdate: boolean;
    supportsSignedUrls: boolean;
    supportsTagging: boolean;
  } {
    switch (provider) {
      case StorageProvider.CLOUDINARY:
        return {
          supportsTransformation: true,
          supportsThumbnails: true,
          supportsMetadataUpdate: true,
          supportsSignedUrls: true,
          supportsTagging: true,
        };
      
      case StorageProvider.AWS_S3:
        return {
          supportsTransformation: false, // Would need Lambda + Sharp
          supportsThumbnails: false,     // Would need Lambda + Sharp
          supportsMetadataUpdate: false, // Requires object copy
          supportsSignedUrls: true,
          supportsTagging: true,
        };
      
      case StorageProvider.LOCAL:
        return {
          supportsTransformation: false, // Would need Sharp integration
          supportsThumbnails: false,     // Would need Sharp integration
          supportsMetadataUpdate: true,
          supportsSignedUrls: true,
          supportsTagging: false,
        };
      
      default:
        return {
          supportsTransformation: false,
          supportsThumbnails: false,
          supportsMetadataUpdate: false,
          supportsSignedUrls: false,
          supportsTagging: false,
        };
    }
  }

  getCurrentProviderCapabilities() {
    const config = this.getStorageConfig();
    return this.getProviderCapabilities(config.provider);
  }

  isFeatureSupported(feature: keyof ReturnType<typeof this.getProviderCapabilities>): boolean {
    const capabilities = this.getCurrentProviderCapabilities();
    return capabilities[feature];
  }

  getRecommendedProvider(requirements: {
    needsTransformation?: boolean;
    needsThumbnails?: boolean;
    needsMetadataUpdate?: boolean;
    needsSignedUrls?: boolean;
    needsTagging?: boolean;
    budget?: 'low' | 'medium' | 'high';
    scale?: 'small' | 'medium' | 'large';
  }): StorageProvider {
    // Simple recommendation logic
    if (requirements.needsTransformation || requirements.needsThumbnails) {
      return StorageProvider.CLOUDINARY;
    }
    
    if (requirements.scale === 'large' || requirements.budget === 'medium') {
      return StorageProvider.AWS_S3;
    }
    
    if (requirements.budget === 'low' || requirements.scale === 'small') {
      return StorageProvider.LOCAL;
    }
    
    return StorageProvider.CLOUDINARY; // Default recommendation
  }
}
