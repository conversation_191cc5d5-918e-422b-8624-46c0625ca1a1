import { Module, Global } from '@nestjs/common';
import { CloudinaryStorageService } from './cloudinary-storage.service';
import { AwsS3StorageService } from './aws-s3-storage.service';
import { LocalStorageService } from './local-storage.service';
import { StorageConfigService } from './storage-config.service';
import { StorageServiceInterface } from './interfaces/storage.interface';

export const STORAGE_SERVICE = Symbol('STORAGE_SERVICE');

// Storage provider factory
function createStorageProvider(): StorageServiceInterface {
  const provider = process.env.STORAGE_PROVIDER || 'local';

  switch (provider.toLowerCase()) {
    case 'cloudinary':
      return new CloudinaryStorageService();
    case 'aws':
    case 's3':
      return new AwsS3StorageService();
    case 'local':
    default:
      return new LocalStorageService();
  }
}

@Global()
@Module({
  providers: [
    CloudinaryStorageService,
    AwsS3StorageService,
    LocalStorageService,
    StorageConfigService,
    {
      provide: STORAGE_SERVICE,
      useFactory: createStorageProvider,
    },
  ],
  exports: [
    STORAGE_SERVICE,
    CloudinaryStorageService,
    AwsS3StorageService,
    LocalStorageService,
    StorageConfigService
  ],
})
export class StorageModule {}
