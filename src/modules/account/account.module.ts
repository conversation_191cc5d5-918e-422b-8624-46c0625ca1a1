import { Module } from '@nestjs/common';
import { AccountRepositoryModule } from '../../infrastructure/database/repositories/account/account-repository.module';
import { AccountController } from './controllers/account.controller';
import { AccountService } from './services/account.service';

// Use Cases
import { CreateAccountUseCase } from '../../core/use-cases/account/create-account.use-case';
import { GetAccountsUseCase } from '../../core/use-cases/account/get-accounts.use-case';
import { GetAccountByIdUseCase } from '../../core/use-cases/account/get-account-by-id.use-case';
import { UpdateAccountUseCase } from '../../core/use-cases/account/update-account.use-case';
import { DeleteAccountUseCase } from '../../core/use-cases/account/delete-account.use-case';
import { SharedModule } from '../../shared/shared.module';

@Module({
  imports: [AccountRepositoryModule, SharedModule],
  controllers: [AccountController],
  providers: [
    // Services
    AccountService,

    // Use Cases
    CreateAccountUseCase,
    GetAccountsUseCase,
    GetAccountByIdUseCase,
    UpdateAccountUseCase,
    DeleteAccountUseCase,
  ],
  exports: [AccountService],
})
export class AccountModule {}
