import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
} from '@nestjs/swagger';
import { AccountService } from '../services/account.service';
import { CreateAccountDto, UpdateAccountDto } from '../dtos';
import { JwtAuthGuard, GetUser } from '../../../shared';
import { AuditInterceptor } from '../../../shared/interceptors/audit.interceptor';
import {
  AuditCreate,
  AuditUpdate,
  AuditDelete,
  AuditRead
} from '../../../shared/decorators/audit.decorator';
import { AuditEntity } from '../../../core/constants/audit-actions';

@ApiTags('Accounts')
@ApiBearerAuth()
@Controller('accounts')
@UseGuards(JwtAuthGuard)
@UseInterceptors(AuditInterceptor)
export class AccountController {
  constructor(private readonly accountService: AccountService) {}

  @Post()
  @AuditCreate(AuditEntity.ACCOUNT, 'id', {
    captureResponse: true,
    description: 'Account created via API',
    accountIdParam: 'id',
  })
  @ApiOperation({ summary: 'Create a new account' })
  @ApiResponse({ status: 201, description: 'Account created successfully' })
  async create(@Body() dto: CreateAccountDto, @GetUser('id') userId: string) {
    return this.accountService.create(dto, userId);
  }

  @Get()
  @ApiOperation({ summary: 'Get all accounts for the current user' })
  @ApiResponse({ status: 200, description: 'Accounts retrieved successfully' })
  async findAll(@GetUser('id') userId: string) {
    return this.accountService.findAll(userId);
  }

  @Get(':id')
  @AuditRead(AuditEntity.ACCOUNT, 'id', {
    description: 'Account viewed via API',
    accountIdParam: 'id',
  })
  @ApiOperation({ summary: 'Get account by ID' })
  @ApiResponse({ status: 200, description: 'Account retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Account not found' })
  async findById(@Param('id') id: string) {
    const account = await this.accountService.findById(id);
    if (!account) {
      throw new Error('Account not found');
    }
    return account;
  }

  @Put(':id')
  @AuditUpdate(AuditEntity.ACCOUNT, 'id', {
    captureRequest: true,
    captureResponse: true,
    description: 'Account updated via API',
    accountIdParam: 'id',
  })
  @ApiOperation({ summary: 'Update account' })
  @ApiResponse({ status: 200, description: 'Account updated successfully' })
  @ApiResponse({ status: 404, description: 'Account not found' })
  async update(@Param('id') id: string, @Body() dto: UpdateAccountDto, @GetUser('id') userId: string) {
    return this.accountService.update(id, dto, userId);
  }

  @Delete(':id')
  @AuditDelete(AuditEntity.ACCOUNT, 'id', {
    captureRequest: true,
    description: 'Account deleted via API',
    accountIdParam: 'id',
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete account' })
  @ApiResponse({ status: 204, description: 'Account deleted successfully' })
  @ApiResponse({ status: 404, description: 'Account not found' })
  async delete(@Param('id') id: string) {
    return this.accountService.delete(id);
  }
}
