import { Injectable } from '@nestjs/common';
import { CreateAccountUseCase } from '../../../core/use-cases/account/create-account.use-case';
import { GetAccountsUseCase } from '../../../core/use-cases/account/get-accounts.use-case';
import { GetAccountByIdUseCase } from '../../../core/use-cases/account/get-account-by-id.use-case';
import { UpdateAccountUseCase } from '../../../core/use-cases/account/update-account.use-case';
import { DeleteAccountUseCase } from '../../../core/use-cases/account/delete-account.use-case';
import { CreateAccountDto, UpdateAccountDto } from '../dtos';
import { Account } from '../../../core/entities/account.entity';

@Injectable()
export class AccountService {
  constructor(
    private readonly createAccountUseCase: CreateAccountUseCase,
    private readonly getAccountsUseCase: GetAccountsUseCase,
    private readonly getAccountByIdUseCase: GetAccountByIdUseCase,
    private readonly updateAccountUseCase: UpdateAccountUseCase,
    private readonly deleteAccountUseCase: DeleteAccountUseCase,
  ) {}

  async create(dto: CreateAccountDto, userId: string): Promise<Account> {
    return this.createAccountUseCase.execute({
      userId,
      name: dto.name,
    });
  }

  async findAll(userId: string): Promise<Account[]> {
    return this.getAccountsUseCase.execute(userId);
  }

  async findById(id: string): Promise<Account | null> {
    return this.getAccountByIdUseCase.execute(id);
  }

  async update(id: string, dto: UpdateAccountDto, userId: string): Promise<Account> {
    return this.updateAccountUseCase.execute({
      id,
      name: dto.name,
      userId, // Add userId for audit logging
    });
  }

  async delete(id: string): Promise<void> {
    return this.deleteAccountUseCase.execute(id);
  }
}
