import {
  Body,
  Controller,
  Post,
  HttpCode,
  HttpStatus,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { Throttle } from '@nestjs/throttler';
import { AdminService } from '../services/admin.service';
import { AdminLoginDto } from '../dtos/admin-login.dto';
import { AdminLoginResponseDto } from '../dtos/admin-response.dto';
import { AdminAuthGuard } from '../../../shared/guards/admin-auth.guard';
import { GetAdminUser } from '../../../shared/decorators/get-admin-user.decorator';

@ApiTags('Admin Authentication')
@Controller('admin/auth')
export class AdminAuthController {
  constructor(private readonly adminService: AdminService) {}

  @Post('login')
  @HttpCode(HttpStatus.OK)
  @Throttle({ default: { limit: 5, ttl: 300000 } }) // 5 admin login attempts per 5 minutes
  @ApiOperation({
    summary: 'Admin login',
    description: 'Authenticate admin user and return access tokens',
  })
  @ApiResponse({
    status: 200,
    description: 'Admin login successful',
    type: AdminLoginResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Invalid credentials' })
  @ApiResponse({ status: 403, description: 'Admin account is not active' })
  async login(@Body() dto: AdminLoginDto): Promise<AdminLoginResponseDto> {
    return this.adminService.login(dto);
  }

  @Post('logout')
  @HttpCode(HttpStatus.NO_CONTENT)
  @UseGuards(AdminAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ 
    summary: 'Admin logout',
    description: 'Logout admin user and invalidate refresh token',
  })
  @ApiResponse({ status: 204, description: 'Admin logout successful' })
  @ApiResponse({ status: 401, description: 'Invalid admin token' })
  async logout(@GetAdminUser('id') adminUserId: string): Promise<void> {
    return this.adminService.logout(adminUserId);
  }
}
