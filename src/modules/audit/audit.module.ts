import { Modu<PERSON> } from '@nestjs/common';
import { AuditController } from './controllers/audit.controller';
import { GetAuditLogsUseCase } from '../../core/use-cases/audit/get-audit-logs.use-case';
import { CheckAuditAccessUseCase } from '../../core/use-cases/audit/check-audit-access.use-case';
import { AuditService } from '../../core/services/audit.service';
import { AuditRepositoryModule } from '../../infrastructure/database/repositories/audit-log/audit-log-repository.module';
import { UserSharedAccessRepositoryModule } from '../../infrastructure/database/repositories/user-shared-access/user-shared-access-repository.module';
import { AccountRepositoryModule } from '../../infrastructure/database/repositories/account/account-repository.module';
import { WalletRepositoryModule } from '../../infrastructure/database/repositories/wallet/wallet-repository.module';
import { SharedModule } from '../../shared/shared.module';

@Module({
  imports: [
    AuditRepositoryModule,
    UserSharedAccessRepositoryModule,
    AccountRepositoryModule,
    WalletRepositoryModule,
    SharedModule,
  ],
  controllers: [AuditController],
  providers: [
    GetAuditLogsUseCase,
    CheckAuditAccessUseCase,
    AuditService,
  ],
  exports: [
    AuditService,
    GetAuditLogsUseCase,
    CheckAuditAccessUseCase,
  ],
})
export class AuditModule {}
