import {
  Controller,
  Get,
  Query,
  Param,
  UseGuards,
  Request,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../../shared/guards/jwt-auth.guard';
import { PermissionGuard } from '../../../shared/guards/permission.guard';
import { RequirePermission } from '../../../shared/decorators/require-permission.decorator';
import { GetAuditLogsUseCase } from '../../../core/use-cases/audit/get-audit-logs.use-case';
import {
  GetAuditLogsQueryDto,
  GetAccountAuditLogsParamsDto,
  GetWalletAuditLogsParamsDto,
  GetEntityAuditLogsParamsDto,
  GetUserAuditLogsParamsDto,
} from '../dto/get-audit-logs.dto';
import {
  AuditLogListResponseDto,
  AuditStatisticsResponseDto,
} from '../dto/audit-log-response.dto';
import { AuditMapper } from '../mappers/audit.mapper';

@ApiTags('Audit Logs')
@ApiBearerAuth()
@Controller('audit')
@UseGuards(JwtAuthGuard, PermissionGuard)
export class AuditController {
  constructor(
    private readonly getAuditLogsUseCase: GetAuditLogsUseCase,
  ) {}

  @Get()
  @RequirePermission('view_audit_logs')
  @ApiOperation({
    summary: 'Get audit logs',
    description: 'Retrieve audit logs with filtering and pagination. Users can only see logs for resources they have access to.',
  })
  @ApiResponse({
    status: 200,
    description: 'Audit logs retrieved successfully',
    type: AuditLogListResponseDto,
  })
  @ApiResponse({
    status: 403,
    description: 'Insufficient permissions to view audit logs',
  })
  async getAuditLogs(
    @Request() req: any,
    @Query() query: GetAuditLogsQueryDto,
  ): Promise<AuditLogListResponseDto> {
    const result = await this.getAuditLogsUseCase.execute({
      userId: req.user.id,
      filters: {
        accountId: query.accountId,
        walletId: query.walletId,
        entity: query.entity,
        action: query.action,
        entityId: query.entityId,
        targetUserId: query.targetUserId,
        startDate: query.startDate,
        endDate: query.endDate,
      },
      pagination: {
        page: query.page,
        limit: query.limit,
        sortBy: query.sortBy,
        sortOrder: query.sortOrder,
      },
    });

    return AuditMapper.toListResponseDto(result);
  }

  @Get('accounts/:accountId')
  @RequirePermission('view_audit_logs')
  @ApiOperation({
    summary: 'Get audit logs for a specific account',
    description: 'Retrieve audit logs for a specific account. User must have access to the account.',
  })
  @ApiParam({
    name: 'accountId',
    description: 'Account ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Account audit logs retrieved successfully',
    type: AuditLogListResponseDto,
  })
  @ApiResponse({
    status: 403,
    description: 'No access to audit logs for this account',
  })
  async getAccountAuditLogs(
    @Request() req: any,
    @Param() params: GetAccountAuditLogsParamsDto,
    @Query() query: Omit<GetAuditLogsQueryDto, 'accountId'>,
  ): Promise<AuditLogListResponseDto> {
    const result = await this.getAuditLogsUseCase.getAccountAuditLogs(
      req.user.id,
      params.accountId,
      {
        walletId: query.walletId,
        entity: query.entity,
        action: query.action,
        entityId: query.entityId,
        targetUserId: query.targetUserId,
        startDate: query.startDate,
        endDate: query.endDate,
      },
      {
        page: query.page,
        limit: query.limit,
        sortBy: query.sortBy,
        sortOrder: query.sortOrder,
      },
    );

    return AuditMapper.toListResponseDto(result);
  }

  @Get('wallets/:walletId')
  @RequirePermission('view_audit_logs')
  @ApiOperation({
    summary: 'Get audit logs for a specific wallet',
    description: 'Retrieve audit logs for a specific wallet. User must have access to the wallet.',
  })
  @ApiParam({
    name: 'walletId',
    description: 'Wallet ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Wallet audit logs retrieved successfully',
    type: AuditLogListResponseDto,
  })
  @ApiResponse({
    status: 403,
    description: 'No access to audit logs for this wallet',
  })
  async getWalletAuditLogs(
    @Request() req: any,
    @Param() params: GetWalletAuditLogsParamsDto,
    @Query() query: Omit<GetAuditLogsQueryDto, 'walletId'>,
  ): Promise<AuditLogListResponseDto> {
    const result = await this.getAuditLogsUseCase.getWalletAuditLogs(
      req.user.id,
      params.walletId,
      {
        accountId: query.accountId,
        entity: query.entity,
        action: query.action,
        entityId: query.entityId,
        targetUserId: query.targetUserId,
        startDate: query.startDate,
        endDate: query.endDate,
      },
      {
        page: query.page,
        limit: query.limit,
        sortBy: query.sortBy,
        sortOrder: query.sortOrder,
      },
    );

    return AuditMapper.toListResponseDto(result);
  }

  @Get('entities/:entity/:entityId')
  @RequirePermission('view_audit_logs')
  @ApiOperation({
    summary: 'Get audit logs for a specific entity',
    description: 'Retrieve audit logs for a specific entity (e.g., a specific transaction, category, etc.).',
  })
  @ApiParam({
    name: 'entity',
    description: 'Entity type',
    enum: ['USER', 'ACCOUNT', 'WALLET', 'TRANSACTION', 'CATEGORY', 'SUBCATEGORY', 'ROLE', 'PERMISSION'],
  })
  @ApiParam({
    name: 'entityId',
    description: 'Entity ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Entity audit logs retrieved successfully',
    type: AuditLogListResponseDto,
  })
  async getEntityAuditLogs(
    @Request() req: any,
    @Param() params: GetEntityAuditLogsParamsDto,
    @Query() query: Omit<GetAuditLogsQueryDto, 'entity' | 'entityId'>,
  ): Promise<AuditLogListResponseDto> {
    const result = await this.getAuditLogsUseCase.getEntityAuditLogs(
      req.user.id,
      params.entity,
      params.entityId,
      {
        accountId: query.accountId,
        walletId: query.walletId,
        action: query.action,
        targetUserId: query.targetUserId,
        startDate: query.startDate,
        endDate: query.endDate,
      },
      {
        page: query.page,
        limit: query.limit,
        sortBy: query.sortBy,
        sortOrder: query.sortOrder,
      },
    );

    return AuditMapper.toListResponseDto(result);
  }

  @Get('users/:targetUserId')
  @RequirePermission('view_audit_logs')
  @ApiOperation({
    summary: 'Get audit logs for actions performed by a specific user',
    description: 'Retrieve audit logs for actions performed by a specific user. Only shows logs for resources the requesting user has access to.',
  })
  @ApiParam({
    name: 'targetUserId',
    description: 'User ID whose actions to retrieve',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'User audit logs retrieved successfully',
    type: AuditLogListResponseDto,
  })
  async getUserAuditLogs(
    @Request() req: any,
    @Param() params: GetUserAuditLogsParamsDto,
    @Query() query: Omit<GetAuditLogsQueryDto, 'targetUserId'>,
  ): Promise<AuditLogListResponseDto> {
    const result = await this.getAuditLogsUseCase.getUserAuditLogs(
      req.user.id,
      params.targetUserId,
      {
        accountId: query.accountId,
        walletId: query.walletId,
        entity: query.entity,
        action: query.action,
        entityId: query.entityId,
        startDate: query.startDate,
        endDate: query.endDate,
      },
      {
        page: query.page,
        limit: query.limit,
        sortBy: query.sortBy,
        sortOrder: query.sortOrder,
      },
    );

    return AuditMapper.toListResponseDto(result);
  }

  @Get('statistics')
  @RequirePermission('view_audit_logs')
  @ApiOperation({
    summary: 'Get audit log statistics',
    description: 'Retrieve audit log statistics and analytics for accessible resources.',
  })
  @ApiResponse({
    status: 200,
    description: 'Audit statistics retrieved successfully',
    type: AuditStatisticsResponseDto,
  })
  async getAuditStatistics(
    @Request() req: any,
    @Query() query: GetAuditLogsQueryDto,
  ): Promise<AuditStatisticsResponseDto> {
    const result = await this.getAuditLogsUseCase.getAuditStatistics(
      req.user.id,
      {
        accountId: query.accountId,
        walletId: query.walletId,
        entity: query.entity,
        action: query.action,
        entityId: query.entityId,
        targetUserId: query.targetUserId,
        startDate: query.startDate,
        endDate: query.endDate,
      },
    );

    return AuditMapper.toStatisticsResponseDto(result);
  }
}
