import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { AuditAction, AuditEntity } from '../../../core/constants/audit-actions';

export class AuditLogResponseDto {
  @ApiProperty({
    description: 'Audit log ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'User who performed the action',
    example: '123e4567-e89b-12d3-a456-************',
  })
  userId: string;

  @ApiProperty({
    description: 'Action performed',
    enum: AuditAction,
    example: AuditAction.CREATE,
  })
  action: AuditAction;

  @ApiProperty({
    description: 'Entity type',
    enum: AuditEntity,
    example: AuditEntity.ACCOUNT,
  })
  entity: AuditEntity;

  @ApiProperty({
    description: 'Entity ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  entityId: string;

  @ApiPropertyOptional({
    description: 'Account ID (if applicable)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  accountId?: string;

  @ApiPropertyOptional({
    description: 'Wallet ID (if applicable)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  walletId?: string;

  @ApiPropertyOptional({
    description: 'Values before the change',
    example: { name: 'Old Account Name', balance: 100 },
  })
  beforeValues?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Values after the change',
    example: { name: 'New Account Name', balance: 150 },
  })
  afterValues?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Additional metadata',
    example: { source: 'web', userAgent: 'Mozilla/5.0...' },
  })
  metadata?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'IP address of the user',
    example: '***********',
  })
  ipAddress?: string;

  @ApiPropertyOptional({
    description: 'User agent string',
    example: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
  })
  userAgent?: string;

  @ApiPropertyOptional({
    description: 'Session ID',
    example: 'sess_123456789',
  })
  sessionId?: string;

  @ApiProperty({
    description: 'Timestamp when the action was performed',
    example: '2024-01-15T10:30:00.000Z',
  })
  createdAt: Date;

  @ApiPropertyOptional({
    description: 'Human-readable description of the action',
    example: 'Created account',
  })
  description?: string;

  @ApiPropertyOptional({
    description: 'Changes made (for UPDATE actions)',
    example: {
      name: { from: 'Old Name', to: 'New Name' },
      balance: { from: 100, to: 150 },
    },
  })
  changes?: Record<string, { from: any; to: any }>;
}

export class AuditLogListResponseDto {
  @ApiProperty({
    description: 'List of audit logs',
    type: [AuditLogResponseDto],
  })
  auditLogs: AuditLogResponseDto[];

  @ApiProperty({
    description: 'Total number of audit logs',
    example: 150,
  })
  total: number;

  @ApiProperty({
    description: 'Current page number',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 50,
  })
  limit: number;

  @ApiProperty({
    description: 'Total number of pages',
    example: 3,
  })
  totalPages: number;

  @ApiProperty({
    description: 'Whether the user has full access to all audit logs',
    example: true,
  })
  userHasFullAccess: boolean;

  @ApiProperty({
    description: 'Summary of user access permissions',
    example: {
      accessibleAccountIds: ['account1', 'account2'],
      accessibleWalletIds: ['wallet1', 'wallet2'],
      isOwner: true,
    },
  })
  accessSummary: {
    accessibleAccountIds: string[];
    accessibleWalletIds: string[];
    isOwner: boolean;
  };
}

export class AuditStatisticsResponseDto {
  @ApiProperty({
    description: 'Total number of audit logs',
    example: 1500,
  })
  totalLogs: number;

  @ApiProperty({
    description: 'Breakdown by action type',
    example: {
      CREATE: 500,
      UPDATE: 400,
      DELETE: 100,
      READ: 300,
      SHARE: 50,
      UNSHARE: 25,
      LOGIN: 100,
      LOGOUT: 25,
    },
  })
  actionBreakdown: Record<AuditAction, number>;

  @ApiProperty({
    description: 'Breakdown by entity type',
    example: {
      USER: 200,
      ACCOUNT: 300,
      WALLET: 400,
      TRANSACTION: 500,
      CATEGORY: 100,
    },
  })
  entityBreakdown: Record<AuditEntity, number>;

  @ApiProperty({
    description: 'Daily activity for the last 30 days',
    example: [
      { date: '2024-01-15', count: 25 },
      { date: '2024-01-14', count: 30 },
    ],
  })
  dailyActivity: Array<{ date: string; count: number }>;

  @ApiProperty({
    description: 'Summary of user access permissions',
    example: {
      accessibleAccountIds: ['account1', 'account2'],
      accessibleWalletIds: ['wallet1', 'wallet2'],
      isOwner: true,
    },
  })
  accessSummary: {
    accessibleAccountIds: string[];
    accessibleWalletIds: string[];
    isOwner: boolean;
  };
}
