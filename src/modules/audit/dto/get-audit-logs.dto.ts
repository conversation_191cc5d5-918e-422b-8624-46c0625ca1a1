import { IsOption<PERSON>, <PERSON>Enum, <PERSON>U<PERSON><PERSON>, <PERSON><PERSON>ate<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, IsString } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { AuditAction, AuditEntity } from '../../../core/constants/audit-actions';

export class GetAuditLogsQueryDto {
  @ApiPropertyOptional({
    description: 'Filter by account ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  accountId?: string;

  @ApiPropertyOptional({
    description: 'Filter by wallet ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  walletId?: string;

  @ApiPropertyOptional({
    description: 'Filter by entity type',
    enum: AuditEntity,
    example: AuditEntity.ACCOUNT,
  })
  @IsOptional()
  @IsEnum(AuditEntity)
  entity?: AuditEntity;

  @ApiPropertyOptional({
    description: 'Filter by action type',
    enum: AuditAction,
    example: AuditAction.CREATE,
  })
  @IsOptional()
  @IsEnum(AuditAction)
  action?: AuditAction;

  @ApiPropertyOptional({
    description: 'Filter by specific entity ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsString()
  entityId?: string;

  @ApiPropertyOptional({
    description: 'Filter by user who performed the action',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  targetUserId?: string;

  @ApiPropertyOptional({
    description: 'Start date for filtering (ISO 8601)',
    example: '2024-01-01T00:00:00.000Z',
  })
  @IsOptional()
  @IsDateString()
  @Transform(({ value }) => value ? new Date(value) : undefined)
  startDate?: Date;

  @ApiPropertyOptional({
    description: 'End date for filtering (ISO 8601)',
    example: '2024-12-31T23:59:59.999Z',
  })
  @IsOptional()
  @IsDateString()
  @Transform(({ value }) => value ? new Date(value) : undefined)
  endDate?: Date;

  @ApiPropertyOptional({
    description: 'Page number (1-based)',
    example: 1,
    minimum: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({
    description: 'Number of items per page',
    example: 50,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 50;

  @ApiPropertyOptional({
    description: 'Sort field',
    enum: ['createdAt', 'action', 'entity'],
    example: 'createdAt',
  })
  @IsOptional()
  @IsEnum(['createdAt', 'action', 'entity'])
  sortBy?: 'createdAt' | 'action' | 'entity' = 'createdAt';

  @ApiPropertyOptional({
    description: 'Sort order',
    enum: ['asc', 'desc'],
    example: 'desc',
  })
  @IsOptional()
  @IsEnum(['asc', 'desc'])
  sortOrder?: 'asc' | 'desc' = 'desc';
}

export class GetAccountAuditLogsParamsDto {
  @ApiPropertyOptional({
    description: 'Account ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  accountId: string;
}

export class GetWalletAuditLogsParamsDto {
  @ApiPropertyOptional({
    description: 'Wallet ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  walletId: string;
}

export class GetEntityAuditLogsParamsDto {
  @ApiPropertyOptional({
    description: 'Entity type',
    enum: AuditEntity,
    example: AuditEntity.ACCOUNT,
  })
  @IsEnum(AuditEntity)
  entity: AuditEntity;

  @ApiPropertyOptional({
    description: 'Entity ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsString()
  entityId: string;
}

export class GetUserAuditLogsParamsDto {
  @ApiPropertyOptional({
    description: 'Target user ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  targetUserId: string;
}
