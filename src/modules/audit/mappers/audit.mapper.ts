import { AuditLog } from '../../../core/entities/audit-log.entity';
import { GetAuditLogsOutput } from '../../../core/use-cases/audit/get-audit-logs.use-case';
import {
  AuditLogResponseDto,
  AuditLogListResponseDto,
  AuditStatisticsResponseDto,
} from '../dto/audit-log-response.dto';
import { AuditAction, AuditEntity } from '../../../core/constants/audit-actions';

export class AuditMapper {
  static toResponseDto(auditLog: AuditLog): AuditLogResponseDto {
    return {
      id: auditLog.id,
      userId: auditLog.userId,
      action: auditLog.action,
      entity: auditLog.entity,
      entityId: auditLog.entityId,
      accountId: auditLog.accountId || undefined,
      walletId: auditLog.walletId || undefined,
      beforeValues: auditLog.beforeValues || undefined,
      afterValues: auditLog.afterValues || undefined,
      metadata: auditLog.metadata || undefined,
      ipAddress: auditLog.ipAddress || undefined,
      userAgent: auditLog.userAgent || undefined,
      sessionId: auditLog.sessionId || undefined,
      createdAt: auditLog.createdAt,
      description: auditLog.getDescription(),
      changes: auditLog.getChanges() || undefined,
    };
  }

  static toListResponseDto(result: GetAuditLogsOutput): AuditLogListResponseDto {
    return {
      auditLogs: result.auditLogs.map(this.toResponseDto),
      total: result.total,
      page: result.page,
      limit: result.limit,
      totalPages: result.totalPages,
      userHasFullAccess: result.userHasFullAccess,
      accessSummary: result.accessSummary,
    };
  }

  static toStatisticsResponseDto(result: {
    totalLogs: number;
    actionBreakdown: Record<AuditAction, number>;
    entityBreakdown: Record<AuditEntity, number>;
    dailyActivity: Array<{ date: string; count: number }>;
    accessSummary: {
      accessibleAccountIds: string[];
      accessibleWalletIds: string[];
      isOwner: boolean;
    };
  }): AuditStatisticsResponseDto {
    return {
      totalLogs: result.totalLogs,
      actionBreakdown: result.actionBreakdown,
      entityBreakdown: result.entityBreakdown,
      dailyActivity: result.dailyActivity,
      accessSummary: result.accessSummary,
    };
  }
}
