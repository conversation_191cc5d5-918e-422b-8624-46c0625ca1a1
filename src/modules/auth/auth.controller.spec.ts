import { Test, TestingModule } from '@nestjs/testing';
import { AuthController } from './controllers/auth.controller';
import { AuthService } from './services/auth.service';
import { RegisterDto, LoginDto, ForgotPasswordDto, VerifyResetCodeDto, ResetPasswordDto } from './dtos';
import { JwtAuthGuard } from '../../shared';

describe('AuthController', () => {
  let controller: AuthController;
  let authService: AuthService;

  const mockAuthService = {
    register: jest.fn(),
    login: jest.fn(),
    logout: jest.fn(),
    getUsers: jest.fn(),
    forgotPassword: jest.fn(),
    verifyResetCode: jest.fn(),
    resetPassword: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AuthController],
      providers: [
        {
          provide: AuthService,
          useValue: mockAuthService,
        },
      ],
    })
    .overrideGuard(JwtAuthGuard)
    .useValue({ canActivate: jest.fn(() => true) })
    .compile();

    controller = module.get<AuthController>(AuthController);
    authService = module.get<AuthService>(AuthService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('register', () => {
    it('should call authService.register with correct parameters', async () => {
      const dto: RegisterDto = {
        email: '<EMAIL>',
        password: 'Password123!',
        firstName: 'John',
        lastName: 'Doe',
        phoneNumber: '**********',
        countryCode: '234',
        bvn: '12345678901',
      };
      const deviceId = 'device-123';

      const expectedResult = {
        id: 'user-123',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        phoneNumber: '**********',
        countryCode: '234',
        bvn: '12345678901',
      };

      mockAuthService.register.mockResolvedValue(expectedResult);

      const result = await controller.register(dto, deviceId);

      expect(authService.register).toHaveBeenCalledWith(dto, deviceId);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('login', () => {
    it('should call authService.login with correct parameters', async () => {
      const dto: LoginDto = {
        email: '<EMAIL>',
        password: 'Password123!',
      };
      const deviceId = 'device-123';

      const expectedResult = {
        user: {
          id: 'user-123',
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          phoneNumber: '**********',
          countryCode: '234',
        },
        access_token: 'access-token',
        refresh_token: 'refresh-token',
      };

      mockAuthService.login.mockResolvedValue(expectedResult);

      const result = await controller.login(dto, deviceId);

      expect(authService.login).toHaveBeenCalledWith(dto, deviceId);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('logout', () => {
    it('should call authService.logout with correct parameters', async () => {
      const userId = 'user-123';
      const deviceId = 'device-123';
      const logoutAllDevices = false;

      mockAuthService.logout.mockResolvedValue(undefined);

      await controller.logout(userId, deviceId, logoutAllDevices);

      expect(authService.logout).toHaveBeenCalledWith(userId, deviceId, logoutAllDevices);
    });
  });

  describe('getUsers', () => {
    it('should call authService.getUsers', async () => {
      const expectedResult = [
        {
          id: 'user-123',
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
        },
      ];

      mockAuthService.getUsers.mockResolvedValue(expectedResult);

      const result = await controller.getUsers();

      expect(authService.getUsers).toHaveBeenCalled();
      expect(result).toEqual(expectedResult);
    });
  });

  describe('forgotPassword', () => {
    it('should call authService.forgotPassword with correct parameters', async () => {
      const dto: ForgotPasswordDto = {
        email: '<EMAIL>',
      };

      const expectedResult = {
        message:
          'If an account with that email exists, a password reset code has been sent.',
      };

      mockAuthService.forgotPassword.mockResolvedValue(expectedResult);

      const result = await controller.forgotPassword(dto);

      expect(authService.forgotPassword).toHaveBeenCalledWith(dto);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('verifyResetCode', () => {
    it('should call authService.verifyResetCode with correct parameters', async () => {
      const dto: VerifyResetCodeDto = {
        code: '123456',
      };

      const expectedResult = {
        verificationToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
        message: 'Code verified successfully. You can now reset your password.',
      };

      mockAuthService.verifyResetCode.mockResolvedValue(expectedResult);

      const result = await controller.verifyResetCode(dto);

      expect(authService.verifyResetCode).toHaveBeenCalledWith(dto);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('resetPassword', () => {
    it('should call authService.resetPassword with correct parameters', async () => {
      const dto: ResetPasswordDto = {
        verificationToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
        newPassword: 'NewPassword123!',
      };

      const expectedResult = {
        message: 'Password has been reset successfully.',
      };

      mockAuthService.resetPassword.mockResolvedValue(expectedResult);

      const result = await controller.resetPassword(dto);

      expect(authService.resetPassword).toHaveBeenCalledWith(dto);
      expect(result).toEqual(expectedResult);
    });
  });
});
