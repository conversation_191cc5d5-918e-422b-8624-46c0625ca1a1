import { Modu<PERSON> } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { AuthController } from './controllers/auth.controller';
import { AuthService } from './services/auth.service';

// Use Cases
import { RegisterUserUseCase } from '../../core/use-cases/auth/register-user.use-case';
import { LoginUserUseCase } from '../../core/use-cases/auth/login-user.use-case';
import { LogoutUserUseCase } from '../../core/use-cases/auth/logout-user.use-case';
import { RequestPasswordResetUseCase } from '../../core/use-cases/auth/request-password-reset.use-case';
import { VerifyResetCodeUseCase } from '../../core/use-cases/auth/verify-reset-code.use-case';
import { ResetPasswordUseCase } from '../../core/use-cases/auth/reset-password.use-case';
import { AssignDefaultRolesUseCase } from '../../core/use-cases/auth/assign-default-roles.use-case';
import { RegisterTrustedDeviceUseCase } from '../../core/use-cases/auth/register-trusted-device.use-case';

// Ports
import { PASSWORD_HASHER_REPOSITORY } from '../../core/ports/auth/password-hasher.port';

// Adapters
import { ArgonPasswordHasher } from '../../infrastructure/auth/argon-password-hasher';
import {
  TrustedDeviceRepositoryModule,
  TRUSTED_DEVICE_REPOSITORY,
} from '../../infrastructure/database/repositories/trusted-device/trusted-device-repository.module';
import { RefreshTokenRepositoryModule } from '../../infrastructure/database/repositories/refresh-token/refresh-token-repository.module';

// Framework dependencies
import { JwtService } from '@nestjs/jwt';
import { UserRepositoryModule } from '../../infrastructure/database/repositories/user/user-repository.module';
import { AccountRepositoryModule } from '../../infrastructure/database/repositories/account/account-repository.module';
import { WalletRepositoryModule } from '../../infrastructure/database/repositories/wallet/wallet-repository.module';
import { WalletTypeRepositoryModule } from '../../infrastructure/database/repositories/wallet-type/wallet-type-repository.module';
import { PasswordResetRepositoryModule } from '../../infrastructure/database/repositories/password-reset/password-reset-repository.module';
import { RoleRepositoryModule } from '../../infrastructure/database/repositories/role/role-repository.module';
import { UserSharedAccessRepositoryModule } from '../../infrastructure/database/repositories/user-shared-access/user-shared-access-repository.module';
import { EmailModule } from '../../infrastructure/notifications/email/email.module';
import { GetUsersUseCase } from '../../core/use-cases/auth/get-users.use-case';
import { SharedModule } from '../../shared/shared.module';

@Module({
  imports: [
    JwtModule.register({
      secret:
        process.env.JWT_SECRET ||
        (() => {
          throw new Error('JWT_SECRET is not defined');
        })(),
      signOptions: { expiresIn: '1h' },
    }),
    UserRepositoryModule, // Import UserRepositoryModule to get USER_REPOSITORY
    AccountRepositoryModule, // Import AccountRepositoryModule for account creation
    WalletRepositoryModule, // Import WalletRepositoryModule for wallet creation
    WalletTypeRepositoryModule, // Import WalletTypeRepositoryModule for wallet type lookup
    PasswordResetRepositoryModule, // Import PasswordResetRepositoryModule
    RoleRepositoryModule, // Import RoleRepositoryModule for role assignment
    UserSharedAccessRepositoryModule, // Import UserSharedAccessRepositoryModule for role assignment
    EmailModule, // Import EmailModule for email services
    TrustedDeviceRepositoryModule, // Import TrustedDeviceRepositoryModule
    RefreshTokenRepositoryModule, // Import RefreshTokenRepositoryModule
    SharedModule, // Import SharedModule for audit functionality
  ],
  controllers: [AuthController],
  providers: [
    // Use Cases
    RegisterUserUseCase,
    LoginUserUseCase,
    LogoutUserUseCase,
    RequestPasswordResetUseCase,
    VerifyResetCodeUseCase,
    ResetPasswordUseCase,
    AssignDefaultRolesUseCase,
    GetUsersUseCase,
    // RegisterTrustedDeviceUseCase is provided via useFactory below
    // Services
    AuthService,
    // Adapters (implement ports)
    {
      provide: PASSWORD_HASHER_REPOSITORY,
      useClass: ArgonPasswordHasher,
    },
    {
      provide: RegisterTrustedDeviceUseCase,
      useFactory: (trustedDeviceRepo) =>
        new RegisterTrustedDeviceUseCase(trustedDeviceRepo),
      inject: [TRUSTED_DEVICE_REPOSITORY],
    },
    // Framework dependencies
    JwtService,
  ],
  exports: [AuthService],
})
export class AuthModule {}
