import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Logger,
  Post,
  UseGuards,
  Headers,
  UseInterceptors,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Throttle } from '@nestjs/throttler';
import { AuthService } from '../services/auth.service';
import { RegisterDto, LoginDto } from '../dtos';
import { ForgotPasswordDto } from '../dtos/forgot-password.dto';
import { VerifyResetCodeDto } from '../dtos/verify-reset-code.dto';
import { ResetPasswordDto } from '../dtos/reset-password.dto';
import { RegisterResponseDto, LoginResponseDto } from '../dtos';
import { JwtAuthGuard } from '../../../shared';
import { GetUser } from '../../../shared';
import { AuditInterceptor } from '../../../shared/interceptors/audit.interceptor';
import { Audit } from '../../../shared/decorators/audit.decorator';
import { AuditAction, AuditEntity } from '../../../core/constants/audit-actions';

@ApiTags('Authentication')
@Controller('auth')
@UseInterceptors(AuditInterceptor)
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('register')
  @Throttle({ default: { limit: 5, ttl: 60000 } }) // 5 registrations per minute
  @Audit({
    action: AuditAction.CREATE,
    entity: AuditEntity.USER,
    entityIdParam: 'id', // Will be extracted from response
    captureRequest: true,
    captureResponse: true,
    description: 'User registered',
    sensitiveFields: ['password'],
  })
  async register(
    @Body() dto: RegisterDto,
    @Headers('x-device-id') deviceId?: string,
  ): Promise<RegisterResponseDto> {
    Logger.log('register', 'dto');
    return this.authService.register(dto, deviceId);
  }

  @Post('login')
  @Audit({
    action: AuditAction.LOGIN,
    entity: AuditEntity.USER,
    entityIdParam: 'id', // Will be extracted from response user.id
    captureRequest: true,
    description: 'User login',
    sensitiveFields: ['password'],
  })
  @HttpCode(HttpStatus.OK)
  @Throttle({ default: { limit: 10, ttl: 60000 } }) // 10 login attempts per minute
  async login(
    @Body() dto: LoginDto,
    @Headers('x-device-id') deviceId?: string,
  ): Promise<LoginResponseDto> {
    return this.authService.login(dto, deviceId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('users')
  async getUsers(): Promise<any> {
    return this.authService.getUsers();
  }

  @Post('logout')
  @Audit({
    action: AuditAction.LOGOUT,
    entity: AuditEntity.USER,
    entityIdParam: 'userId', // Will be extracted from @GetUser
    captureRequest: true,
    description: 'User logout',
  })
  @UseGuards(JwtAuthGuard)
  async logout(
    @GetUser('id') userId: string,
    @Headers('x-device-id') deviceId?: string,
    @Body('logoutAllDevices') logoutAllDevices?: boolean,
  ): Promise<void> {
    return this.authService.logout(userId, deviceId, logoutAllDevices);
  }

  @Post('forgot-password')
  @HttpCode(HttpStatus.OK)
  @Throttle({ default: { limit: 3, ttl: 300000 } }) // 3 password reset requests per 5 minutes
  @ApiOperation({ summary: 'Request password reset code for mobile app' })
  @ApiResponse({
    status: 200,
    description: '6-digit reset code sent to email successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example:
            'If an account with that email exists, a password reset code has been sent.',
        },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Invalid email format' })
  async forgotPassword(
    @Body() dto: ForgotPasswordDto,
  ): Promise<{ message: string }> {
    return this.authService.forgotPassword(dto);
  }

  @Post('verify-reset-code')
  @HttpCode(HttpStatus.OK)
  @Throttle({ default: { limit: 5, ttl: 60000 } }) // 5 code verification attempts per minute
  @ApiOperation({ summary: 'Verify 6-digit reset code' })
  @ApiResponse({
    status: 200,
    description: 'Code verified successfully',
    schema: {
      type: 'object',
      properties: {
        verificationToken: {
          type: 'string',
          example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
        },
        message: {
          type: 'string',
          example:
            'Code verified successfully. You can now reset your password.',
        },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Invalid code format' })
  @ApiResponse({
    status: 404,
    description: 'Code not found, expired, or already used',
  })
  async verifyResetCode(
    @Body() dto: VerifyResetCodeDto,
  ): Promise<{ verificationToken: string; message: string }> {
    return this.authService.verifyResetCode(dto);
  }

  @Post('reset-password')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Reset password using verification token' })
  @ApiResponse({
    status: 200,
    description: 'Password reset successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Password has been reset successfully.',
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid verification token or password requirements not met',
  })
  @ApiResponse({
    status: 404,
    description: 'Verification token expired or invalid',
  })
  async resetPassword(
    @Body() dto: ResetPasswordDto,
  ): Promise<{ message: string }> {
    return this.authService.resetPassword(dto);
  }
}
