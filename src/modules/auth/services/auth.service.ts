import { Injectable } from '@nestjs/common';
import { RegisterUserUseCase } from '../../../core/use-cases/auth/register-user.use-case';
import { LoginUserUseCase } from '../../../core/use-cases/auth/login-user.use-case';
import { LogoutUserUseCase } from '../../../core/use-cases/auth/logout-user.use-case';
import { RequestPasswordResetUseCase } from '../../../core/use-cases/auth/request-password-reset.use-case';
import { VerifyResetCodeUseCase } from '../../../core/use-cases/auth/verify-reset-code.use-case';
import { ResetPasswordUseCase } from '../../../core/use-cases/auth/reset-password.use-case';
import { RegisterDto, LoginDto } from '../dtos';
import { ForgotPasswordDto } from '../dtos/forgot-password.dto';
import { VerifyResetCodeDto } from '../dtos/verify-reset-code.dto';
import { ResetPasswordDto } from '../dtos/reset-password.dto';
import {
  RegisterResponseDto,
  LoginResponseDto,
} from '../dtos/auth-response.dto';
import { UserMapper } from '../mappers/user.mapper';
import { GetUsersUseCase } from '../../../core/use-cases/auth/get-users.use-case';
import { User } from '../../../core/entities/user.entity';
import { RegisterTrustedDeviceUseCase } from '../../../core/use-cases/auth/register-trusted-device.use-case';
import { REFRESH_TOKEN_REPOSITORY } from '../../../infrastructure/database/repositories/refresh-token/refresh-token-repository.module';
import { Inject } from '@nestjs/common';

@Injectable()
export class AuthService {
  constructor(
    private readonly registerUserUseCase: RegisterUserUseCase,
    private readonly loginUserUseCase: LoginUserUseCase,
    private readonly logoutUserUseCase: LogoutUserUseCase,
    private readonly requestPasswordResetUseCase: RequestPasswordResetUseCase,
    private readonly verifyResetCodeUseCase: VerifyResetCodeUseCase,
    private readonly resetPasswordUseCase: ResetPasswordUseCase,
    private readonly getUsersCase: GetUsersUseCase,
    private readonly registerTrustedDeviceUseCase: RegisterTrustedDeviceUseCase,
    @Inject(REFRESH_TOKEN_REPOSITORY)
    private readonly refreshTokenRepo,
  ) {}

  async register(
    dto: RegisterDto,
    deviceId?: string,
  ): Promise<RegisterResponseDto> {
    const user = await this.registerUserUseCase.execute({
      email: dto.email,
      password: dto.password,
      firstName: dto.firstName,
      lastName: dto.lastName,
      phoneNumber: dto.phoneNumber,
      bvn: dto.bvn,
    });
    if (deviceId) {
      try {
        await this.registerTrustedDeviceUseCase.execute(user.id, deviceId);
      } catch (error) {
        // Log the error but don't fail the registration
        console.warn('Failed to register trusted device:', error.message);
      }
    }
    return UserMapper.toResponseDto(user);
  }

  async login(dto: LoginDto, deviceId?: string): Promise<LoginResponseDto> {
    const result = await this.loginUserUseCase.execute({
      email: dto.email,
      password: dto.password,
      deviceId: deviceId || 'unknown',
    });
    if (deviceId) {
      try {
        await this.registerTrustedDeviceUseCase.execute(result.user.id, deviceId);
      } catch (error) {
        // Log the error but don't fail the login
        console.warn('Failed to register trusted device:', error.message);
      }
    }
    return {
      user: UserMapper.toResponseDto(result.user),
      access_token: result.tokens.accessToken,
      refresh_token: result.tokens.refreshToken,
    };
  }

  async logout(
    userId: string,
    deviceId?: string,
    logoutAllDevices?: boolean,
  ): Promise<void> {
    await this.logoutUserUseCase.execute({
      userId,
      deviceId,
      logoutAllDevices,
    });
  }

  async getUsers(): Promise<User[]> {
    return await this.getUsersCase.execute();
  }

  async forgotPassword(dto: ForgotPasswordDto): Promise<{ message: string }> {
    return await this.requestPasswordResetUseCase.execute({
      email: dto.email,
    });
  }

  async verifyResetCode(
    dto: VerifyResetCodeDto,
  ): Promise<{ verificationToken: string; message: string }> {
    return await this.verifyResetCodeUseCase.execute({
      code: dto.code,
    });
  }

  async resetPassword(dto: ResetPasswordDto): Promise<{ message: string }> {
    return await this.resetPasswordUseCase.execute({
      verificationToken: dto.verificationToken,
      newPassword: dto.newPassword,
    });
  }
}
