import { Module } from '@nestjs/common';
import { CategoriesController } from './controllers/categories.controller';
import { SubCategoriesController } from './controllers/subcategories.controller';
import { CategoriesService } from './services/categories.service';
import { SubCategoriesService } from './services/subcategories.service';

// Use Cases - Categories
import { CreateCategoryUseCase } from '../../core/use-cases/category/create-category.use-case';
import { UpdateCategoryUseCase } from '../../core/use-cases/category/update-category.use-case';
import { DeleteCategoryUseCase } from '../../core/use-cases/category/delete-category.use-case';
import { GetCategoriesUseCase } from '../../core/use-cases/category/get-categories.use-case';
import { GetCategoryByIdUseCase } from '../../core/use-cases/category/get-category-by-id.use-case';
import { GetCategoriesWithSubCategoriesUseCase } from '../../core/use-cases/category/get-categories-with-subcategories.use-case';
import { GetCategoryWithSubCategoriesUseCase } from '../../core/use-cases/category/get-category-with-subcategories.use-case';
import { UpdateCategoriesSortOrderUseCase } from '../../core/use-cases/category/update-categories-sort-order.use-case';

// Use Cases - SubCategories
import { CreateSubCategoryUseCase } from '../../core/use-cases/subcategory/create-subcategory.use-case';
import { UpdateSubCategoryUseCase } from '../../core/use-cases/subcategory/update-subcategory.use-case';
import { DeleteSubCategoryUseCase } from '../../core/use-cases/subcategory/delete-subcategory.use-case';
import { GetSubCategoriesUseCase } from '../../core/use-cases/subcategory/get-subcategories.use-case';
import { GetSubCategoryByIdUseCase } from '../../core/use-cases/subcategory/get-subcategory-by-id.use-case';
import { UpdateSubCategoriesSortOrderUseCase } from '../../core/use-cases/subcategory/update-subcategories-sort-order.use-case';

// Repository Modules
import { CategoryRepositoryModule } from '../../infrastructure/database/repositories/category/category-repository.module';
import { SubCategoryRepositoryModule } from '../../infrastructure/database/repositories/subcategory/subcategory-repository.module';
import { SharedModule } from '../../shared/shared.module';

@Module({
  imports: [CategoryRepositoryModule, SubCategoryRepositoryModule, SharedModule],
  controllers: [CategoriesController, SubCategoriesController],
  providers: [
    // Services
    CategoriesService,
    SubCategoriesService,

    // Category Use Cases
    CreateCategoryUseCase,
    UpdateCategoryUseCase,
    DeleteCategoryUseCase,
    GetCategoriesUseCase,
    GetCategoryByIdUseCase,
    GetCategoriesWithSubCategoriesUseCase,
    GetCategoryWithSubCategoriesUseCase,
    UpdateCategoriesSortOrderUseCase,

    // SubCategory Use Cases
    CreateSubCategoryUseCase,
    UpdateSubCategoryUseCase,
    DeleteSubCategoryUseCase,
    GetSubCategoriesUseCase,
    GetSubCategoryByIdUseCase,
    UpdateSubCategoriesSortOrderUseCase,
  ],
  exports: [CategoriesService, SubCategoriesService],
})
export class CategoriesModule {}
