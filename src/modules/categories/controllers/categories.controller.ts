import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { CategoriesService } from '../services/categories.service';
import {
  CreateCategoryDto,
  UpdateCategoryDto,
  CategoryResponseDto,
  CategoryWithSubCategoriesResponseDto,
  UpdateCategoriesSortOrderDto,
} from '../dtos';
import { JwtAuthGuard, GetUser } from '../../../shared';
import { AuditInterceptor } from '../../../shared/interceptors/audit.interceptor';
import {
  AuditCreate,
  AuditUpdate,
  AuditDelete,
  AuditRead
} from '../../../shared/decorators/audit.decorator';
import { SkipAudit } from '../../../shared/decorators/skip-audit.decorator';
import { AuditEntity } from '../../../core/constants/audit-actions';

@ApiTags('Categories')
@Controller('categories')
@UseGuards(JwtAuthGuard)
@UseInterceptors(AuditInterceptor)
export class CategoriesController {
  constructor(private readonly categoriesService: CategoriesService) {}

  @Post()
  // Audit logging is handled in the use case layer
  @ApiOperation({ summary: 'Create a new category' })
  @ApiResponse({
    status: 201,
    description: 'Category created successfully',
    type: CategoryResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async createCategory(
    @GetUser('id') userId: string,
    @Body() createCategoryDto: CreateCategoryDto,
  ): Promise<CategoryResponseDto> {
    return this.categoriesService.createCategory(userId, createCategoryDto);
  }

  @Get('with-subcategories')
  @ApiOperation({
    summary:
      'Get all categories with their subcategories for the authenticated user',
  })
  @ApiQuery({
    name: 'type',
    required: false,
    description: 'Filter by category type',
    enum: ['income', 'expense'],
  })
  @ApiResponse({
    status: 200,
    description: 'Categories with subcategories retrieved successfully',
    type: [CategoryWithSubCategoriesResponseDto],
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getCategoriesWithSubCategories(
    @GetUser('id') userId: string,
    @Query('type') type?: string,
  ): Promise<CategoryWithSubCategoriesResponseDto[]> {
    return this.categoriesService.getCategoriesWithSubCategories(userId, type);
  }

  @Get()
  @ApiOperation({ summary: 'Get all categories for the authenticated user' })
  @ApiQuery({
    name: 'type',
    required: false,
    description: 'Filter by category type',
    enum: ['income', 'expense'],
  })
  @ApiResponse({
    status: 200,
    description: 'Categories retrieved successfully',
    type: [CategoryResponseDto],
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getCategories(
    @GetUser('id') userId: string,
    @Query('type') type?: string,
  ): Promise<CategoryResponseDto[]> {
    return this.categoriesService.getCategories(userId, type);
  }

  @Get(':id/with-subcategories')
  @ApiOperation({ summary: 'Get a category with its subcategories by ID' })
  @ApiParam({ name: 'id', description: 'Category ID' })
  @ApiResponse({
    status: 200,
    description: 'Category with subcategories retrieved successfully',
    type: CategoryWithSubCategoriesResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Category not found' })
  async getCategoryWithSubCategories(
    @Param('id') id: string,
    @GetUser('id') userId: string,
  ): Promise<CategoryWithSubCategoriesResponseDto> {
    return this.categoriesService.getCategoryWithSubCategories(id, userId);
  }

  @Get(':id')
  @AuditRead(AuditEntity.CATEGORY, 'id', {
    description: 'Category viewed via API',
  })
  @ApiOperation({ summary: 'Get a category by ID' })
  @ApiParam({ name: 'id', description: 'Category ID' })
  @ApiResponse({
    status: 200,
    description: 'Category retrieved successfully',
    type: CategoryResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Category not found' })
  async getCategoryById(
    @Param('id') id: string,
    @GetUser('id') userId: string,
  ): Promise<CategoryResponseDto> {
    return this.categoriesService.getCategoryById(id, userId);
  }

  @Put(':id')
  @AuditUpdate(AuditEntity.CATEGORY, 'id', {
    captureRequest: true,
    captureResponse: true,
    description: 'Category updated via API',
    sensitiveFields: [],
  })
  @ApiOperation({ summary: 'Update a category' })
  @ApiParam({ name: 'id', description: 'Category ID' })
  @ApiResponse({
    status: 200,
    description: 'Category updated successfully',
    type: CategoryResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Category not found' })
  async updateCategory(
    @Param('id') id: string,
    @GetUser('id') userId: string,
    @Body() updateCategoryDto: UpdateCategoryDto,
  ): Promise<CategoryResponseDto> {
    return this.categoriesService.updateCategory(id, userId, updateCategoryDto);
  }

  @Delete(':id')
  @SkipAudit() // Skip automatic audit - manual audit logging in use case handles cascade deletions
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete a category' })
  @ApiParam({ name: 'id', description: 'Category ID' })
  @ApiResponse({ status: 204, description: 'Category deleted successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Category not found' })
  async deleteCategory(
    @Param('id') id: string,
    @GetUser('id') userId: string,
  ): Promise<void> {
    return this.categoriesService.deleteCategory(id, userId);
  }

  @Put('sort-order')
  @ApiOperation({ summary: 'Update sort order for categories' })
  @ApiResponse({
    status: 200,
    description: 'Categories sort order updated successfully',
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async updateCategoriesSortOrder(
    @GetUser('id') userId: string,
    @Body() updateSortOrderDto: UpdateCategoriesSortOrderDto,
  ): Promise<void> {
    return this.categoriesService.updateCategoriesSortOrder(
      userId,
      updateSortOrderDto,
    );
  }
}
