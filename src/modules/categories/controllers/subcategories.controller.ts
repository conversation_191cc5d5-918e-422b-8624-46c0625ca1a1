import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { SubCategoriesService } from '../services/subcategories.service';
import {
  CreateSubCategoryDto,
  UpdateSubCategoryDto,
  SubCategoryResponseDto,
  UpdateSubCategoriesSortOrderDto,
} from '../dtos';
import { JwtAuthGuard, GetUser } from '../../../shared';
import { AuditInterceptor } from '../../../shared/interceptors/audit.interceptor';
import {
  AuditCreate,
  AuditUpdate,
  AuditDelete,
  AuditRead
} from '../../../shared/decorators/audit.decorator';
import { AuditEntity } from '../../../core/constants/audit-actions';

@ApiTags('SubCategories')
@Controller('subcategories')
@UseGuards(JwtAuthGuard)
@UseInterceptors(AuditInterceptor)
export class SubCategoriesController {
  constructor(private readonly subCategoriesService: SubCategoriesService) {}

  @Post()
  @AuditCreate(AuditEntity.SUBCATEGORY, 'id', {
    captureResponse: true,
    description: 'Subcategory created via API',
  })
  @ApiOperation({ summary: 'Create a new subcategory' })
  @ApiResponse({
    status: 201,
    description: 'SubCategory created successfully',
    type: SubCategoryResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async createSubCategory(
    @GetUser('id') userId: string,
    @Body() createSubCategoryDto: CreateSubCategoryDto,
  ): Promise<SubCategoryResponseDto> {
    return this.subCategoriesService.createSubCategory(
      userId,
      createSubCategoryDto,
    );
  }

  @Get()
  @ApiOperation({ summary: 'Get all subcategories for the authenticated user' })
  @ApiQuery({
    name: 'categoryId',
    required: false,
    description: 'Filter by category ID',
  })
  @ApiResponse({
    status: 200,
    description: 'SubCategories retrieved successfully',
    type: [SubCategoryResponseDto],
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getSubCategories(
    @GetUser('id') userId: string,
    @Query('categoryId') categoryId?: string,
  ): Promise<SubCategoryResponseDto[]> {
    return this.subCategoriesService.getSubCategories(userId, categoryId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a subcategory by ID' })
  @ApiParam({ name: 'id', description: 'SubCategory ID' })
  @ApiResponse({
    status: 200,
    description: 'SubCategory retrieved successfully',
    type: SubCategoryResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'SubCategory not found' })
  async getSubCategoryById(
    @Param('id') id: string,
    @GetUser('id') userId: string,
  ): Promise<SubCategoryResponseDto> {
    return this.subCategoriesService.getSubCategoryById(id, userId);
  }

  @Put(':id')
  @AuditUpdate(AuditEntity.SUBCATEGORY, 'id', {
    captureRequest: true,
    captureResponse: true,
    description: 'Subcategory updated via API',
  })
  @ApiOperation({ summary: 'Update a subcategory' })
  @ApiParam({ name: 'id', description: 'SubCategory ID' })
  @ApiResponse({
    status: 200,
    description: 'SubCategory updated successfully',
    type: SubCategoryResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'SubCategory not found' })
  async updateSubCategory(
    @Param('id') id: string,
    @GetUser('id') userId: string,
    @Body() updateSubCategoryDto: UpdateSubCategoryDto,
  ): Promise<SubCategoryResponseDto> {
    return this.subCategoriesService.updateSubCategory(
      id,
      userId,
      updateSubCategoryDto,
    );
  }

  @Delete(':id')
  @AuditDelete(AuditEntity.SUBCATEGORY, 'id', {
    captureRequest: true,
    description: 'Subcategory deleted via API',
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete a subcategory' })
  @ApiParam({ name: 'id', description: 'SubCategory ID' })
  @ApiResponse({ status: 204, description: 'SubCategory deleted successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'SubCategory not found' })
  async deleteSubCategory(
    @Param('id') id: string,
    @GetUser('id') userId: string,
  ): Promise<void> {
    return this.subCategoriesService.deleteSubCategory(id, userId);
  }

  @Put('sort-order')
  @ApiOperation({ summary: 'Update sort order for subcategories' })
  @ApiResponse({
    status: 200,
    description: 'SubCategories sort order updated successfully',
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async updateSubCategoriesSortOrder(
    @GetUser('id') userId: string,
    @Body() updateSortOrderDto: UpdateSubCategoriesSortOrderDto,
  ): Promise<void> {
    return this.subCategoriesService.updateSubCategoriesSortOrder(
      userId,
      updateSortOrderDto,
    );
  }
}
