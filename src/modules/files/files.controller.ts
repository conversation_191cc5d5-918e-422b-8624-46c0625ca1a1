import {
  Controller,
  Post,
  Get,
  Delete,
  Param,
  Body,
  UploadedFile,
  UseInterceptors,
  UseGuards,
  HttpCode,
  HttpStatus,
  Logger,
  Query,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiConsumes } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../shared/guards/jwt-auth.guard';
import { GetUser } from '../../shared/decorators/get-user.decorator';
import { User } from '../../core/entities/user.entity';
import { FilesService } from './files.service';

@ApiTags('Files')
@Controller('files')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class FilesController {
  private readonly logger = new Logger(FilesController.name);

  constructor(private readonly filesService: FilesService) {}

  @Post('upload')
  @HttpCode(HttpStatus.OK)
  @UseInterceptors(FileInterceptor('file'))
  @ApiOperation({ 
    summary: 'Upload a file',
    description: 'Upload a file to the configured storage provider'
  })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({ status: 200, description: 'File uploaded successfully' })
  @ApiResponse({ status: 400, description: 'Invalid file or upload failed' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async uploadFile(
    @UploadedFile() file: Express.Multer.File,
    @Body('folder') folder: string,
    @Body('tags') tags: string,
    @GetUser() user: User,
  ) {
    this.logger.log(`File upload request from user ${user.id}`, {
      userId: user.id,
      fileName: file?.originalname,
      size: file?.size,
      folder,
    });

    try {
      const result = await this.filesService.uploadFile({
        file: file.buffer,
        fileName: file.originalname,
        mimeType: file.mimetype,
        folder: folder || 'general',
        userId: user.id,
        tags: tags ? tags.split(',').map(tag => tag.trim()) : [],
      });

      return {
        success: result.success,
        message: result.success ? 'File uploaded successfully' : 'File upload failed',
        data: result.success ? {
          fileUrl: result.fileUrl,
          secureUrl: result.secureUrl,
          publicId: result.publicId,
          format: result.format,
          size: result.size,
        } : null,
        error: result.errorMessage,
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`File upload failed for user ${user.id}`, {
        userId: user.id,
        error: errorMessage,
      });

      return {
        success: false,
        message: 'File upload failed',
        data: null,
        error: errorMessage,
      };
    }
  }

  @Get('signed-upload-url')
  @ApiOperation({ 
    summary: 'Generate signed upload URL',
    description: 'Generate a signed URL for direct file upload to storage provider'
  })
  @ApiResponse({ status: 200, description: 'Signed upload URL generated successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getSignedUploadUrl(
    @Query('folder') folder: string,
    @Query('maxFileSize') maxFileSize: string,
    @Query('allowedFormats') allowedFormats: string,
    @GetUser() user: User,
  ) {
    try {
      const result = await this.filesService.generateSignedUploadUrl({
        folder: folder || 'general',
        userId: user.id,
        expiresIn: 3600, // 1 hour
        maxFileSize: maxFileSize ? parseInt(maxFileSize) : 10 * 1024 * 1024, // 10MB default
        allowedFormats: allowedFormats ? allowedFormats.split(',') : ['jpg', 'jpeg', 'png', 'pdf'],
      });

      return {
        success: true,
        message: 'Signed upload URL generated successfully',
        data: result,
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Failed to generate signed upload URL for user ${user.id}`, {
        userId: user.id,
        error: errorMessage,
      });

      return {
        success: false,
        message: 'Failed to generate signed upload URL',
        data: null,
        error: errorMessage,
      };
    }
  }

  @Get(':publicId/info')
  @ApiOperation({ 
    summary: 'Get file information',
    description: 'Get detailed information about a file'
  })
  @ApiResponse({ status: 200, description: 'File information retrieved successfully' })
  @ApiResponse({ status: 404, description: 'File not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getFileInfo(
    @Param('publicId') publicId: string,
    @GetUser() user: User,
  ) {
    try {
      const info = await this.filesService.getFileInfo(publicId);
      
      return {
        success: true,
        message: 'File information retrieved successfully',
        data: info,
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Failed to get file info for user ${user.id}`, {
        userId: user.id,
        publicId,
        error: errorMessage,
      });

      return {
        success: false,
        message: 'Failed to get file information',
        data: null,
        error: errorMessage,
      };
    }
  }

  @Get(':publicId/url')
  @ApiOperation({ 
    summary: 'Get file URL',
    description: 'Get a secure URL to access a file'
  })
  @ApiResponse({ status: 200, description: 'File URL generated successfully' })
  @ApiResponse({ status: 404, description: 'File not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getFileUrl(
    @Param('publicId') publicId: string,
    @Query('expiresIn') expiresIn: string,
    @Query('width') width: string,
    @Query('height') height: string,
    @GetUser() user: User,
  ) {
    try {
      const url = await this.filesService.getSecureFileUrl(publicId, {
        expiresIn: expiresIn ? parseInt(expiresIn) : 3600,
        transformation: (width || height) ? {
          width: width ? parseInt(width) : undefined,
          height: height ? parseInt(height) : undefined,
          crop: 'fill',
        } : undefined,
      });
      
      return {
        success: true,
        message: 'File URL generated successfully',
        data: url,
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Failed to get file URL for user ${user.id}`, {
        userId: user.id,
        publicId,
        error: errorMessage,
      });

      return {
        success: false,
        message: 'Failed to get file URL',
        data: null,
        error: errorMessage,
      };
    }
  }

  @Delete(':publicId')
  @ApiOperation({ 
    summary: 'Delete a file',
    description: 'Delete a file from storage'
  })
  @ApiResponse({ status: 200, description: 'File deleted successfully' })
  @ApiResponse({ status: 404, description: 'File not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async deleteFile(
    @Param('publicId') publicId: string,
    @GetUser() user: User,
  ) {
    try {
      const result = await this.filesService.deleteFile({ publicId });
      
      return {
        success: result.success,
        message: result.success ? 'File deleted successfully' : 'File deletion failed',
        data: result.success ? { publicId } : null,
        error: result.errorMessage,
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Failed to delete file for user ${user.id}`, {
        userId: user.id,
        publicId,
        error: errorMessage,
      });

      return {
        success: false,
        message: 'Failed to delete file',
        data: null,
        error: errorMessage,
      };
    }
  }

  @Get('storage/capabilities')
  @ApiOperation({ 
    summary: 'Get storage provider capabilities',
    description: 'Get information about what the current storage provider supports'
  })
  @ApiResponse({ status: 200, description: 'Storage capabilities retrieved successfully' })
  async getStorageCapabilities() {
    try {
      const capabilities = await this.filesService.getStorageCapabilities();
      
      return {
        success: true,
        message: 'Storage capabilities retrieved successfully',
        data: capabilities,
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Failed to get storage capabilities`, {
        error: errorMessage,
      });

      return {
        success: false,
        message: 'Failed to get storage capabilities',
        data: null,
        error: errorMessage,
      };
    }
  }
}
