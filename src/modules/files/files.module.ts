import { Module } from '@nestjs/common';
import { FilesController } from './files.controller';
import { FilesService } from './files.service';
import { StorageModule } from '../../infrastructure/storage/storage.module';
import { StorageConfigService } from '../../infrastructure/storage/storage-config.service';

@Module({
  imports: [StorageModule],
  controllers: [FilesController],
  providers: [FilesService, StorageConfigService],
  exports: [FilesService],
})
export class FilesModule {}
