import { Injectable, Inject, Logger } from '@nestjs/common';
import { STORAGE_SERVICE } from '../../infrastructure/storage/storage.module';
import { StorageServiceInterface } from '../../infrastructure/storage/interfaces/storage.interface';
import { StorageConfigService } from '../../infrastructure/storage/storage-config.service';

@Injectable()
export class FilesService {
  private readonly logger = new Logger(FilesService.name);

  constructor(
    @Inject(STORAGE_SERVICE)
    private readonly storageService: StorageServiceInterface,
    private readonly storageConfigService: StorageConfigService,
  ) {}

  async uploadFile(request: {
    file: Buffer;
    fileName: string;
    mimeType: string;
    folder?: string;
    userId?: string;
    tags?: string[];
  }) {
    this.logger.log(`Uploading file via storage service`, {
      fileName: request.fileName,
      folder: request.folder,
      userId: request.userId,
    });

    return this.storageService.uploadFile({
      file: request.file,
      fileName: request.fileName,
      mimeType: request.mimeType,
      folder: request.folder,
      userId: request.userId,
      tags: request.tags,
    });
  }

  async uploadMultipleFiles(requests: Array<{
    file: Buffer;
    fileName: string;
    mimeType: string;
    folder?: string;
    userId?: string;
    tags?: string[];
  }>) {
    this.logger.log(`Uploading ${requests.length} files via storage service`);

    return this.storageService.uploadMultipleFiles(requests.map(req => ({
      file: req.file,
      fileName: req.fileName,
      mimeType: req.mimeType,
      folder: req.folder,
      userId: req.userId,
      tags: req.tags,
    })));
  }

  async getFileUrl(publicId: string, options?: {
    expiresIn?: number;
    secure?: boolean;
    transformation?: {
      width?: number;
      height?: number;
      quality?: number;
      format?: 'jpg' | 'png' | 'pdf' | 'webp';
      crop?: 'fill' | 'fit' | 'scale' | 'crop';
      gravity?: 'auto' | 'center' | 'face' | 'faces';
    };
  }) {
    return this.storageService.getFileUrl(publicId, options);
  }

  async getSecureFileUrl(publicId: string, options?: {
    expiresIn?: number;
    transformation?: {
      width?: number;
      height?: number;
      quality?: number;
      format?: 'jpg' | 'png' | 'pdf' | 'webp';
      crop?: 'fill' | 'fit' | 'scale' | 'crop';
      gravity?: 'auto' | 'center' | 'face' | 'faces';
    };
  }) {
    return this.storageService.getSecureFileUrl(publicId, {
      secure: true,
      ...options,
    });
  }

  async downloadFile(publicId: string): Promise<Buffer> {
    return this.storageService.downloadFile(publicId);
  }

  async transformFile(publicId: string, options: {
    width?: number;
    height?: number;
    quality?: number;
    format?: 'jpg' | 'png' | 'pdf' | 'webp';
    crop?: 'fill' | 'fit' | 'scale' | 'crop';
    gravity?: 'auto' | 'center' | 'face' | 'faces';
  }) {
    const capabilities = this.storageConfigService.getCurrentProviderCapabilities();
    
    if (!capabilities.supportsTransformation) {
      this.logger.warn('Current storage provider does not support image transformation');
      return this.getFileUrl(publicId);
    }

    return this.storageService.transformFile(publicId, options);
  }

  async generateThumbnail(publicId: string, width = 200, height = 200) {
    const capabilities = this.storageConfigService.getCurrentProviderCapabilities();
    
    if (!capabilities.supportsThumbnails) {
      this.logger.warn('Current storage provider does not support thumbnail generation');
      return this.getFileUrl(publicId);
    }

    return this.storageService.generateThumbnail(publicId, width, height);
  }

  async deleteFile(request: { publicId: string; fileUrl?: string }) {
    this.logger.log(`Deleting file via storage service`, {
      publicId: request.publicId,
    });

    return this.storageService.deleteFile(request);
  }

  async deleteMultipleFiles(requests: Array<{ publicId: string; fileUrl?: string }>) {
    this.logger.log(`Deleting ${requests.length} files via storage service`);

    return this.storageService.deleteMultipleFiles(requests);
  }

  async getFileMetadata(publicId: string) {
    return this.storageService.getFileMetadata(publicId);
  }

  async updateFileMetadata(publicId: string, metadata: Record<string, any>) {
    const capabilities = this.storageConfigService.getCurrentProviderCapabilities();
    
    if (!capabilities.supportsMetadataUpdate) {
      this.logger.warn('Current storage provider does not support metadata updates');
      return false;
    }

    return this.storageService.updateFileMetadata(publicId, metadata);
  }

  async validateFile(publicId: string) {
    return this.storageService.validateFile(publicId);
  }

  async getFileInfo(publicId: string) {
    return this.storageService.getFileInfo(publicId);
  }

  async generateSignedUploadUrl(options: {
    folder?: string;
    userId?: string;
    expiresIn?: number;
    maxFileSize?: number;
    allowedFormats?: string[];
    tags?: string[];
  }) {
    const capabilities = this.storageConfigService.getCurrentProviderCapabilities();
    
    if (!capabilities.supportsSignedUrls) {
      this.logger.warn('Current storage provider does not support signed upload URLs');
      throw new Error('Signed upload URLs not supported by current storage provider');
    }

    return this.storageService.generateSignedUploadUrl(options);
  }

  async deleteExpiredFiles() {
    return this.storageService.deleteExpiredFiles();
  }

  async deleteUserFiles(userId: string) {
    this.logger.log(`Deleting all files for user ${userId}`);
    return this.storageService.deleteUserFiles(userId);
  }

  async getStorageCapabilities() {
    const config = this.storageConfigService.getStorageConfig();
    const capabilities = this.storageConfigService.getCurrentProviderCapabilities();
    
    return {
      provider: config.provider,
      capabilities,
      recommendations: {
        forImageProcessing: this.storageConfigService.getRecommendedProvider({
          needsTransformation: true,
          needsThumbnails: true,
        }),
        forLargeScale: this.storageConfigService.getRecommendedProvider({
          scale: 'large',
          budget: 'medium',
        }),
        forBudgetFriendly: this.storageConfigService.getRecommendedProvider({
          budget: 'low',
          scale: 'small',
        }),
      },
    };
  }

  async getStorageStats() {
    // This would require implementing storage-specific analytics
    // For now, return basic info
    return {
      provider: this.storageConfigService.getStorageConfig().provider,
      capabilities: this.storageConfigService.getCurrentProviderCapabilities(),
      // In a real implementation, you might track:
      // - Total files uploaded
      // - Total storage used
      // - Upload success rate
      // - Average file size
      // etc.
    };
  }

  // Utility method to check if a feature is supported
  isFeatureSupported(feature: 'transformation' | 'thumbnails' | 'metadataUpdate' | 'signedUrls' | 'tagging'): boolean {
    const capabilities = this.storageConfigService.getCurrentProviderCapabilities();
    
    switch (feature) {
      case 'transformation':
        return capabilities.supportsTransformation;
      case 'thumbnails':
        return capabilities.supportsThumbnails;
      case 'metadataUpdate':
        return capabilities.supportsMetadataUpdate;
      case 'signedUrls':
        return capabilities.supportsSignedUrls;
      case 'tagging':
        return capabilities.supportsTagging;
      default:
        return false;
    }
  }
}
