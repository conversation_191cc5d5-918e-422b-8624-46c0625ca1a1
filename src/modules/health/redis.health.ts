import { Injectable } from '@nestjs/common';
import {
  HealthIndicator,
  HealthIndicatorResult,
  HealthCheckError,
} from '@nestjs/terminus';
import { RedisService } from '../../infrastructure/external/redis/redis.service';

@Injectable()
export class RedisHealthIndicator extends HealthIndicator {
  constructor(private readonly redis: RedisService) {
    super();
  }

  async isHealthy(key: string): Promise<HealthIndicatorResult> {
    try {
      await this.redis.getClient().ping();
      return super.getStatus(key, true);
    } catch (err) {
      throw new HealthCheckError('Redis check failed', super.getStatus(key, false, { message: err.message }));
    }
  }
}
