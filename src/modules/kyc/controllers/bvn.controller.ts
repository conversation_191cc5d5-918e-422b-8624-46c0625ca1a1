import {
  Controller,
  Post,
  Get,
  Body,
  Param,
  UseGuards,
  HttpCode,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../../shared/guards/jwt-auth.guard';
import { GetUser } from '../../../shared/decorators/get-user.decorator';
import { User } from '../../../core/entities/user.entity';
import { VerifyBvnUseCase } from '../../../core/use-cases/kyc/verify-bvn.use-case';
import { VerifyBvnDto, VerifyBvnResponseDto } from '../dtos/verify-bvn.dto';

@ApiTags('KYC - BVN Verification')
@Controller('kyc/bvn')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class BvnController {
  private readonly logger = new Logger(BvnController.name);

  constructor(private readonly verifyBvnUseCase: VerifyBvnUseCase) {}

  @Post('verify')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: 'Verify BVN',
    description: 'Verify a Bank Verification Number (BVN) for identity verification'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'BVN verification completed',
    type: VerifyBvnResponseDto
  })
  @ApiResponse({ status: 400, description: 'Invalid BVN format or missing required fields' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async verifyBvn(
    @Body() dto: VerifyBvnDto,
    @GetUser() user: User,
  ): Promise<VerifyBvnResponseDto> {
    this.logger.log(`BVN verification request from user ${user.id}`, {
      userId: user.id,
      bvn: dto.bvn.substring(0, 3) + '********', // Log only first 3 digits for security
    });

    try {
      const result = await this.verifyBvnUseCase.execute({
        userId: user.id,
        bvn: dto.bvn,
        firstName: dto.firstName,
        lastName: dto.lastName,
        dateOfBirth: dto.dateOfBirth,
        phoneNumber: dto.phoneNumber,
        countryCode: user.countryCode,
      });

      this.logger.log(`BVN verification completed for user ${user.id}`, {
        userId: user.id,
        verificationId: result.verification.id,
        isVerified: result.isVerified,
        status: result.verification.status,
      });

      return {
        success: true,
        message: result.isVerified 
          ? 'BVN verification successful' 
          : 'BVN verification failed',
        data: {
          verificationId: result.verification.id,
          status: result.verification.status,
          isVerified: result.isVerified,
          extractedData: result.extractedData,
          providerUsed: result.providerUsed,
          responseTime: result.responseTime,
          verifiedAt: result.verification.verifiedAt,
          expiresAt: result.verification.expiresAt,
        },
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`BVN verification failed for user ${user.id}`, {
        userId: user.id,
        error: errorMessage,
      });

      return {
        success: false,
        message: errorMessage,
        data: null,
      };
    }
  }

  @Get('status')
  @ApiOperation({ 
    summary: 'Get BVN verification status',
    description: 'Get the current BVN verification status for the authenticated user'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'BVN verification status retrieved successfully'
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getBvnStatus(@GetUser() user: User) {
    this.logger.log(`BVN status request from user ${user.id}`, {
      userId: user.id,
    });

    try {
      const status = await this.verifyBvnUseCase.getUserBvnStatus(user.id);

      return {
        success: true,
        message: 'BVN status retrieved successfully',
        data: status,
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Failed to get BVN status for user ${user.id}`, {
        userId: user.id,
        error: errorMessage,
      });

      return {
        success: false,
        message: errorMessage,
        data: null,
      };
    }
  }

  @Post('retry/:verificationId')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: 'Retry BVN verification',
    description: 'Retry a failed BVN verification'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'BVN verification retry completed',
    type: VerifyBvnResponseDto
  })
  @ApiResponse({ status: 400, description: 'Invalid verification ID or verification already successful' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Verification not found' })
  async retryBvnVerification(
    @Param('verificationId') verificationId: string,
    @GetUser() user: User,
  ): Promise<VerifyBvnResponseDto> {
    this.logger.log(`BVN verification retry request from user ${user.id}`, {
      userId: user.id,
      verificationId,
    });

    try {
      const result = await this.verifyBvnUseCase.retryBvnVerification(verificationId);

      // Verify that the verification belongs to the authenticated user
      if (result.verification.userId !== user.id) {
        throw new Error('Verification does not belong to the authenticated user');
      }

      this.logger.log(`BVN verification retry completed for user ${user.id}`, {
        userId: user.id,
        verificationId: result.verification.id,
        isVerified: result.isVerified,
        status: result.verification.status,
      });

      return {
        success: true,
        message: result.isVerified 
          ? 'BVN verification retry successful' 
          : 'BVN verification retry failed',
        data: {
          verificationId: result.verification.id,
          status: result.verification.status,
          isVerified: result.isVerified,
          extractedData: result.extractedData,
          providerUsed: result.providerUsed,
          responseTime: result.responseTime,
          verifiedAt: result.verification.verifiedAt,
          expiresAt: result.verification.expiresAt,
        },
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`BVN verification retry failed for user ${user.id}`, {
        userId: user.id,
        verificationId,
        error: errorMessage,
      });

      return {
        success: false,
        message: errorMessage,
        data: null,
      };
    }
  }
}
