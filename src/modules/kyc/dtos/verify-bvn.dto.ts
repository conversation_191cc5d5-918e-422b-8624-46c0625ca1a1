import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNotEmpty, Matches, IsOptional, IsDateString, IsPhoneNumber } from 'class-validator';

export class VerifyBvnDto {
  @ApiProperty({ 
    example: '***********',
    description: 'Bank Verification Number (11 digits)',
    minLength: 11,
    maxLength: 11
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^\d{11}$/, {
    message: 'BVN must be exactly 11 digits',
  })
  bvn: string;

  @ApiPropertyOptional({ 
    example: 'John',
    description: 'First name for verification (optional but recommended)'
  })
  @IsString()
  @IsOptional()
  firstName?: string;

  @ApiPropertyOptional({ 
    example: 'Doe',
    description: 'Last name for verification (optional but recommended)'
  })
  @IsString()
  @IsOptional()
  lastName?: string;

  @ApiPropertyOptional({ 
    example: '1990-01-15',
    description: 'Date of birth in YYYY-MM-DD format (optional but recommended)'
  })
  @IsDateString()
  @IsOptional()
  dateOfBirth?: string;

  @ApiPropertyOptional({ 
    example: '***********',
    description: 'Phone number for verification (optional but recommended)'
  })
  @IsString()
  @IsOptional()
  phoneNumber?: string;
}

export class VerifyBvnResponseDto {
  @ApiProperty({ 
    example: true,
    description: 'Whether the request was successful'
  })
  success: boolean;

  @ApiProperty({ 
    example: 'BVN verification successful',
    description: 'Response message'
  })
  message: string;

  @ApiProperty({ 
    description: 'Verification result data',
    nullable: true
  })
  data: {
    verificationId: string;
    status: string;
    isVerified: boolean;
    extractedData?: Record<string, any>;
    providerUsed: string;
    responseTime: number;
    verifiedAt?: Date;
    expiresAt?: Date;
  } | null;
}

export class BvnStatusResponseDto {
  @ApiProperty({ 
    example: true,
    description: 'Whether the request was successful'
  })
  success: boolean;

  @ApiProperty({ 
    example: 'BVN status retrieved successfully',
    description: 'Response message'
  })
  message: string;

  @ApiProperty({ 
    description: 'BVN verification status data',
    nullable: true
  })
  data: {
    hasVerifiedBvn: boolean;
    verification?: {
      id: string;
      status: string;
      verifiedAt?: Date;
      expiresAt?: Date;
      extractedData?: Record<string, any>;
    };
    lastAttempt?: Date;
  } | null;
}
