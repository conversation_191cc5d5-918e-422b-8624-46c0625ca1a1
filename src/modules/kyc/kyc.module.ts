import { Module } from '@nestjs/common';
import { KycVerificationRepositoryModule } from '../../infrastructure/database/repositories/kyc-verification/kyc-verification-repository.module';
import { DOCUMENT_STORAGE_PORT } from '../../core/ports/kyc/document-storage.port';
import { KycDocumentStorageAdapter } from '../../infrastructure/kyc/kyc-document-storage.adapter';
import { CountryKycConfigService } from '../../infrastructure/kyc/country-kyc-config.service';
import { KycProviderFactory } from '../../infrastructure/kyc/kyc-provider-factory';
import { KycProviderManager } from '../../infrastructure/kyc/kyc-provider-manager';
import { StorageModule } from '../../infrastructure/storage/storage.module';

// Use Cases
import { VerifyBvnUseCase } from '../../core/use-cases/kyc/verify-bvn.use-case';
import { VerifyNinUseCase } from '../../core/use-cases/kyc/verify-nin.use-case';
import { VerifyCacUseCase } from '../../core/use-cases/kyc/verify-cac.use-case';
import { VerifyTinUseCase } from '../../core/use-cases/kyc/verify-tin.use-case';
import { GetKycStatusUseCase } from '../../core/use-cases/kyc/get-kyc-status.use-case';

@Module({
  imports: [
    KycVerificationRepositoryModule,
    StorageModule,
  ],
  providers: [
    // Infrastructure Services
    CountryKycConfigService,
    KycProviderFactory,
    KycProviderManager,
    {
      provide: DOCUMENT_STORAGE_PORT,
      useClass: KycDocumentStorageAdapter,
    },
    
    // Use Cases
    VerifyBvnUseCase,
    VerifyNinUseCase,
    VerifyCacUseCase,
    VerifyTinUseCase,
    GetKycStatusUseCase,
  ],
  exports: [
    // Export use cases for controllers
    VerifyBvnUseCase,
    VerifyNinUseCase,
    VerifyCacUseCase,
    VerifyTinUseCase,
    GetKycStatusUseCase,
    
    // Export services for other modules
    CountryKycConfigService,
    KycProviderManager,
    DOCUMENT_STORAGE_PORT,
  ],
})
export class KycModule {}
