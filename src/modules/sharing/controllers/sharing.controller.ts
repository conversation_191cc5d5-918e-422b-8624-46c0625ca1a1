import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { SharingService } from '../services/sharing.service';
import { ShareAccountDto, ShareWalletDto } from '../dtos';
import { SharedAccessResponseDto } from '../dtos/shared-access-response.dto';
import { UserSharedResourcesResponseDto } from '../dtos/user-shared-resources-response.dto';
import { JwtAuthGuard, GetUser } from '../../../shared';
import { AuditInterceptor } from '../../../shared/interceptors/audit.interceptor';
import { AuditShare, AuditUnshare } from '../../../shared/decorators/audit.decorator';

@ApiTags('Sharing')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@UseInterceptors(AuditInterceptor)
@Controller('sharing')
export class SharingController {
  constructor(private readonly sharingService: SharingService) {}

  @Post('accounts/:accountId')
  @AuditShare('account', 'accountId', {
    description: 'Account shared with user',
    captureRequest: true,
    captureResponse: true,
  })
  @ApiOperation({ summary: 'Share account with another user' })
  @ApiResponse({
    status: 201,
    description: 'Account shared successfully',
    type: SharedAccessResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'Account or role not found' })
  @ApiResponse({
    status: 409,
    description: 'User already has access to this account',
  })
  async shareAccount(
    @Param('accountId') accountId: string,
    @Body() dto: ShareAccountDto,
    @GetUser('id') grantedBy: string,
  ): Promise<SharedAccessResponseDto> {
    return this.sharingService.shareAccount(accountId, dto, grantedBy);
  }

  @Post('wallets/:walletId')
  @AuditShare('wallet', 'walletId', {
    description: 'Wallet shared with user',
    captureRequest: true,
    captureResponse: true,
  })
  @ApiOperation({ summary: 'Share wallet with another user' })
  @ApiResponse({
    status: 201,
    description: 'Wallet shared successfully',
    type: SharedAccessResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'Wallet or role not found' })
  @ApiResponse({
    status: 409,
    description: 'User already has access to this wallet',
  })
  async shareWallet(
    @Param('walletId') walletId: string,
    @Body() dto: ShareWalletDto,
    @GetUser('id') grantedBy: string,
  ): Promise<SharedAccessResponseDto> {
    return this.sharingService.shareWallet(walletId, dto, grantedBy);
  }

  @Get('my-shared-resources')
  @ApiOperation({ summary: 'Get resources shared with current user' })
  @ApiResponse({
    status: 200,
    description: 'Shared resources retrieved successfully',
    type: UserSharedResourcesResponseDto,
  })
  async getMySharedResources(
    @GetUser('id') userId: string,
  ): Promise<UserSharedResourcesResponseDto> {
    return this.sharingService.getUserSharedResources(userId);
  }

  @Delete('accounts/:accountId/users/:userId')
  @AuditUnshare('account', 'accountId', {
    description: 'Account access revoked from user',
    captureRequest: true,
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Revoke user access to account' })
  @ApiResponse({ status: 204, description: 'Access revoked successfully' })
  @ApiResponse({ status: 404, description: 'Account or user access not found' })
  async revokeAccountAccess(
    @Param('accountId') accountId: string,
    @Param('userId') userId: string,
    @GetUser('id') revokedBy: string,
  ): Promise<void> {
    return this.sharingService.revokeAccountAccess(
      accountId,
      userId,
      revokedBy,
    );
  }

  @Delete('wallets/:walletId/users/:userId')
  @AuditUnshare('wallet', 'walletId', {
    description: 'Wallet access revoked from user',
    captureRequest: true,
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Revoke user access to wallet' })
  @ApiResponse({ status: 204, description: 'Access revoked successfully' })
  @ApiResponse({ status: 404, description: 'Wallet or user access not found' })
  async revokeWalletAccess(
    @Param('walletId') walletId: string,
    @Param('userId') userId: string,
    @GetUser('id') revokedBy: string,
  ): Promise<void> {
    return this.sharingService.revokeWalletAccess(walletId, userId, revokedBy);
  }
}
