import { Module } from '@nestjs/common';
import { Sharing<PERSON>ontroller } from './controllers/sharing.controller';
import { ExampleProtectedController } from './controllers/example-protected.controller';
import { SharingService } from './services/sharing.service';
import { PermissionGuard } from '../../shared/guards/permission.guard';

// Use Cases
import { ShareAccountUseCase } from '../../core/use-cases/sharing/share-account.use-case';
import { ShareWalletUseCase } from '../../core/use-cases/sharing/share-wallet.use-case';
import { GetUserSharedResourcesUseCase } from '../../core/use-cases/sharing/get-user-shared-resources.use-case';
import { RevokeAccessUseCase } from '../../core/use-cases/sharing/revoke-access.use-case';
import { CheckUserPermissionUseCase } from '../../core/use-cases/sharing/check-user-permission.use-case';

// Repository Modules
import { UserSharedAccessRepositoryModule } from '../../infrastructure/database/repositories/user-shared-access/user-shared-access-repository.module';
import { RoleRepositoryModule } from '../../infrastructure/database/repositories/role/role-repository.module';
import { SharedModule } from '../../shared/shared.module';

@Module({
  imports: [UserSharedAccessRepositoryModule, RoleRepositoryModule, SharedModule],
  controllers: [SharingController, ExampleProtectedController],
  providers: [
    // Services
    SharingService,

    // Guards
    PermissionGuard,

    // Use Cases
    ShareAccountUseCase,
    ShareWalletUseCase,
    GetUserSharedResourcesUseCase,
    RevokeAccessUseCase,
    CheckUserPermissionUseCase,
  ],
  exports: [SharingService, CheckUserPermissionUseCase, PermissionGuard],
})
export class SharingModule {}
