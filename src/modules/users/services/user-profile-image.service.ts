import { Injectable, Inject, Logger } from '@nestjs/common';
import { CloudinaryStorageService } from '../../../infrastructure/storage/cloudinary-storage.service';
import { STORAGE_SERVICE } from '../../../infrastructure/storage/storage.module';

export interface UploadProfileImageRequest {
  userId: string;
  imageFile: Buffer;
  fileName: string;
  mimeType: string;
}

export interface UploadProfileImageResult {
  success: boolean;
  imageUrl?: string;
  thumbnailUrl?: string;
  publicId?: string;
  errorMessage?: string;
}

@Injectable()
export class UserProfileImageService {
  private readonly logger = new Logger(UserProfileImageService.name);

  constructor(
    @Inject(STORAGE_SERVICE)
    private readonly storageService: CloudinaryStorageService,
  ) {}

  async uploadProfileImage(request: UploadProfileImageRequest): Promise<UploadProfileImageResult> {
    try {
      this.logger.log(`Uploading profile image for user ${request.userId}`, {
        userId: request.userId,
        fileName: request.fileName,
      });

      // Upload the original image
      const uploadResult = await this.storageService.uploadFile({
        file: request.imageFile,
        fileName: request.fileName,
        mimeType: request.mimeType,
        folder: 'user-profiles',
        userId: request.userId,
        metadata: {
          type: 'profile_image',
          uploaded_by: request.userId,
        },
        tags: ['profile', 'user-image'],
      });

      if (!uploadResult.success) {
        return {
          success: false,
          errorMessage: uploadResult.errorMessage,
        };
      }

      // Generate a thumbnail
      const thumbnailUrl = await this.storageService.generateThumbnail(
        uploadResult.publicId,
        150, // width
        150  // height
      );

      this.logger.log(`Profile image uploaded successfully for user ${request.userId}`, {
        userId: request.userId,
        publicId: uploadResult.publicId,
      });

      return {
        success: true,
        imageUrl: uploadResult.secureUrl,
        thumbnailUrl,
        publicId: uploadResult.publicId,
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Failed to upload profile image for user ${request.userId}`, {
        userId: request.userId,
        error: errorMessage,
      });

      return {
        success: false,
        errorMessage,
      };
    }
  }

  async deleteProfileImage(userId: string, publicId: string): Promise<boolean> {
    try {
      this.logger.log(`Deleting profile image for user ${userId}`, {
        userId,
        publicId,
      });

      const result = await this.storageService.deleteFile({ publicId });
      
      if (result.success) {
        this.logger.log(`Profile image deleted successfully for user ${userId}`, {
          userId,
          publicId,
        });
      }

      return result.success;

    } catch (error) {
      this.logger.error(`Failed to delete profile image for user ${userId}`, {
        userId,
        publicId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      return false;
    }
  }

  async getProfileImageUrl(publicId: string, options?: { 
    width?: number; 
    height?: number; 
    expiresIn?: number;
  }): Promise<string> {
    return this.storageService.getFileUrl(publicId, {
      secure: true,
      expiresIn: options?.expiresIn || 3600, // 1 hour default
      transformation: options?.width || options?.height ? {
        width: options.width,
        height: options.height,
        crop: 'fill',
        gravity: 'face',
      } : undefined,
    });
  }

  async generateUploadUrl(userId: string): Promise<{
    uploadUrl: string;
    uploadParams: Record<string, any>;
    expiresAt: Date;
  }> {
    return this.storageService.generateSignedUploadUrl({
      folder: 'user-profiles',
      userId,
      expiresIn: 3600, // 1 hour
      maxFileSize: 5 * 1024 * 1024, // 5MB
      allowedFormats: ['jpg', 'jpeg', 'png'],
      tags: ['profile', 'user-image'],
    });
  }
}
