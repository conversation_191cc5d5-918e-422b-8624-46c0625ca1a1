import {
  Body,
  Post,
  Patch,
  Delete,
  UseGuards,
  Controller,
  Get,
  UseInterceptors,
} from '@nestjs/common';
import { UsersService } from './services/users.service';
import { CreatePinDto, ChangePinDto } from './dtos/pin.dto';
import { GetUser, JwtAuthGuard } from '../../shared';
import { User } from '../../core/entities/user.entity';
import { AuditInterceptor } from '../../shared/interceptors/audit.interceptor';
import { Audit } from '../../shared/decorators/audit.decorator';
import { AuditAction, AuditEntity } from '../../core/constants/audit-actions';

@Controller('users')
@UseGuards(JwtAuthGuard)
@UseInterceptors(AuditInterceptor)
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Get('me')
  getMe(@GetUser() user: User) {
    return user;
  }

  @Post('pin')
  @Audit({
    action: AuditAction.CREATE,
    entity: AuditEntity.USER,
    entityIdParam: 'id', // Will be extracted from user
    description: 'PIN created',
    sensitiveFields: ['pin'],
  })
  async createPin(@GetUser() user: User, @Body() dto: CreatePinDto) {
    await this.usersService.createPin(user, dto.pin);
    return { success: true };
  }

  @Patch('pin')
  @Audit({
    action: AuditAction.UPDATE,
    entity: AuditEntity.USER,
    entityIdParam: 'id', // Will be extracted from user
    description: 'PIN changed',
    sensitiveFields: ['oldPin', 'newPin'],
  })
  async changePin(@GetUser() user: User, @Body() dto: ChangePinDto) {
    await this.usersService.changePin(user, dto.oldPin, dto.newPin);
    return { success: true };
  }

  @Delete('pin')
  @Audit({
    action: AuditAction.DELETE,
    entity: AuditEntity.USER,
    entityIdParam: 'id', // Will be extracted from user
    description: 'PIN reset/deleted',
  })
  async resetPin(@GetUser() user: User) {
    await this.usersService.resetPin(user);
    return { success: true };
  }
}
