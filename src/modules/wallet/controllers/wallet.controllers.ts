import {
  Body,
  Controller,
  Post,
  UseGuards,
  Get,
  Put,
  Delete,
  Param,
  HttpCode,
  HttpStatus,
  UseInterceptors,
} from '@nestjs/common';
import { WalletService } from '../services/wallet.service';
import { CreateWalletDto, UpdateWalletDto } from '../dtos';
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
} from '@nestjs/swagger';
import { JwtAuthGuard, GetUser } from 'src/shared';
import { AuditInterceptor } from '../../../shared/interceptors/audit.interceptor';
import {
  AuditCreate,
  AuditUpdate,
  AuditDelete,
  AuditRead
} from '../../../shared/decorators/audit.decorator';
import { AuditEntity } from '../../../core/constants/audit-actions';

@ApiTags('Wallets')
@ApiBearerAuth()
@Controller('wallets')
@UseGuards(JwtAuthGuard)
@UseInterceptors(AuditInterceptor)
export class WalletController {
  constructor(private readonly walletService: WalletService) {}

  @Post()
  @AuditCreate(AuditEntity.WALLET, 'id', {
    captureResponse: true,
    description: 'Wallet created via API',
    walletIdParam: 'id',
  })
  @ApiOperation({ summary: 'Create a new wallet' })
  @ApiResponse({ status: 201, description: 'Wallet created successfully' })
  async create(@Body() dto: CreateWalletDto, @GetUser('id') userId: string) {
    const wallet = await this.walletService.create(dto, userId);
    return wallet;
  }

  @Get()
  @ApiOperation({ summary: 'Get all wallets for the current user' })
  @ApiResponse({ status: 200, description: 'Wallets retrieved successfully' })
  async list(@GetUser('id') userId: string) {
    const wallets = await this.walletService.list(userId);
    return wallets;
  }

  @Get(':id')
  @AuditRead(AuditEntity.WALLET, 'id', {
    description: 'Wallet viewed via API',
    walletIdParam: 'id',
  })
  @ApiOperation({ summary: 'Get wallet by ID' })
  @ApiResponse({ status: 200, description: 'Wallet retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Wallet not found' })
  async findById(@Param('id') id: string) {
    const wallet = await this.walletService.findById(id);
    return wallet;
  }

  @Put(':id')
  @AuditUpdate(AuditEntity.WALLET, 'id', {
    captureRequest: true,
    captureResponse: true,
    description: 'Wallet updated via API',
    walletIdParam: 'id',
  })
  @ApiOperation({ summary: 'Update wallet' })
  @ApiResponse({ status: 200, description: 'Wallet updated successfully' })
  @ApiResponse({ status: 404, description: 'Wallet not found' })
  async update(@Param('id') id: string, @Body() dto: UpdateWalletDto) {
    const wallet = await this.walletService.update(id, dto);
    return wallet;
  }

  @Delete(':id')
  @AuditDelete(AuditEntity.WALLET, 'id', {
    captureRequest: true,
    description: 'Wallet deleted via API',
    walletIdParam: 'id',
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete wallet' })
  @ApiResponse({ status: 204, description: 'Wallet deleted successfully' })
  @ApiResponse({ status: 404, description: 'Wallet not found' })
  async delete(@Param('id') id: string) {
    return this.walletService.delete(id);
  }
}
