import { Module } from '@nestjs/common';
import { WalletRepositoryModule } from 'src/infrastructure/database/repositories/wallet/wallet-repository.module';
import { WalletController } from './controllers/wallet.controllers';
import { WalletService } from './services/wallet.service';

// Use Cases
import { CreateWalletUseCase } from 'src/core/use-cases/wallet/create-wallet.use-case';
import { GetWalletsUseCase } from 'src/core/use-cases/wallet/get-wallets.use-case';
import { GetWalletByIdUseCase } from 'src/core/use-cases/wallet/get-wallet-by-id.use-case';
import { UpdateWalletUseCase } from 'src/core/use-cases/wallet/update-wallet.use-case';
import { DeleteWalletUseCase } from 'src/core/use-cases/wallet/delete-wallet.use-case';
import { SharedModule } from '../../shared/shared.module';

@Module({
  imports: [WalletRepositoryModule, SharedModule],
  controllers: [WalletController],
  providers: [
    // Services
    WalletService,

    // Use Cases
    CreateWalletUseCase,
    GetWalletsUseCase,
    GetWalletByIdUseCase,
    UpdateWalletUseCase,
    DeleteWalletUseCase,
  ],
  exports: [WalletService],
})
export class WalletModule {}
