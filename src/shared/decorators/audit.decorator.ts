import { SetMetadata } from '@nestjs/common';
import { AuditAction, AuditEntity } from '../../core/constants/audit-actions';

export interface AuditOptions {
  action: AuditAction;
  entity: AuditEntity;
  entityIdParam?: string; // Parameter name that contains the entity ID
  accountIdParam?: string; // Parameter name that contains the account ID
  walletIdParam?: string; // Parameter name that contains the wallet ID
  captureRequest?: boolean; // Whether to capture request data
  captureResponse?: boolean; // Whether to capture response data
  sensitiveFields?: string[]; // Fields to redact from logs
  description?: string; // Custom description for the audit log
}

export const AUDIT_METADATA_KEY = 'audit';

/**
 * Decorator to mark methods for automatic audit logging
 */
export const Audit = (options: AuditOptions) => SetMetadata(AUDIT_METADATA_KEY, options);

/**
 * Convenience decorators for common audit actions
 */

export const AuditCreate = (
  entity: AuditEntity,
  entityIdParam = 'id',
  options?: Partial<AuditOptions>,
) =>
  Audit({
    action: AuditAction.CREATE,
    entity,
    entityIdParam,
    captureResponse: true,
    ...options,
  });

export const AuditUpdate = (
  entity: AuditEntity,
  entityIdParam = 'id',
  options?: Partial<AuditOptions>,
) =>
  Audit({
    action: AuditAction.UPDATE,
    entity,
    entityIdParam,
    captureRequest: true,
    captureResponse: true,
    ...options,
  });

export const AuditDelete = (
  entity: AuditEntity,
  entityIdParam = 'id',
  options?: Partial<AuditOptions>,
) =>
  Audit({
    action: AuditAction.DELETE,
    entity,
    entityIdParam,
    captureRequest: true,
    ...options,
  });

export const AuditRead = (
  entity: AuditEntity,
  entityIdParam = 'id',
  options?: Partial<AuditOptions>,
) =>
  Audit({
    action: AuditAction.READ,
    entity,
    entityIdParam,
    ...options,
  });

export const AuditShare = (
  resourceType: 'account' | 'wallet',
  resourceIdParam = 'id',
  options?: Partial<AuditOptions>,
) => {
  const accountIdParam = resourceType === 'account' ? resourceIdParam : undefined;
  const walletIdParam = resourceType === 'wallet' ? resourceIdParam : undefined;

  return Audit({
    action: AuditAction.SHARE,
    entity: AuditEntity.USER_SHARED_ACCESS,
    entityIdParam: resourceIdParam,
    accountIdParam,
    walletIdParam,
    captureRequest: true,
    ...options,
  });
};

export const AuditUnshare = (
  resourceType: 'account' | 'wallet',
  resourceIdParam = 'id',
  options?: Partial<AuditOptions>,
) => {
  const accountIdParam = resourceType === 'account' ? resourceIdParam : undefined;
  const walletIdParam = resourceType === 'wallet' ? resourceIdParam : undefined;

  return Audit({
    action: AuditAction.UNSHARE,
    entity: AuditEntity.USER_SHARED_ACCESS,
    entityIdParam: resourceIdParam,
    accountIdParam,
    walletIdParam,
    captureRequest: true,
    ...options,
  });
};
