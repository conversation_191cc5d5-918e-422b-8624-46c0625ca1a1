import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpStatus,
  UnauthorizedException,
  BadRequestException,
  HttpException,
} from '@nestjs/common';
import { ThrottlerException } from '@nestjs/throttler';
import { Response } from 'express';
import {
  UserAlreadyExistsException,
  InvalidCredentialsException,
  BvnAlreadyExistsException,
  InvalidPasswordResetTokenException,
  PasswordResetTokenExpiredException,
  PasswordResetTokenAlreadyUsedException,
  UserNotFoundException,
} from '../../core/exceptions/auth.exceptions';

@Catch()
export class DomainExceptionFilter implements ExceptionFilter {
  catch(exception: Error, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();

    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let message = exception.message;

    if (exception instanceof UserAlreadyExistsException) {
      status = HttpStatus.CONFLICT;
    } else if (exception instanceof BvnAlreadyExistsException) {
      status = HttpStatus.CONFLICT;
    } else if (exception instanceof InvalidCredentialsException) {
      status = HttpStatus.UNAUTHORIZED;
    } else if (exception instanceof UserNotFoundException) {
      status = HttpStatus.NOT_FOUND;
    } else if (exception instanceof InvalidPasswordResetTokenException) {
      status = HttpStatus.BAD_REQUEST;
    } else if (exception instanceof PasswordResetTokenExpiredException) {
      status = HttpStatus.BAD_REQUEST;
    } else if (exception instanceof PasswordResetTokenAlreadyUsedException) {
      status = HttpStatus.BAD_REQUEST;
    } else if (exception instanceof ThrottlerException) {
      status = HttpStatus.TOO_MANY_REQUESTS;
    } else if (exception instanceof UnauthorizedException) {
      status = HttpStatus.UNAUTHORIZED;
    } else if (exception instanceof BadRequestException) {
      status = HttpStatus.BAD_REQUEST;
      message = exception.message;

      const validationErrors = exception.getResponse() as HttpException;
      if (validationErrors && validationErrors.message) {
        message = validationErrors.message;
      }
    } else {
      console.log(exception);
    }

    response.status(status).json({
      statusCode: status,
      message,
      timestamp: new Date().toISOString(),
    });
  }
}
