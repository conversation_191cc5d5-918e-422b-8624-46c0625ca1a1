import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  CallHandler,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { AuditService } from '../../core/services/audit.service';
import { AUDIT_METADATA_KEY, AuditOptions } from '../decorators/audit.decorator';
import { SKIP_AUDIT_KEY } from '../decorators/skip-audit.decorator';
import { AuditAction } from '../../core/constants/audit-actions';

@Injectable()
export class AuditInterceptor implements NestInterceptor {
  constructor(
    private readonly reflector: Reflector,
    private readonly auditService: AuditService,
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    // Check if audit should be skipped
    const skipAudit = this.reflector.get<boolean>(
      SKIP_AUDIT_KEY,
      context.getHandler(),
    );

    if (skipAudit) {
      return next.handle();
    }

    const auditOptions = this.reflector.get<AuditOptions>(
      AUDIT_METADATA_KEY,
      context.getHandler(),
    );

    if (!auditOptions) {
      return next.handle();
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      return next.handle(); // No user, skip audit
    }

    const auditContext = this.auditService.extractContextFromRequest(request);
    const params = request.params;
    const body = request.body;
    const query = request.query;

    // Extract entity ID from parameters
    let entityId = this.extractParam(params, auditOptions.entityIdParam || 'id');
    const accountId = auditOptions.accountIdParam
      ? this.extractParam(params, auditOptions.accountIdParam)
      : undefined;
    const walletId = auditOptions.walletIdParam
      ? this.extractParam(params, auditOptions.walletIdParam)
      : undefined;

    // For some operations (like POST), entity ID might come from response
    if (!entityId && auditOptions.captureResponse) {
      // We'll extract it from response later
      entityId = 'pending'; // Temporary placeholder
    }

    if (!entityId) {
      return next.handle(); // No entity ID, skip audit
    }

    // Prepare request data for logging
    let requestData: Record<string, any> | undefined;
    if (auditOptions.captureRequest) {
      requestData = this.sanitizeData(
        { ...body, ...query },
        auditOptions.sensitiveFields,
      );
    }

    return next.handle().pipe(
      tap((response) => {
        // Extract entity ID from response if it was pending
        let finalEntityId = entityId;
        if (entityId === 'pending' && response && response.id) {
          finalEntityId = response.id;
        }

        // Log successful action
        this.logAuditEvent(
          user.id,
          auditOptions,
          finalEntityId,
          accountId,
          walletId,
          auditContext,
          requestData,
          response,
          true,
        );
      }),
      catchError((error) => {
        // Log failed action
        this.logAuditEvent(
          user.id,
          auditOptions,
          entityId,
          accountId,
          walletId,
          auditContext,
          requestData,
          undefined,
          false,
          error.message,
        );
        throw error;
      }),
    );
  }

  private async logAuditEvent(
    userId: string,
    options: AuditOptions,
    entityId: string,
    accountId?: string,
    walletId?: string,
    context?: any,
    requestData?: Record<string, any>,
    response?: any,
    success = true,
    errorMessage?: string,
  ): Promise<void> {
    try {
      let responseData: Record<string, any> | undefined;
      if (options.captureResponse && response && success) {
        responseData = this.sanitizeData(response, options.sensitiveFields);
      }

      const metadata: Record<string, any> = {
        success,
        description: options.description,
      };

      if (requestData) {
        metadata.requestData = requestData;
      }

      if (responseData) {
        metadata.responseData = responseData;
      }

      if (errorMessage) {
        metadata.error = errorMessage;
      }

      // Handle different action types
      switch (options.action) {
        case AuditAction.CREATE:
          await this.auditService.logCreate(
            userId,
            options.entity,
            entityId,
            responseData || requestData || {},
            { accountId, walletId, context, metadata },
          );
          break;

        case AuditAction.UPDATE:
          // For updates, we'd ideally want before/after values
          // This is a simplified version - in practice, you'd fetch the before state
          await this.auditService.logUpdate(
            userId,
            options.entity,
            entityId,
            {}, // beforeValues - would need to be fetched
            requestData || {},
            { accountId, walletId, context, metadata },
          );
          break;

        case AuditAction.DELETE:
          await this.auditService.logDelete(
            userId,
            options.entity,
            entityId,
            requestData || {},
            { accountId, walletId, context, metadata },
          );
          break;

        case AuditAction.READ:
          await this.auditService.logRead(
            userId,
            options.entity,
            entityId,
            { accountId, walletId, context, metadata },
          );
          break;

        case AuditAction.SHARE:
        case AuditAction.UNSHARE:
          if (requestData?.userId && requestData?.roleId) {
            const resourceType = accountId ? 'account' : 'wallet';
            const resourceId = accountId || walletId;
            if (resourceId) {
              await this.auditService.logSharing(
                userId,
                options.action,
                requestData.userId,
                resourceType,
                resourceId,
                requestData.roleId,
                context,
                metadata,
              );
            }
          }
          break;

        default:
          // Generic audit log
          await this.auditService.logCustom({
            userId,
            action: options.action,
            entity: options.entity,
            entityId,
            accountId,
            walletId,
            metadata,
            context,
          });
      }
    } catch (auditError) {
      // Don't let audit logging failures break the main operation
      console.error('Audit logging failed:', auditError);
    }
  }

  private extractParam(params: Record<string, any>, paramName: string): string | undefined {
    return params[paramName];
  }

  private sanitizeData(
    data: any,
    sensitiveFields: string[] = [],
  ): Record<string, any> {
    if (!data || typeof data !== 'object') {
      return {};
    }

    const defaultSensitiveFields = [
      'password',
      'passwordHash',
      'token',
      'secret',
      'key',
      'pin',
      'pinHash',
      'appPinHash',
      'refreshToken',
      'refreshTokenHash',
    ];

    const allSensitiveFields = [...defaultSensitiveFields, ...sensitiveFields];
    const sanitized = { ...data };

    for (const field of allSensitiveFields) {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]';
      }
    }

    return sanitized;
  }
}
