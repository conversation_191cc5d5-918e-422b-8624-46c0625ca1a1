import { Global, Module } from '@nestjs/common';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { AdminAuthGuard } from './guards/admin-auth.guard';
import { SuperAdminGuard } from './guards/super-admin.guard';
import { PermissionGuard } from './guards/permission.guard';
import { USER_AUTHENTICATOR } from '../core/ports/auth/user-authenticator.port';
import { ADMIN_AUTHENTICATOR } from '../core/ports/admin/admin-authenticator.port';
import { JwtAuthenticator } from '../infrastructure/auth/jwt-authenticator';
import { AdminJwtAuthenticator } from '../infrastructure/auth/admin-jwt-authenticator';
import { JwtService } from '@nestjs/jwt';
import { UserRepositoryModule } from '../infrastructure/database/repositories/user/user-repository.module';
import { AdminUserRepositoryModule } from '../infrastructure/database/repositories/admin-user/admin-user-repository.module';
import { UserSharedAccessRepositoryModule } from '../infrastructure/database/repositories/user-shared-access/user-shared-access-repository.module';
import { AuditRepositoryModule } from '../infrastructure/database/repositories/audit-log/audit-log-repository.module';
import { AuditInterceptor } from './interceptors/audit.interceptor';
import { AuditService } from '../core/services/audit.service';
import { CheckUserPermissionUseCase } from '../core/use-cases/sharing/check-user-permission.use-case';
@Global()
@Module({
  imports: [
    UserRepositoryModule,
    AdminUserRepositoryModule,
    UserSharedAccessRepositoryModule,
    AuditRepositoryModule,
  ],
  providers: [
    JwtAuthGuard,
    AdminAuthGuard,
    SuperAdminGuard,
    PermissionGuard,
    CheckUserPermissionUseCase,
    AuditInterceptor,
    AuditService,
    {
      provide: USER_AUTHENTICATOR,
      useClass: JwtAuthenticator,
    },
    {
      provide: ADMIN_AUTHENTICATOR,
      useClass: AdminJwtAuthenticator,
    },
    JwtService,
  ],
  exports: [
    JwtAuthGuard,
    AdminAuthGuard,
    SuperAdminGuard,
    PermissionGuard,
    CheckUserPermissionUseCase,
    AuditInterceptor,
    AuditService,
    USER_AUTHENTICATOR,
    ADMIN_AUTHENTICATOR,
  ],
})
export class SharedModule {}
