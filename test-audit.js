/**
 * Simple test script to verify audit logging functionality
 * This script tests the audit system without requiring a full database setup
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

// Test data
const testUser = {
  email: '<EMAIL>',
  password: 'TestPassword123!',
  firstName: 'Test',
  lastName: 'User'
};

const testAccount = {
  name: 'Test Account for Audit'
};

let authToken = '';
let userId = '';
let accountId = '';

async function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function makeRequest(method, endpoint, data = null, headers = {}) {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
        'x-device-id': 'test-device-123',
        ...headers
      }
    };

    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    return { 
      success: false, 
      error: error.response?.data || error.message,
      status: error.response?.status 
    };
  }
}

async function testUserRegistration() {
  console.log('\n🔍 Testing User Registration (should create audit log)...');
  
  const result = await makeRequest('POST', '/auth/register', testUser);
  
  if (result.success) {
    console.log('✅ User registration successful');
    console.log('📝 Response:', JSON.stringify(result.data, null, 2));
    
    if (result.data.user) {
      userId = result.data.user.id;
      console.log(`👤 User ID: ${userId}`);
    }
    
    return true;
  } else {
    console.log('❌ User registration failed:', result.error);
    return false;
  }
}

async function testUserLogin() {
  console.log('\n🔍 Testing User Login (should create audit log)...');
  
  const loginData = {
    email: testUser.email,
    password: testUser.password
  };
  
  const result = await makeRequest('POST', '/auth/login', loginData);
  
  if (result.success) {
    console.log('✅ User login successful');
    authToken = result.data.tokens.accessToken;
    console.log('🔑 Auth token obtained');
    return true;
  } else {
    console.log('❌ User login failed:', result.error);
    return false;
  }
}

async function testAccountCreation() {
  console.log('\n🔍 Testing Account Creation (should create audit log)...');
  
  const headers = {
    'Authorization': `Bearer ${authToken}`
  };
  
  const result = await makeRequest('POST', '/accounts', testAccount, headers);
  
  if (result.success) {
    console.log('✅ Account creation successful');
    console.log('📝 Response:', JSON.stringify(result.data, null, 2));
    
    if (result.data.id) {
      accountId = result.data.id;
      console.log(`🏦 Account ID: ${accountId}`);
    }
    
    return true;
  } else {
    console.log('❌ Account creation failed:', result.error);
    return false;
  }
}

async function testAccountUpdate() {
  console.log('\n🔍 Testing Account Update (should create audit log)...');
  
  if (!accountId) {
    console.log('❌ No account ID available for update test');
    return false;
  }
  
  const updateData = {
    name: 'Updated Test Account for Audit'
  };
  
  const headers = {
    'Authorization': `Bearer ${authToken}`
  };
  
  const result = await makeRequest('PUT', `/accounts/${accountId}`, updateData, headers);
  
  if (result.success) {
    console.log('✅ Account update successful');
    console.log('📝 Response:', JSON.stringify(result.data, null, 2));
    return true;
  } else {
    console.log('❌ Account update failed:', result.error);
    return false;
  }
}

async function testAuditLogRetrieval() {
  console.log('\n🔍 Testing Audit Log Retrieval...');
  
  const headers = {
    'Authorization': `Bearer ${authToken}`
  };
  
  // Wait a moment for audit logs to be created
  await sleep(1000);
  
  const result = await makeRequest('GET', '/audit?limit=10&sortOrder=desc', null, headers);
  
  if (result.success) {
    console.log('✅ Audit log retrieval successful');
    console.log('📊 Audit logs found:', result.data.total);
    
    if (result.data.auditLogs && result.data.auditLogs.length > 0) {
      console.log('\n📋 Recent audit logs:');
      result.data.auditLogs.forEach((log, index) => {
        console.log(`${index + 1}. ${log.action} ${log.entity} (${log.entityId}) at ${log.createdAt}`);
        if (log.description) {
          console.log(`   📝 ${log.description}`);
        }
      });
    }
    
    return true;
  } else {
    console.log('❌ Audit log retrieval failed:', result.error);
    return false;
  }
}

async function testAccountSpecificAuditLogs() {
  console.log('\n🔍 Testing Account-Specific Audit Logs...');
  
  if (!accountId) {
    console.log('❌ No account ID available for account audit test');
    return false;
  }
  
  const headers = {
    'Authorization': `Bearer ${authToken}`
  };
  
  const result = await makeRequest('GET', `/audit/accounts/${accountId}`, null, headers);
  
  if (result.success) {
    console.log('✅ Account-specific audit log retrieval successful');
    console.log('📊 Account audit logs found:', result.data.total);
    
    if (result.data.auditLogs && result.data.auditLogs.length > 0) {
      console.log('\n📋 Account audit logs:');
      result.data.auditLogs.forEach((log, index) => {
        console.log(`${index + 1}. ${log.action} ${log.entity} at ${log.createdAt}`);
      });
    }
    
    return true;
  } else {
    console.log('❌ Account-specific audit log retrieval failed:', result.error);
    return false;
  }
}

async function runAuditTests() {
  console.log('🚀 Starting Audit Logging System Tests');
  console.log('=====================================');
  
  let testsPassed = 0;
  let totalTests = 0;
  
  // Test 1: User Registration
  totalTests++;
  if (await testUserRegistration()) testsPassed++;
  
  // Test 2: User Login
  totalTests++;
  if (await testUserLogin()) testsPassed++;
  
  // Test 3: Account Creation
  totalTests++;
  if (await testAccountCreation()) testsPassed++;
  
  // Test 4: Account Update
  totalTests++;
  if (await testAccountUpdate()) testsPassed++;
  
  // Test 5: Audit Log Retrieval
  totalTests++;
  if (await testAuditLogRetrieval()) testsPassed++;
  
  // Test 6: Account-Specific Audit Logs
  totalTests++;
  if (await testAccountSpecificAuditLogs()) testsPassed++;
  
  console.log('\n📊 Test Results');
  console.log('================');
  console.log(`✅ Tests Passed: ${testsPassed}/${totalTests}`);
  console.log(`❌ Tests Failed: ${totalTests - testsPassed}/${totalTests}`);
  
  if (testsPassed === totalTests) {
    console.log('\n🎉 All audit logging tests passed! The system is working correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the error messages above.');
  }
}

// Check if server is running
async function checkServerHealth() {
  console.log('🔍 Checking if server is running...');
  
  const result = await makeRequest('GET', '/health');
  
  if (result.success) {
    console.log('✅ Server is running and healthy');
    return true;
  } else {
    console.log('❌ Server is not running or not healthy');
    console.log('💡 Please start the server with: npm run start:dev');
    return false;
  }
}

// Main execution
async function main() {
  const serverHealthy = await checkServerHealth();
  
  if (serverHealthy) {
    await runAuditTests();
  } else {
    console.log('\n🛑 Cannot run tests - server is not available');
    process.exit(1);
  }
}

// Run the tests
main().catch(console.error);
