import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { AdminModule } from '../../src/modules/admin/admin.module';
import { DatabaseModule } from '../../src/infrastructure/database/database.module';
import { ConfigModule } from '@nestjs/config';
import { JwtModule, JwtService } from '@nestjs/jwt';

describe('Admin Auth Integration Tests', () => {
  let app: INestApplication;
  let moduleFixture: TestingModule;
  let jwtService: JwtService;
  let validAdminToken: string;
  let expiredAdminToken: string;
  let invalidAdminToken: string;
  let superAdminToken: string;

  beforeAll(async () => {
    moduleFixture = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: '.env.test',
        }),
        JwtModule.register({
          secret: process.env.JWT_SECRET || 'test-secret',
          signOptions: { expiresIn: '1h' },
        }),
        DatabaseModule,
        AdminModule,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }));
    
    await app.init();

    jwtService = moduleFixture.get<JwtService>(JwtService);

    // Create test admin tokens
    validAdminToken = await jwtService.signAsync(
      { 
        sub: 'admin-user-id', 
        email: '<EMAIL>',
        isAdmin: true,
        isSuperAdmin: false
      },
      { secret: process.env.JWT_SECRET || 'test-secret' }
    );

    superAdminToken = await jwtService.signAsync(
      { 
        sub: 'super-admin-user-id', 
        email: '<EMAIL>',
        isAdmin: true,
        isSuperAdmin: true
      },
      { secret: process.env.JWT_SECRET || 'test-secret' }
    );

    expiredAdminToken = await jwtService.signAsync(
      { 
        sub: 'admin-user-id', 
        email: '<EMAIL>',
        isAdmin: true
      },
      { secret: process.env.JWT_SECRET || 'test-secret', expiresIn: '-1h' }
    );

    invalidAdminToken = 'invalid.admin.token';
  });

  afterAll(async () => {
    await app.close();
  });

  describe('POST /admin/auth/login', () => {
    const validAdminLoginData = {
      email: '<EMAIL>',
      password: 'AdminPassword123!',
    };

    it('should login admin successfully with valid credentials', () => {
      return request(app.getHttpServer())
        .post('/admin/auth/login')
        .send(validAdminLoginData)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('adminUser');
          expect(res.body).toHaveProperty('access_token');
          expect(res.body.adminUser).toHaveProperty('email', validAdminLoginData.email);
          expect(res.body.adminUser).not.toHaveProperty('password');
        });
    });

    it('should fail with invalid admin email', () => {
      return request(app.getHttpServer())
        .post('/admin/auth/login')
        .send({ ...validAdminLoginData, email: '<EMAIL>' })
        .expect(401);
    });

    it('should fail with invalid admin password', () => {
      return request(app.getHttpServer())
        .post('/admin/auth/login')
        .send({ ...validAdminLoginData, password: 'wrongpassword' })
        .expect(401);
    });

    it('should fail with missing credentials', () => {
      return request(app.getHttpServer())
        .post('/admin/auth/login')
        .send({ email: '<EMAIL>' })
        .expect(400);
    });

    it('should fail with invalid email format', () => {
      return request(app.getHttpServer())
        .post('/admin/auth/login')
        .send({ email: 'invalid-email', password: 'AdminPassword123!' })
        .expect(400);
    });

    it('should fail with short password', () => {
      return request(app.getHttpServer())
        .post('/admin/auth/login')
        .send({ email: '<EMAIL>', password: 'short' })
        .expect(400);
    });

    it('should fail for inactive admin account', () => {
      return request(app.getHttpServer())
        .post('/admin/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'AdminPassword123!',
        })
        .expect(403); // Assuming 403 for inactive account
    });
  });

  describe('Admin Protected Endpoints', () => {
    // These tests assume there are admin-protected endpoints
    // You'll need to replace '/admin/protected-endpoint' with actual admin endpoints

    it('should access admin endpoint with valid admin token', () => {
      return request(app.getHttpServer())
        .get('/admin/users') // Replace with actual admin endpoint
        .set('Authorization', `Bearer ${validAdminToken}`)
        .expect((res) => {
          // Should either succeed (200) or not be implemented yet (404)
          expect([200, 404]).toContain(res.status);
        });
    });

    it('should fail to access admin endpoint without token', () => {
      return request(app.getHttpServer())
        .get('/admin/users') // Replace with actual admin endpoint
        .expect((res) => {
          // Should either be unauthorized (401) or not found (404)
          expect([401, 404]).toContain(res.status);
        });
    });

    it('should fail to access admin endpoint with invalid token', () => {
      return request(app.getHttpServer())
        .get('/admin/users') // Replace with actual admin endpoint
        .set('Authorization', `Bearer ${invalidAdminToken}`)
        .expect((res) => {
          // Should either be unauthorized (401) or not found (404)
          expect([401, 404]).toContain(res.status);
        });
    });

    it('should fail to access admin endpoint with expired token', () => {
      return request(app.getHttpServer())
        .get('/admin/users') // Replace with actual admin endpoint
        .set('Authorization', `Bearer ${expiredAdminToken}`)
        .expect((res) => {
          // Should either be unauthorized (401) or not found (404)
          expect([401, 404]).toContain(res.status);
        });
    });

    it('should fail to access admin endpoint with regular user token', () => {
      // Create a regular user token
      const userToken = jwtService.sign(
        { sub: 'user-id', email: '<EMAIL>' },
        { secret: process.env.JWT_SECRET || 'test-secret' }
      );

      return request(app.getHttpServer())
        .get('/admin/users') // Replace with actual admin endpoint
        .set('Authorization', `Bearer ${userToken}`)
        .expect((res) => {
          // Should either be forbidden (403) or not found (404)
          expect([403, 404]).toContain(res.status);
        });
    });
  });

  describe('Super Admin Protected Endpoints', () => {
    // These tests assume there are super-admin-only endpoints

    it('should access super admin endpoint with super admin token', () => {
      return request(app.getHttpServer())
        .get('/admin/super-admin-only') // Replace with actual super admin endpoint
        .set('Authorization', `Bearer ${superAdminToken}`)
        .expect((res) => {
          // Should either succeed (200) or not be implemented yet (404)
          expect([200, 404]).toContain(res.status);
        });
    });

    it('should fail to access super admin endpoint with regular admin token', () => {
      return request(app.getHttpServer())
        .get('/admin/super-admin-only') // Replace with actual super admin endpoint
        .set('Authorization', `Bearer ${validAdminToken}`)
        .expect((res) => {
          // Should either be forbidden (403) or not found (404)
          expect([403, 404]).toContain(res.status);
        });
    });

    it('should fail to access super admin endpoint without token', () => {
      return request(app.getHttpServer())
        .get('/admin/super-admin-only') // Replace with actual super admin endpoint
        .expect((res) => {
          // Should either be unauthorized (401) or not found (404)
          expect([401, 404]).toContain(res.status);
        });
    });
  });

  describe('Admin Token Validation Edge Cases', () => {
    it('should handle malformed admin authorization header', () => {
      return request(app.getHttpServer())
        .get('/admin/users') // Replace with actual admin endpoint
        .set('Authorization', `InvalidFormat ${validAdminToken}`)
        .expect((res) => {
          expect([401, 404]).toContain(res.status);
        });
    });

    it('should handle empty admin authorization header', () => {
      return request(app.getHttpServer())
        .get('/admin/users') // Replace with actual admin endpoint
        .set('Authorization', '')
        .expect((res) => {
          expect([401, 404]).toContain(res.status);
        });
    });

    it('should handle admin token with special characters', () => {
      const specialCharToken = 'admin.token.with.special@chars#$%';
      return request(app.getHttpServer())
        .get('/admin/users') // Replace with actual admin endpoint
        .set('Authorization', `Bearer ${specialCharToken}`)
        .expect((res) => {
          expect([401, 404]).toContain(res.status);
        });
    });
  });

  describe('Admin Security Tests', () => {
    it('should handle multiple rapid admin login attempts', async () => {
      const promises = Array(3).fill(null).map(() =>
        request(app.getHttpServer())
          .post('/admin/auth/login')
          .send({
            email: '<EMAIL>',
            password: 'wrongpassword',
          })
      );

      const results = await Promise.all(promises);
      results.forEach(result => {
        expect([401, 429]).toContain(result.status); // Either unauthorized or rate limited
      });
    });

    it('should not leak admin information in error messages', () => {
      return request(app.getHttpServer())
        .post('/admin/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'AdminPassword123!',
        })
        .expect(401)
        .expect((res) => {
          // Error message should not reveal whether admin exists
          expect(res.body.message).not.toContain('admin not found');
          expect(res.body.message).not.toContain('user does not exist');
        });
    });
  });
});
