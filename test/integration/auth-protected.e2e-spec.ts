import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { AuthModule } from '../../src/modules/auth/auth.module';
import { DatabaseModule } from '../../src/infrastructure/database/database.module';
import { ConfigModule } from '@nestjs/config';
import { JwtModule, JwtService } from '@nestjs/jwt';

describe('Auth Protected Endpoints Integration Tests', () => {
  let app: INestApplication;
  let moduleFixture: TestingModule;
  let jwtService: JwtService;
  let validAccessToken: string;
  let expiredAccessToken: string;
  let invalidAccessToken: string;

  beforeAll(async () => {
    moduleFixture = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: '.env.test',
        }),
        JwtModule.register({
          secret: process.env.JWT_SECRET || 'test-secret',
          signOptions: { expiresIn: '1h' },
        }),
        DatabaseModule,
        AuthModule,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }));
    
    await app.init();

    jwtService = moduleFixture.get<JwtService>(JwtService);

    // Create test tokens
    validAccessToken = await jwtService.signAsync(
      { sub: 'test-user-id', email: '<EMAIL>' },
      { secret: process.env.JWT_SECRET || 'test-secret' }
    );

    expiredAccessToken = await jwtService.signAsync(
      { sub: 'test-user-id', email: '<EMAIL>' },
      { secret: process.env.JWT_SECRET || 'test-secret', expiresIn: '-1h' }
    );

    invalidAccessToken = 'invalid.jwt.token';
  });

  afterAll(async () => {
    await app.close();
  });

  describe('GET /auth/users (Protected)', () => {
    it('should return users with valid token', () => {
      return request(app.getHttpServer())
        .get('/auth/users')
        .set('Authorization', `Bearer ${validAccessToken}`)
        .expect(200);
    });

    it('should fail without authorization header', () => {
      return request(app.getHttpServer())
        .get('/auth/users')
        .expect(401)
        .expect((res) => {
          expect(res.body.message).toContain('No token provided');
        });
    });

    it('should fail with invalid token', () => {
      return request(app.getHttpServer())
        .get('/auth/users')
        .set('Authorization', `Bearer ${invalidAccessToken}`)
        .expect(401)
        .expect((res) => {
          expect(res.body.message).toContain('Invalid token');
        });
    });

    it('should fail with expired token', () => {
      return request(app.getHttpServer())
        .get('/auth/users')
        .set('Authorization', `Bearer ${expiredAccessToken}`)
        .expect(401)
        .expect((res) => {
          expect(res.body.message).toContain('Invalid token');
        });
    });

    it('should fail with malformed authorization header', () => {
      return request(app.getHttpServer())
        .get('/auth/users')
        .set('Authorization', `InvalidFormat ${validAccessToken}`)
        .expect(401)
        .expect((res) => {
          expect(res.body.message).toContain('No token provided');
        });
    });

    it('should fail with missing Bearer prefix', () => {
      return request(app.getHttpServer())
        .get('/auth/users')
        .set('Authorization', validAccessToken)
        .expect(401)
        .expect((res) => {
          expect(res.body.message).toContain('No token provided');
        });
    });
  });

  describe('POST /auth/logout (Protected)', () => {
    it('should logout successfully with valid token', () => {
      return request(app.getHttpServer())
        .post('/auth/logout')
        .set('Authorization', `Bearer ${validAccessToken}`)
        .set('x-device-id', 'device-123')
        .send({ logoutAllDevices: false })
        .expect(201);
    });

    it('should logout from all devices', () => {
      return request(app.getHttpServer())
        .post('/auth/logout')
        .set('Authorization', `Bearer ${validAccessToken}`)
        .set('x-device-id', 'device-123')
        .send({ logoutAllDevices: true })
        .expect(201);
    });

    it('should logout without device ID', () => {
      return request(app.getHttpServer())
        .post('/auth/logout')
        .set('Authorization', `Bearer ${validAccessToken}`)
        .send({ logoutAllDevices: false })
        .expect(201);
    });

    it('should fail without authorization header', () => {
      return request(app.getHttpServer())
        .post('/auth/logout')
        .send({ logoutAllDevices: false })
        .expect(401)
        .expect((res) => {
          expect(res.body.message).toContain('No token provided');
        });
    });

    it('should fail with invalid token', () => {
      return request(app.getHttpServer())
        .post('/auth/logout')
        .set('Authorization', `Bearer ${invalidAccessToken}`)
        .send({ logoutAllDevices: false })
        .expect(401)
        .expect((res) => {
          expect(res.body.message).toContain('Invalid token');
        });
    });

    it('should fail with expired token', () => {
      return request(app.getHttpServer())
        .post('/auth/logout')
        .set('Authorization', `Bearer ${expiredAccessToken}`)
        .send({ logoutAllDevices: false })
        .expect(401)
        .expect((res) => {
          expect(res.body.message).toContain('Invalid token');
        });
    });
  });

  describe('JWT Token Validation Edge Cases', () => {
    it('should handle empty authorization header', () => {
      return request(app.getHttpServer())
        .get('/auth/users')
        .set('Authorization', '')
        .expect(401);
    });

    it('should handle authorization header with only Bearer', () => {
      return request(app.getHttpServer())
        .get('/auth/users')
        .set('Authorization', 'Bearer')
        .expect(401);
    });

    it('should handle authorization header with extra spaces', () => {
      return request(app.getHttpServer())
        .get('/auth/users')
        .set('Authorization', `Bearer  ${validAccessToken}`)
        .expect(401); // Should fail due to extra spaces
    });

    it('should handle very long invalid token', () => {
      const longInvalidToken = 'a'.repeat(1000);
      return request(app.getHttpServer())
        .get('/auth/users')
        .set('Authorization', `Bearer ${longInvalidToken}`)
        .expect(401);
    });

    it('should handle token with special characters', () => {
      const specialCharToken = 'token.with.special@chars#$%';
      return request(app.getHttpServer())
        .get('/auth/users')
        .set('Authorization', `Bearer ${specialCharToken}`)
        .expect(401);
    });
  });

  describe('Security Headers and CORS', () => {
    it('should handle requests without user agent', () => {
      return request(app.getHttpServer())
        .post('/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'Password123!',
        })
        .expect(200);
    });

    it('should handle requests with custom headers', () => {
      return request(app.getHttpServer())
        .post('/auth/login')
        .set('X-Custom-Header', 'custom-value')
        .send({
          email: '<EMAIL>',
          password: 'Password123!',
        })
        .expect(200);
    });
  });

  describe('Rate Limiting and Security', () => {
    it('should handle multiple rapid requests', async () => {
      const promises = Array(5).fill(null).map(() =>
        request(app.getHttpServer())
          .post('/auth/login')
          .send({
            email: '<EMAIL>',
            password: 'wrongpassword',
          })
      );

      const results = await Promise.all(promises);
      results.forEach(result => {
        expect([401, 429]).toContain(result.status); // Either unauthorized or rate limited
      });
    });
  });
});
