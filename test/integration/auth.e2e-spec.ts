import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { AuthController } from '../../src/modules/auth/controllers/auth.controller';
import { AuthService } from '../../src/modules/auth/services/auth.service';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule } from '@nestjs/config';

describe('Auth Integration Tests', () => {
  let app: INestApplication;
  let moduleFixture: TestingModule;

  // Mock AuthService for testing
  const mockAuthService = {
    register: jest.fn(),
    login: jest.fn(),
    logout: jest.fn(),
    getUsers: jest.fn(),
    forgotPassword: jest.fn(),
    verifyResetCode: jest.fn(),
    resetPassword: jest.fn(),
  };

  beforeAll(async () => {
    moduleFixture = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: '.env.test',
        }),
        JwtModule.register({
          secret: process.env.JWT_SECRET || 'test-secret',
          signOptions: { expiresIn: '1h' },
        }),
      ],
      controllers: [AuthController],
      providers: [
        {
          provide: AuthService,
          useValue: mockAuthService,
        },
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }));

    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('POST /auth/register', () => {
    const validRegisterData = {
      email: '<EMAIL>',
      password: 'Password123!',
      firstName: 'John',
      lastName: 'Doe',
      phoneNumber: '**********',
      countryCode: '234',
      bvn: '12345678901',
    };

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should register a new user successfully', async () => {
      const expectedResult = {
        id: 'user-123',
        email: validRegisterData.email,
        firstName: validRegisterData.firstName,
        lastName: validRegisterData.lastName,
        phoneNumber: validRegisterData.phoneNumber,
        countryCode: validRegisterData.countryCode,
        bvn: validRegisterData.bvn,
      };

      mockAuthService.register.mockResolvedValue(expectedResult);

      return request(app.getHttpServer())
        .post('/auth/register')
        .send(validRegisterData)
        .expect(201)
        .expect((res) => {
          expect(res.body).toEqual(expectedResult);
          expect(mockAuthService.register).toHaveBeenCalledWith(
            validRegisterData,
            undefined
          );
        });
    });

    it('should register with device ID header', async () => {
      const userData = { ...validRegisterData, email: '<EMAIL>' };
      const expectedResult = {
        id: 'user-124',
        email: userData.email,
        firstName: userData.firstName,
        lastName: userData.lastName,
        phoneNumber: userData.phoneNumber,
        countryCode: userData.countryCode,
        bvn: userData.bvn,
      };

      mockAuthService.register.mockResolvedValue(expectedResult);

      return request(app.getHttpServer())
        .post('/auth/register')
        .set('x-device-id', 'device-123')
        .send(userData)
        .expect(201)
        .expect((res) => {
          expect(mockAuthService.register).toHaveBeenCalledWith(
            userData,
            'device-123'
          );
        });
    });

    it('should fail with invalid email', () => {
      return request(app.getHttpServer())
        .post('/auth/register')
        .send({ ...validRegisterData, email: 'invalid-email' })
        .expect(400);
    });

    it('should fail with weak password', () => {
      return request(app.getHttpServer())
        .post('/auth/register')
        .send({ ...validRegisterData, password: 'weak' })
        .expect(400);
    });

    it('should fail with invalid phone number', () => {
      return request(app.getHttpServer())
        .post('/auth/register')
        .send({ ...validRegisterData, phoneNumber: 'invalid' })
        .expect(400);
    });

    it('should fail with missing required fields', () => {
      return request(app.getHttpServer())
        .post('/auth/register')
        .send({ email: '<EMAIL>' })
        .expect(400);
    });
  });

  describe('POST /auth/login', () => {
    const loginData = {
      email: '<EMAIL>',
      password: 'Password123!',
    };

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should login successfully with valid credentials', async () => {
      const expectedResult = {
        user: {
          id: 'user-123',
          email: loginData.email,
          firstName: 'John',
          lastName: 'Doe',
          phoneNumber: '**********',
          countryCode: '234',
        },
        access_token: 'access-token-123',
        refresh_token: 'refresh-token-123',
      };

      mockAuthService.login.mockResolvedValue(expectedResult);

      return request(app.getHttpServer())
        .post('/auth/login')
        .send(loginData)
        .expect(200)
        .expect((res) => {
          expect(res.body).toEqual(expectedResult);
          expect(mockAuthService.login).toHaveBeenCalledWith(
            loginData,
            undefined
          );
        });
    });

    it('should login with device ID header', async () => {
      const expectedResult = {
        user: {
          id: 'user-123',
          email: loginData.email,
          firstName: 'John',
          lastName: 'Doe',
          phoneNumber: '**********',
          countryCode: '234',
        },
        access_token: 'access-token-123',
        refresh_token: 'refresh-token-123',
      };

      mockAuthService.login.mockResolvedValue(expectedResult);

      return request(app.getHttpServer())
        .post('/auth/login')
        .set('x-device-id', 'device-456')
        .send(loginData)
        .expect(200)
        .expect((res) => {
          expect(mockAuthService.login).toHaveBeenCalledWith(
            loginData,
            'device-456'
          );
        });
    });

    it('should fail with missing credentials', () => {
      return request(app.getHttpServer())
        .post('/auth/login')
        .send({ email: '<EMAIL>' })
        .expect(400);
    });

    it('should fail with invalid email format', () => {
      return request(app.getHttpServer())
        .post('/auth/login')
        .send({ email: 'invalid-email', password: 'Password123!' })
        .expect(400);
    });
  });

  describe('POST /auth/forgot-password', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should send password reset code for valid email', async () => {
      const expectedResult = {
        message: 'If an account with that email exists, a password reset code has been sent.',
      };

      mockAuthService.forgotPassword.mockResolvedValue(expectedResult);

      return request(app.getHttpServer())
        .post('/auth/forgot-password')
        .send({ email: '<EMAIL>' })
        .expect(200)
        .expect((res) => {
          expect(res.body).toEqual(expectedResult);
          expect(mockAuthService.forgotPassword).toHaveBeenCalledWith({
            email: '<EMAIL>',
          });
        });
    });

    it('should return same message for non-existent email (security)', async () => {
      const expectedResult = {
        message: 'If an account with that email exists, a password reset code has been sent.',
      };

      mockAuthService.forgotPassword.mockResolvedValue(expectedResult);

      return request(app.getHttpServer())
        .post('/auth/forgot-password')
        .send({ email: '<EMAIL>' })
        .expect(200)
        .expect((res) => {
          expect(res.body).toEqual(expectedResult);
        });
    });

    it('should fail with invalid email format', () => {
      return request(app.getHttpServer())
        .post('/auth/forgot-password')
        .send({ email: 'invalid-email' })
        .expect(400);
    });

    it('should fail with missing email', () => {
      return request(app.getHttpServer())
        .post('/auth/forgot-password')
        .send({})
        .expect(400);
    });
  });

  describe('POST /auth/verify-reset-code', () => {
    it('should fail with invalid code format', () => {
      return request(app.getHttpServer())
        .post('/auth/verify-reset-code')
        .send({ code: '12345' }) // 5 digits instead of 6
        .expect(400);
    });

    it('should fail with non-numeric code', () => {
      return request(app.getHttpServer())
        .post('/auth/verify-reset-code')
        .send({ code: 'abcdef' })
        .expect(400);
    });

    it('should fail with missing code', () => {
      return request(app.getHttpServer())
        .post('/auth/verify-reset-code')
        .send({})
        .expect(400);
    });

    it('should fail with non-existent code', () => {
      return request(app.getHttpServer())
        .post('/auth/verify-reset-code')
        .send({ code: '999999' })
        .expect(400); // Assuming 400 for invalid code
    });
  });

  describe('POST /auth/reset-password', () => {
    it('should fail with invalid verification token', () => {
      return request(app.getHttpServer())
        .post('/auth/reset-password')
        .send({
          verificationToken: 'invalid-token',
          newPassword: 'NewPassword123!',
        })
        .expect(400);
    });

    it('should fail with weak password', () => {
      return request(app.getHttpServer())
        .post('/auth/reset-password')
        .send({
          verificationToken: 'valid-token',
          newPassword: 'weak',
        })
        .expect(400);
    });

    it('should fail with missing fields', () => {
      return request(app.getHttpServer())
        .post('/auth/reset-password')
        .send({ verificationToken: 'valid-token' })
        .expect(400);
    });
  });
});
